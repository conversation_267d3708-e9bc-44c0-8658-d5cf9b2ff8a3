# 功能模块说明

## 模块概览

NHK日语学习平台包含7个主要功能模块，每个模块都有明确的职责和完整的功能实现。

## 1. 用户认证模块 🔐

### 功能描述
提供用户登录、认证和会话管理功能，支持多用户独立的学习进度管理。

### 主要功能
- **用户登录界面**：美观的登录表单，支持密码显示/隐藏
- **会话管理**：基于localStorage的用户状态持久化
- **权限控制**：区分普通用户和管理员权限
- **自动登录**：页面刷新后自动恢复登录状态

### 技术实现
- **前端组件**：LoginPage.tsx
- **认证工具**：auth.js
- **存储方式**：浏览器localStorage
- **安全机制**：客户端会话验证

### 完成程度
- ✅ **已完成**：基础登录功能
- ✅ **已完成**：用户状态管理
- ✅ **已完成**：权限验证
- ⚠️ **待优化**：密码加密存储
- ⚠️ **待扩展**：用户注册功能

### 测试账号
```
管理员: admin / admin123
学生1: student1 / student123
学生2: student2 / student123
老师: teacher / teacher123
```

## 2. 新闻阅读模块 📰

### 功能描述
核心学习模块，提供新闻文章的浏览、阅读和学习功能。

### 主要功能
- **文章列表展示**：支持分页、搜索、筛选
- **详细阅读器**：词汇标注、语法解析、翻译功能
- **多级别支持**：N5-N1难度级别筛选
- **媒体支持**：图片、视频、音频内容展示
- **学习工具**：词汇收藏、进度记录

### 核心组件
- **NewsReader.tsx**：文章列表管理
- **NewsCard.tsx**：文章卡片展示
- **ArticleReader.tsx**：详细阅读界面

### 数据来源
- SQLite数据库存储的文章内容
- RSS源抓取的实时新闻
- AI处理后的增强内容

### 完成程度
- ✅ **已完成**：文章列表展示
- ✅ **已完成**：搜索和筛选功能
- ✅ **已完成**：详细阅读器
- ✅ **已完成**：响应式设计
- ⚠️ **部分完成**：词汇标注功能
- ⚠️ **部分完成**：语法解析显示
- 🔄 **开发中**：音频播放功能

## 3. 学习统计模块 📊

### 功能描述
可视化展示用户的学习进度、成就和统计数据。

### 主要功能
- **学习概览**：连续学习天数、总学习时长
- **词汇统计**：已学词汇数量、掌握程度分布
- **进度追踪**：各级别学习进度可视化
- **成就系统**：学习里程碑和徽章展示
- **活动分析**：每日/每周学习活动统计

### 数据展示
- **统计卡片**：关键指标的数字化展示
- **进度条**：各级别词汇掌握进度
- **图表分析**：学习活动趋势图
- **成就展示**：学习成果和里程碑

### 完成程度
- ✅ **已完成**：基础统计展示
- ✅ **已完成**：进度可视化
- ✅ **已完成**：成就展示
- ⚠️ **模拟数据**：真实数据统计待完善
- 🔄 **开发中**：详细图表分析
- 📋 **计划中**：学习报告导出

## 4. 生词本模块 📚

### 功能描述
个人词汇管理系统，支持词汇收藏、复习和学习状态跟踪。

### 主要功能
- **词汇管理**：添加、删除、编辑词汇
- **学习状态**：新词汇、学习中、已掌握、困难词汇
- **搜索筛选**：按级别、状态、关键词筛选
- **复习系统**：基于遗忘曲线的复习提醒
- **统计分析**：词汇学习进度统计

### 数据结构
- **词汇信息**：单词、读音、释义、例句
- **学习记录**：复习次数、掌握程度、最后复习时间
- **关联数据**：出现文章、使用频率

### 完成程度
- ✅ **已完成**：词汇列表展示
- ✅ **已完成**：状态管理
- ✅ **已完成**：搜索筛选功能
- ⚠️ **部分完成**：词汇添加功能
- 🔄 **开发中**：复习算法
- 📋 **计划中**：词汇导入导出

## 5. AI助教模块 🤖

### 功能描述
智能学习助手，提供问答、对话练习和语法解析功能。

### 主要功能
- **智能问答**：回答日语学习相关问题
- **情景对话**：模拟真实场景的对话练习
- **语法解析**：分析句子结构和语法点
- **学习建议**：个性化的学习指导

### 对话场景
- 机场办理登机（N4级别）
- 餐厅点餐（N5级别）
- 商务会议（N2级别）
- 医院就诊（N3级别）
- 购物消费（N4级别）
- 求职面试（N2级别）

### 完成程度
- ✅ **已完成**：基础对话界面
- ✅ **已完成**：情景对话模板
- ✅ **已完成**：语法解析工具
- ⚠️ **模拟实现**：AI回复逻辑
- 🔄 **开发中**：真实AI集成
- 📋 **计划中**：语音对话功能

## 6. RSS源管理模块 📡

### 功能描述
管理新闻数据源，配置RSS源和字段映射规则。

### 主要功能
- **RSS源配置**：添加、编辑、删除RSS源
- **字段映射**：灵活配置数据提取规则
- **实时测试**：验证RSS源有效性
- **抓取控制**：设置抓取数量和频率
- **AI处理配置**：控制AI功能开关

### 配置选项
- **基础信息**：名称、URL、描述、分类
- **抓取设置**：最大文章数、抓取间隔
- **字段映射**：标题、链接、内容、图片等字段的提取规则
- **AI处理**：翻译、词汇提取、语法分析等功能开关

### 完成程度
- ✅ **已完成**：RSS源管理界面
- ✅ **已完成**：字段映射配置
- ✅ **已完成**：实时测试功能
- ✅ **已完成**：后端API支持
- ⚠️ **部分完成**：复杂映射规则
- 📋 **计划中**：批量导入导出

## 7. 抓取管理模块 ⚡

### 功能描述
控制新闻抓取任务，监控系统状态和数据处理进度。

### 主要功能
- **抓取控制**：启动、停止、监控抓取任务
- **进度监控**：实时显示抓取进度和日志
- **AI处理队列**：管理AI内容处理任务
- **数据库管理**：统计、导出、清理数据
- **系统监控**：服务状态、性能指标

### 监控面板
- **概览统计**：文章数量、词汇数量、存储使用
- **抓取控制**：任务启停、进度显示
- **AI处理**：队列状态、处理统计
- **历史记录**：抓取会话历史
- **数据管理**：导出、清理、备份

### 完成程度
- ✅ **已完成**：抓取任务控制
- ✅ **已完成**：实时进度监控
- ✅ **已完成**：数据库统计
- ✅ **已完成**：AI队列管理
- ✅ **已完成**：历史记录查看
- ⚠️ **部分完成**：性能优化
- 📋 **计划中**：自动化调度

## 跨模块功能

### 响应式设计
- **桌面端**：完整功能展示，多列布局
- **移动端**：简化界面，底部导航
- **自适应**：根据屏幕尺寸调整布局

### 数据同步
- **实时更新**：前后端数据实时同步
- **状态管理**：全局状态和本地状态管理
- **缓存机制**：提高数据访问性能

### 错误处理
- **网络错误**：API连接失败处理
- **数据错误**：数据格式验证和错误提示
- **用户错误**：操作错误的友好提示

## 模块间依赖关系

```
用户认证模块 (基础)
    ↓
新闻阅读模块 ← → 生词本模块
    ↓              ↓
学习统计模块 ← → AI助教模块
    ↓
RSS源管理模块 ← → 抓取管理模块
```

### 数据流向
1. **RSS源管理** → 配置数据源
2. **抓取管理** → 获取新闻内容
3. **新闻阅读** → 用户学习交互
4. **生词本** → 收藏学习内容
5. **学习统计** → 展示学习成果
6. **AI助教** → 提供学习辅助

## 开发优先级

### 高优先级（核心功能）
1. ✅ 用户认证和会话管理
2. ✅ 新闻内容展示和阅读
3. ✅ RSS源管理和抓取控制
4. ✅ 基础数据管理

### 中优先级（增强功能）
1. ⚠️ 词汇标注和语法解析
2. ⚠️ AI助教智能回复
3. ⚠️ 学习进度真实统计
4. 🔄 复习算法和提醒系统

### 低优先级（扩展功能）
1. 📋 语音播放和识别
2. 📋 社交学习功能
3. 📋 学习报告和分析
4. 📋 多语言界面支持

这种模块化的设计确保了系统的可维护性和可扩展性，每个模块都可以独立开发和测试，同时保持良好的用户体验。