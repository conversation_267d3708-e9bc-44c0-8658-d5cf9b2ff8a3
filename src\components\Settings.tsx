
'use client';

import React, { useState, useEffect, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import authManager from '@/utils/auth';
import {
  User,
  Bell,
  LogOut,
  BrainCircuit,
  RefreshCw,
  Loader,
  CheckCircle,
  AlertCircle,
  Eye,
  Save,
  Key,
  PlusCircle,
  Trash2,
  GripVertical,
  MessageSquare,
  Edit3,
  X,
  Check,
  Clock,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';
import {
  setSystemDefaultModelAction,
  syncAIModelsAction,
  setAITemperatureAction,
  addApiKeyAction,
  deleteApiKeyAction,
  toggleApiKeyStatusAction,
  updateApiKeyOrderAction,
  updateApiKeyIntervalsAction,
  getScrapingDelayConfigAction,
  updateScrapingDelayConfigAction,
  startAIProcessorAction,
  stopAIProcessorAction,
  getAIProcessorStatusAction
} from '@/app/actions';

interface AIModel {
  id: number;
  model_id: string;
  display_name: string | null;
  description: string | null;
  model_type: string | null;
}

interface ApiKey {
    id: number;
    name: string;
    api_key: string;
    provider: string;
    priority: number;
    is_active: boolean;
    quota_reset_interval_minutes?: number;
    min_usage_interval_seconds?: number;
}

interface AIPrompt {
    id: number;
    name: string;
    type: string;
    category: string;
    title: string;
    description: string | null;
    content: string;
    variables: string | null;
    is_active: boolean;
    version: string;
    created_at: Date;
    updated_at: Date;
}

interface ScrapingDelayConfig {
    rssSourceDelay: number;
    articleDelay: number;
    requestMinDelay: number;
    requestMaxDelay: number;
}

interface AIProcessorStatus {
  isRunning: boolean;
  sessionId: string | null;
  keyStats: {
    total: number;
    active: number;
    failed: number;
  };
}

interface SettingsProps {
  initialModels: AIModel[];
  initialDefaultModelId: string | null;
  initialAITemperature: number;
  initialApiKeys: ApiKey[];
  initialAIPrompts: AIPrompt[];
}

const Settings: React.FC<SettingsProps> = ({ initialModels, initialDefaultModelId, initialAITemperature, initialApiKeys, initialAIPrompts }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [models, setModels] = useState<AIModel[]>(initialModels);
  const [defaultModelId, setDefaultModelId] = useState<string | null>(initialDefaultModelId);
  const [aiTemperature, setAITemperature] = useState<number>(initialAITemperature);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>(initialApiKeys);
  const [aiPrompts, setAIPrompts] = useState<AIPrompt[]>(initialAIPrompts);

  const [syncMessage, setSyncMessage] = useState<string | null>(null);
  const [syncError, setSyncError] = useState<string | null>(null);
  
  const [saveStatus, setSaveStatus] = useState<{ type: 'success' | 'error', message: string } | null>(null);

  // AI提示词编辑状态
  const [editingPrompt, setEditingPrompt] = useState<AIPrompt | null>(null);
  const [promptEditContent, setPromptEditContent] = useState<string>('');
  const [promptSaving, setPromptSaving] = useState<boolean>(false);

  const [currentUser, setCurrentUser] = useState<any>(null);

  const [newApiKey, setNewApiKey] = useState({
    name: '',
    key: '',
    quotaResetInterval: 60,
    minUsageInterval: 1
  });

  // API Key间隔编辑状态
  const [editingKeyId, setEditingKeyId] = useState<number | null>(null);
  const [editingIntervals, setEditingIntervals] = useState<{
    quotaResetInterval: number;
    minUsageInterval: number;
  }>({ quotaResetInterval: 60, minUsageInterval: 1 });

  // 抓取延迟配置状态
  const [delayConfig, setDelayConfig] = useState<ScrapingDelayConfig>({
    rssSourceDelay: 5000,
    articleDelay: 2000,
    requestMinDelay: 2000,
    requestMaxDelay: 5000
  });
  const [delayConfigLoading, setDelayConfigLoading] = useState(false);

  // AI处理器状态
  const [aiProcessorStatus, setAIProcessorStatus] = useState<AIProcessorStatus>({
    isRunning: false,
    sessionId: null,
    keyStats: { total: 0, active: 0, failed: 0 }
  });
  const [aiProcessorLoading, setAIProcessorLoading] = useState(false);

  useEffect(() => {
    setCurrentUser(authManager.getCurrentUser());
  }, []);

  useEffect(() => {
    setModels(initialModels);
    setDefaultModelId(initialDefaultModelId);
    setAITemperature(initialAITemperature);
    setApiKeys(initialApiKeys);
  }, [initialModels, initialDefaultModelId, initialAITemperature, initialApiKeys]);

  // 加载延迟配置
  useEffect(() => {
    const loadDelayConfig = async () => {
      try {
        const result = await getScrapingDelayConfigAction();
        if (result.success && result.data) {
          setDelayConfig(result.data);
        }
      } catch (error) {
        console.error('加载延迟配置失败:', error);
      }
    };
    loadDelayConfig();
  }, []);

  // 加载AI处理器状态
  useEffect(() => {
    const loadAIProcessorStatus = async () => {
      try {
        const result = await getAIProcessorStatusAction();
        if (result.success && result.data) {
          setAIProcessorStatus(result.data);
        }
      } catch (error) {
        console.error('加载AI处理器状态失败:', error);
      }
    };
    loadAIProcessorStatus();

    // 每10秒刷新一次状态
    const interval = setInterval(loadAIProcessorStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const isDirty = defaultModelId !== initialDefaultModelId ||
                  aiTemperature !== initialAITemperature;

  const handleSyncModels = () => {
    startTransition(async () => {
      setSyncError(null);
      setSyncMessage(null);
      const result = await syncAIModelsAction();
      if (result.success) {
        setSyncMessage(result.message || '');
        router.refresh(); 
      } else {
        setSyncError(`${result.error}\n\n详情: ${result.details}`);
      }
    });
  };
  
  const handleSaveSettings = () => {
    startTransition(async () => {
      setSaveStatus(null);
      const promises = [];

      if (defaultModelId !== initialDefaultModelId) {
        promises.push(setSystemDefaultModelAction(defaultModelId!));
      }
      if (aiTemperature !== initialAITemperature) {
        promises.push(setAITemperatureAction(aiTemperature));
      }

      const results = await Promise.all(promises);
      const firstError = results.find(r => !r.success);

      if (firstError) {
        setSaveStatus({ type: 'error', message: firstError.error || '保存失败' });
      } else {
        setSaveStatus({ type: 'success', message: '设置已成功保存！' });
        setTimeout(() => setSaveStatus(null), 3000);
        router.refresh();
      }
    });
  };

  const handleAddApiKey = (e: React.FormEvent) => {
    e.preventDefault();
    startTransition(async () => {
        if (!newApiKey.name || !newApiKey.key) return;
        const result = await addApiKeyAction(
            newApiKey.name,
            newApiKey.key,
            newApiKey.quotaResetInterval,
            newApiKey.minUsageInterval
        );
        if (result.success) {
            setNewApiKey({ name: '', key: '', quotaResetInterval: 60, minUsageInterval: 1 });
            router.refresh();
        } else {
            alert(`Error: ${result.error}`);
        }
    });
  };

  const handleDeleteApiKey = (id: number) => {
    if (confirm('确定要删除这个 API Key 吗？')) {
        startTransition(async () => {
            const result = await deleteApiKeyAction(id);
            if (result.success) {
                router.refresh();
            } else {
                alert(`Error: ${result.error}`);
            }
        });
    }
  };

  const handleToggleApiKeyStatus = (id: number) => {
    startTransition(async () => {
        const result = await toggleApiKeyStatusAction(id);
        if (result.success) {
            setSaveStatus({ type: 'success', message: result.message || '操作成功' });
            router.refresh();
        } else {
            setSaveStatus({ type: 'error', message: result.error || '操作失败' });
        }
        setTimeout(() => setSaveStatus(null), 3000);
    });
  };

  const handleEditKeyIntervals = (key: ApiKey) => {
    setEditingKeyId(key.id);
    setEditingIntervals({
      quotaResetInterval: key.quota_reset_interval_minutes || 60,
      minUsageInterval: key.min_usage_interval_seconds || 1
    });
  };

  const handleSaveKeyIntervals = () => {
    if (editingKeyId === null) return;

    startTransition(async () => {
      const result = await updateApiKeyIntervalsAction(
        editingKeyId,
        editingIntervals.quotaResetInterval,
        editingIntervals.minUsageInterval
      );
      if (result.success) {
        setEditingKeyId(null);
        setSaveStatus({ type: 'success', message: result.message || '更新成功' });
        router.refresh();
      } else {
        setSaveStatus({ type: 'error', message: result.error || '更新失败' });
      }
      setTimeout(() => setSaveStatus(null), 3000);
    });
  };

  const handleCancelEditIntervals = () => {
    setEditingKeyId(null);
  };




  const handleLogout = () => {
    authManager.logout();
    router.push('/login');
  };

  // 延迟配置处理函数
  const handleSaveDelayConfig = () => {
    startTransition(async () => {
      setDelayConfigLoading(true);
      try {
        const result = await updateScrapingDelayConfigAction(delayConfig);
        if (result.success) {
          setSaveStatus({ type: 'success', message: result.message || '保存成功' });
        } else {
          setSaveStatus({ type: 'error', message: result.error || '保存失败' });
        }
      } catch (error: any) {
        setSaveStatus({ type: 'error', message: '保存失败: ' + error.message });
      } finally {
        setDelayConfigLoading(false);
      }
    });
  };

  const handleDelayConfigChange = (field: keyof ScrapingDelayConfig, value: number) => {
    setDelayConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const [settings, setSettings] = useState({
    notifications: {
      dailyReminder: true,
      weeklyReport: true,
    },
  });

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...(prev[category as keyof typeof prev] as any),
        [key]: value
      }
    }));
  };

  // 提示词管理函数
  const handleEditPrompt = (prompt: AIPrompt) => {
    setEditingPrompt(prompt);
    setPromptEditContent(prompt.content);
  };

  const handleSavePrompt = async () => {
    if (!editingPrompt) return;

    setPromptSaving(true);
    try {
      const response = await fetch(`/api/ai/prompts/${editingPrompt.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: promptEditContent,
        }),
      });

      if (response.ok) {
        const updatedPrompt = await response.json();
        setAIPrompts(prev => prev.map(p => p.id === editingPrompt.id ? updatedPrompt : p));
        setEditingPrompt(null);
        setSaveStatus({ type: 'success', message: '提示词已成功保存！' });
        setTimeout(() => setSaveStatus(null), 3000);
      } else {
        throw new Error('保存失败');
      }
    } catch (error) {
      setSaveStatus({ type: 'error', message: '保存提示词失败，请稍后重试' });
    } finally {
      setPromptSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingPrompt(null);
    setPromptEditContent('');
  };

  const formatLoginTime = (loginTime: string) => {
    if (!loginTime) return '未知';
    const date = new Date(loginTime);
    return date.toLocaleString('zh-CN');
  };

  // AI处理器控制函数
  const handleStartAIProcessor = async () => {
    setAIProcessorLoading(true);
    try {
      const result = await startAIProcessorAction();
      if (result.success) {
        setSaveStatus({ type: 'success', message: result.message || 'AI处理器已启动' });
        // 刷新状态
        const statusResult = await getAIProcessorStatusAction();
        if (statusResult.success && statusResult.data) {
          setAIProcessorStatus(statusResult.data);
        }
      } else {
        setSaveStatus({ type: 'error', message: result.error || '启动失败' });
      }
    } catch (error) {
      setSaveStatus({ type: 'error', message: '启动AI处理器失败' });
    } finally {
      setAIProcessorLoading(false);
      setTimeout(() => setSaveStatus(null), 3000);
    }
  };

  const handleStopAIProcessor = async () => {
    setAIProcessorLoading(true);
    try {
      const result = await stopAIProcessorAction();
      if (result.success) {
        setSaveStatus({ type: 'success', message: result.message || 'AI处理器已停止' });
        // 刷新状态
        const statusResult = await getAIProcessorStatusAction();
        if (statusResult.success && statusResult.data) {
          setAIProcessorStatus(statusResult.data);
        }
      } else {
        setSaveStatus({ type: 'error', message: result.error || '停止失败' });
      }
    } catch (error) {
      setSaveStatus({ type: 'error', message: '停止AI处理器失败' });
    } finally {
      setAIProcessorLoading(false);
      setTimeout(() => setSaveStatus(null), 3000);
    }
  };

  return (
    <div className="space-y-6 pb-20 md:pb-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">设置</h2>
          <p className="text-gray-600 mt-1">个性化你的学习体验和系统配置</p>
        </div>
        <button
          onClick={handleSaveSettings}
          disabled={isPending || !isDirty}
          className="flex items-center justify-center space-x-2 py-2 px-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
        >
          {isPending ? <Loader className="h-5 w-5 animate-spin" /> : <Save className="h-5 w-5" />}
          <span>保存更改</span>
        </button>
      </div>
      
      {saveStatus && (
        <div className={`flex items-center space-x-2 p-3 rounded-lg text-sm ${saveStatus.type === 'success' ? 'bg-green-50 border border-green-200 text-green-800' : 'bg-red-50 border border-red-200 text-red-800'}`}>
          {saveStatus.type === 'success' ? <CheckCircle className="h-5 w-5" /> : <AlertCircle className="h-5 w-5" />}
          <span>{saveStatus.message}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <User className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-900">用户信息</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-4 bg-indigo-50 rounded-lg">
              <div className="h-12 w-12 bg-indigo-500 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">{currentUser?.displayName || '未知用户'}</div>
                <div className="text-sm text-gray-600">用户名: {currentUser?.username || '未知'}</div>
                <div className="text-xs text-gray-500">登录时间: {formatLoginTime(currentUser?.loginTime)}</div>
              </div>
            </div>

            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-2 py-2 px-4 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span>退出登录</span>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Bell className="h-5 w-5 text-yellow-600" />
            <h3 className="text-lg font-semibold text-gray-900">通知设置</h3>
          </div>
          
          <div className="space-y-4">
            {Object.entries({
              dailyReminder: '每日学习提醒',
              weeklyReport: '周报总结',
            }).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-gray-700">{label}</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications[key as keyof typeof settings.notifications]}
                    onChange={(e) => updateSetting('notifications', key, e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>
      


        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <BrainCircuit className="h-5 w-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI设置</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label htmlFor="aiTemperature" className="block text-sm font-medium text-gray-700">AI温度参数</label>
              <p className="text-xs text-gray-500 mb-2">
                控制AI回答的创造性和随机性。较低的值（如0.2）使输出更确定和一致，较高的值（如1.0）使输出更有创造性和多样性。范围：0.0-2.0，推荐值：0.7
              </p>
              <input
                id="aiTemperature"
                type="number"
                value={aiTemperature}
                onChange={(e) => setAITemperature(Number(e.target.value))}
                className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
                min="0"
                max="2"
                step="0.1"
              />
              <div className="mt-2 text-xs text-gray-600">
                当前值: {aiTemperature}
                {aiTemperature <= 0.3 && " (保守模式)"}
                {aiTemperature > 0.3 && aiTemperature <= 0.7 && " (平衡模式)"}
                {aiTemperature > 0.7 && aiTemperature <= 1.2 && " (创造模式)"}
                {aiTemperature > 1.2 && " (高创造模式)"}
              </div>
            </div>
          </div>
        </div>

        {/* AI处理器控制 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <BrainCircuit className="h-5 w-5 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900">AI分析处理器</h3>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${aiProcessorStatus.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className={`text-sm font-medium ${aiProcessorStatus.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                {aiProcessorStatus.isRunning ? '运行中' : '已停止'}
              </span>
            </div>
          </div>

          <p className="text-sm text-gray-600 mb-4">
            控制AI文章分析处理器的启动和停止。处理器运行时会自动分析待处理的文章，提取词汇和语法点。
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">API Key 状态</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">总数:</span>
                  <span className="font-medium">{aiProcessorStatus.keyStats.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">可用:</span>
                  <span className="font-medium text-green-600">{aiProcessorStatus.keyStats.active}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">失败:</span>
                  <span className="font-medium text-red-600">{aiProcessorStatus.keyStats.failed}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">会话信息</h4>
              <div className="text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">会话ID:</span>
                  <span className="font-mono text-xs">{aiProcessorStatus.sessionId || '无'}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleStartAIProcessor}
              disabled={aiProcessorLoading || aiProcessorStatus.isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {aiProcessorLoading ? (
                <Loader className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              <span>启动处理器</span>
            </button>

            <button
              onClick={handleStopAIProcessor}
              disabled={aiProcessorLoading || !aiProcessorStatus.isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {aiProcessorLoading ? (
                <Loader className="h-4 w-4 animate-spin" />
              ) : (
                <X className="h-4 w-4" />
              )}
              <span>停止处理器</span>
            </button>
          </div>
        </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-cyan-600" />
            <h3 className="text-lg font-semibold text-gray-900">API Key 管理</h3>
          </div>

        </div>
        <p className="text-sm text-gray-600 mb-4">
          在这里管理您的 Google Gemini API Key。系统将按列表顺序使用这些Key，并在一个Key额度耗尽时自动切换到下一个。如果此处没有配置Key，系统将使用.env文件中的备用Key。
        </p>

        <div className="space-y-3 mb-6">
            {isPending && apiKeys.length === 0 && <Loader className="animate-spin" />}
            {apiKeys.map((key, index) => (
                <div key={key.id} className={`border rounded-lg ${key.is_active ? 'border-gray-200 bg-white' : 'border-gray-300 bg-gray-50'}`}>
                    <div className="flex items-center space-x-3 p-3">
                        <div className="flex-1">
                            <div className="flex items-center space-x-2">
                                <p className={`font-medium ${key.is_active ? 'text-gray-800' : 'text-gray-500'}`}>{key.name}</p>
                                <span className={`px-2 py-1 text-xs rounded-full ${key.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                                    {key.is_active ? '启用' : '禁用'}
                                </span>
                            </div>
                            <p className={`text-xs font-mono ${key.is_active ? 'text-gray-500' : 'text-gray-400'}`}>****-****-{key.api_key.slice(-4)}</p>
                            <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                                <span>配额重置: {key.quota_reset_interval_minutes || 60}分钟</span>
                                <span>使用间隔: {key.min_usage_interval_seconds || 1}秒</span>
                            </div>
                        </div>
                        <span className="text-xs text-gray-500">优先级: {index + 1}</span>
                        <button
                            onClick={() => handleEditKeyIntervals(key)}
                            disabled={isPending}
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg disabled:opacity-50"
                            title="编辑间隔配置"
                        >
                            <Clock className="h-4 w-4"/>
                        </button>
                        <button
                            onClick={() => handleToggleApiKeyStatus(key.id)}
                            disabled={isPending}
                            className={`p-2 rounded-lg disabled:opacity-50 ${key.is_active ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-400 hover:text-green-600 hover:bg-green-50'}`}
                            title={key.is_active ? '点击禁用' : '点击启用'}
                        >
                            {key.is_active ? <ToggleRight className="h-4 w-4"/> : <ToggleLeft className="h-4 w-4"/>}
                        </button>
                        <button onClick={() => handleDeleteApiKey(key.id)} disabled={isPending} className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg disabled:opacity-50">
                            <Trash2 className="h-4 w-4"/>
                        </button>
                    </div>

                    {editingKeyId === key.id && (
                        <div className="border-t border-gray-200 p-3 bg-gray-50">
                            <h5 className="font-medium text-gray-800 mb-3">编辑间隔配置</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        配额重置间隔 (分钟)
                                    </label>
                                    <input
                                        type="number"
                                        min="1"
                                        max="1440"
                                        value={editingIntervals.quotaResetInterval}
                                        onChange={(e) => setEditingIntervals(prev => ({
                                            ...prev,
                                            quotaResetInterval: parseInt(e.target.value) || 60
                                        }))}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                    <p className="text-xs text-gray-500 mt-1">超限后等待多久重新使用此Key</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        最小使用间隔 (秒)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        max="60"
                                        value={editingIntervals.minUsageInterval}
                                        onChange={(e) => setEditingIntervals(prev => ({
                                            ...prev,
                                            minUsageInterval: parseInt(e.target.value) || 1
                                        }))}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                    <p className="text-xs text-gray-500 mt-1">两次使用之间的最小间隔</p>
                                </div>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <button
                                    onClick={handleCancelEditIntervals}
                                    disabled={isPending}
                                    className="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg disabled:opacity-50"
                                >
                                    取消
                                </button>
                                <button
                                    onClick={handleSaveKeyIntervals}
                                    disabled={isPending}
                                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {isPending ? <Loader className="h-4 w-4 animate-spin" /> : '保存'}
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            ))}
        </div>
        
        <form onSubmit={handleAddApiKey} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-800 mb-3">添加新的 API Key</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <input
                    type="text"
                    placeholder="Key 名称 (例如, '备用Key')"
                    value={newApiKey.name}
                    onChange={(e) => setNewApiKey(prev => ({...prev, name: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    required
                />
                <input
                    type="password"
                    placeholder="API Key (例如, AIza...)"
                    value={newApiKey.key}
                    onChange={(e) => setNewApiKey(prev => ({...prev, key: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    required
                />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        配额重置间隔 (分钟)
                    </label>
                    <input
                        type="number"
                        min="1"
                        max="1440"
                        value={newApiKey.quotaResetInterval}
                        onChange={(e) => setNewApiKey(prev => ({
                            ...prev,
                            quotaResetInterval: parseInt(e.target.value) || 60
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">超限后等待多久重新使用此Key</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        最小使用间隔 (秒)
                    </label>
                    <input
                        type="number"
                        min="0"
                        max="60"
                        value={newApiKey.minUsageInterval}
                        onChange={(e) => setNewApiKey(prev => ({
                            ...prev,
                            minUsageInterval: parseInt(e.target.value) || 1
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">两次使用之间的最小间隔</p>
                </div>
            </div>
            <div className="flex justify-end">
                <button type="submit" disabled={isPending || !newApiKey.name || !newApiKey.key} className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400">
                    <PlusCircle className="h-4 w-4" />
                    <span>添加 Key</span>
                </button>
            </div>
        </form>
      </div>

      {/* 抓取延迟配置 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-gray-900">抓取延迟配置</h3>
          </div>
          <button
            onClick={handleSaveDelayConfig}
            disabled={delayConfigLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-400"
          >
            {delayConfigLoading ? <Loader className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            <span>保存配置</span>
          </button>
        </div>
        <p className="text-sm text-gray-600 mb-6">
          配置抓取过程中的延迟时间，避免对目标网站造成过大压力。合理的延迟设置有助于提高抓取成功率。
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              RSS源间延迟 (毫秒)
            </label>
            <input
              type="number"
              min="1000"
              max="30000"
              step="1000"
              value={delayConfig.rssSourceDelay}
              onChange={(e) => handleDelayConfigChange('rssSourceDelay', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="text-xs text-gray-500 mt-1">处理完一个RSS源后等待的时间</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文章间延迟 (毫秒)
            </label>
            <input
              type="number"
              min="500"
              max="10000"
              step="500"
              value={delayConfig.articleDelay}
              onChange={(e) => handleDelayConfigChange('articleDelay', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="text-xs text-gray-500 mt-1">处理完一篇文章后等待的时间（跳过的文章不延迟）</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最小请求延迟 (毫秒)
            </label>
            <input
              type="number"
              min="1000"
              max="10000"
              step="500"
              value={delayConfig.requestMinDelay}
              onChange={(e) => handleDelayConfigChange('requestMinDelay', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="text-xs text-gray-500 mt-1">智能延迟的最小时间</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最大请求延迟 (毫秒)
            </label>
            <input
              type="number"
              min="2000"
              max="20000"
              step="1000"
              value={delayConfig.requestMaxDelay}
              onChange={(e) => handleDelayConfigChange('requestMaxDelay', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="text-xs text-gray-500 mt-1">智能延迟的最大时间</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="text-sm font-medium text-yellow-800 mb-2">延迟策略说明</h4>
          <ul className="text-xs text-yellow-700 space-y-1">
            <li>• <strong>RSS源间延迟</strong>：处理完一个RSS源的所有文章后，等待指定时间再处理下一个RSS源</li>
            <li>• <strong>文章间延迟</strong>：成功处理一篇文章后等待的时间，如果文章已存在则跳过延迟</li>
            <li>• <strong>智能延迟</strong>：每个HTTP请求之间的随机延迟，在最小和最大值之间随机选择</li>
            <li>• <strong>自适应调整</strong>：系统会根据服务器响应自动调整延迟策略</li>
          </ul>
        </div>
      </div>

      {/* AI提示词管理 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI提示词管理</h3>
          </div>
        </div>
        <p className="text-sm text-gray-600 mb-4">
          管理系统中AI处理使用的提示词。您可以编辑这些提示词来精确控制AI的行为和输出格式。
        </p>

        <div className="space-y-4">
          {Object.entries(
            aiPrompts.reduce((acc, prompt) => {
              if (!acc[prompt.category]) acc[prompt.category] = [];
              acc[prompt.category].push(prompt);
              return acc;
            }, {} as Record<string, AIPrompt[]>)
          ).map(([category, prompts]) => (
            <div key={category} className="border border-gray-200 rounded-lg">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h4 className="font-medium text-gray-900">
                  {category === 'article_analysis' && '文章分析'}
                  {category === 'grammar_analysis' && '语法分析'}
                  {category === 'tutor_chat' && 'AI助教'}
                  {!['article_analysis', 'grammar_analysis', 'tutor_chat'].includes(category) && category}
                </h4>
              </div>
              <div className="p-4 space-y-3">
                {prompts.map((prompt) => (
                  <div key={prompt.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h5 className="font-medium text-gray-900">{prompt.title}</h5>
                        <p className="text-sm text-gray-500">
                          类型: {prompt.type} | 版本: {prompt.version}
                        </p>
                        {prompt.description && (
                          <p className="text-sm text-gray-600 mt-1">{prompt.description}</p>
                        )}
                      </div>
                      <button
                        onClick={() => handleEditPrompt(prompt)}
                        className="flex items-center space-x-1 px-3 py-1.5 text-sm text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200"
                      >
                        <Edit3 className="h-4 w-4" />
                        <span>编辑</span>
                      </button>
                    </div>
                    <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded border max-h-20 overflow-y-auto">
                      {prompt.content.substring(0, 200)}
                      {prompt.content.length > 200 && '...'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
                <BrainCircuit className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">AI模型管理</h3>
            </div>
            <button onClick={handleSyncModels} disabled={isPending || !!syncError} className="flex items-center space-x-2 px-3 py-1.5 text-sm text-indigo-700 bg-indigo-100 rounded-lg hover:bg-indigo-200 disabled:bg-gray-200 disabled:text-gray-500 disabled:cursor-not-allowed">
                {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                <span>同步模型列表</span>
            </button>
        </div>
        <div className="text-sm text-gray-600 mb-4">
            管理系统可用的AI模型，并设置默认用于文章分析的模型。
        </div>

        {syncMessage && (
            <div className="mb-4 flex items-center space-x-2 p-3 bg-green-50 border border-green-200 text-green-800 rounded-lg text-sm">
                <CheckCircle className="h-5 w-5" />
                <span>{syncMessage}</span>
            </div>
        )}
        {syncError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-800 rounded-lg text-sm">
                <div className="flex items-start space-x-2">
                    <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                    <pre className="whitespace-pre-wrap font-mono">{syncError}</pre>
                </div>
            </div>
        )}

        <div className="space-y-3">
            {models.map(model => (
                <div key={model.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                        <p className="font-medium text-gray-800">{model.display_name}</p>
                        <p className="text-xs text-gray-500">{model.model_id}</p>
                    </div>
                    {defaultModelId === model.model_id ? (
                        <span className="px-3 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">当前默认</span>
                    ) : (
                        <button onClick={() => setDefaultModelId(model.model_id)} className="px-3 py-1 text-xs font-medium text-indigo-700 bg-indigo-100 rounded-full hover:bg-indigo-200">
                            设为默认
                        </button>
                    )}
                </div>
            ))}
             {models.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                    <p>没有可用的AI模型。请点击“同步模型列表”获取。</p>
                </div>
            )}
        </div>
      </div>

      {/* 提示词编辑模态框 */}
      {editingPrompt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">编辑提示词</h3>
                <p className="text-sm text-gray-600">{editingPrompt.title}</p>
              </div>
              <button
                onClick={handleCancelEdit}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  提示词内容
                </label>
                <textarea
                  value={promptEditContent}
                  onChange={(e) => setPromptEditContent(e.target.value)}
                  className="w-full h-96 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                  placeholder="输入提示词内容..."
                />
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">提示词信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">名称:</span> {editingPrompt.name}
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">类型:</span> {editingPrompt.type}
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">分类:</span> {editingPrompt.category}
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">版本:</span> {editingPrompt.version}
                  </div>
                </div>
                {editingPrompt.variables && editingPrompt.variables !== '{}' && (
                  <div className="mt-3">
                    <span className="font-medium text-gray-700">变量:</span>
                    <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-x-auto">
                      {JSON.stringify(JSON.parse(editingPrompt.variables), null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                disabled={promptSaving}
              >
                取消
              </button>
              <button
                onClick={handleSavePrompt}
                disabled={promptSaving || promptEditContent === editingPrompt.content}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {promptSaving ? (
                  <Loader className="h-4 w-4 animate-spin" />
                ) : (
                  <Check className="h-4 w-4" />
                )}
                <span>{promptSaving ? '保存中...' : '保存'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
