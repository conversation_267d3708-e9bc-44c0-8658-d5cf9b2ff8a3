/**
 * 服务器端Ruby标签处理工具
 * 用于修复AI返回的不完整ruby标签格式
 */

/**
 * 修复不完整的Ruby标签格式
 * 将孤立的<rt>标签转换为完整的<ruby><rt></rt></ruby>格式
 * @param text 包含可能不完整ruby标签的文本
 * @returns 修复后的文本
 */
export function fixRubyTags(text: string): string {
  if (!text) return '';
  
  // 正则表达式匹配模式：汉字+<rt>读音</rt>
  // 这个模式匹配没有被<ruby>包装的汉字和<rt>标签组合
  const incompleteRubyPattern = /([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>/g;
  
  // 将不完整的格式转换为完整的ruby格式
  let fixedText = text.replace(incompleteRubyPattern, '<ruby>$1<rt>$2</rt></ruby>');
  
  // 处理连续的汉字+<rt>组合，确保每个汉字都有自己的<ruby>标签
  // 例如：株式<rt>かぶしき</rt>保有<rt>ほゆう</rt> -> <ruby>株式<rt>かぶしき</rt></ruby><ruby>保有<rt>ほゆう</rt></ruby>
  const consecutivePattern = /([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>\s*([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>/g;
  
  // 多次应用修复，直到没有更多的连续模式
  let previousText = '';
  let iterations = 0;
  const maxIterations = 10; // 防止无限循环
  
  while (fixedText !== previousText && iterations < maxIterations) {
    previousText = fixedText;
    fixedText = fixedText.replace(consecutivePattern, '<ruby>$1<rt>$2</rt></ruby><ruby>$3<rt>$4</rt></ruby>');
    iterations++;
  }
  
  return fixedText;
}

/**
 * 清理Ruby标签，只保留主要文本
 * @param text 包含<ruby>标签的文本
 * @returns 清理后的纯文本
 */
export function stripRubyTags(text: string): string {
  if (!text) return '';
  
  // 移除ruby标签，保留主要文本
  return text
    .replace(/<ruby>(.*?)<rt>.*?<\/rt><\/ruby>/g, '$1')
    .replace(/<\/?[^>]+(>|$)/g, ''); // 移除其他HTML标签
}

/**
 * 检查文本是否包含Ruby标签
 * @param text 要检查的文本
 * @returns 是否包含Ruby标签
 */
export function hasRubyTags(text: string): boolean {
  return Boolean(text && (text.includes('<ruby>') || text.includes('<rt>')));
}

/**
 * 修复分析结果中的所有ruby标签
 * @param analysisResult AI分析结果对象
 * @returns 修复后的分析结果
 */
export function fixAnalysisResultRubyTags(analysisResult: any): any {
  if (!analysisResult) return analysisResult;
  
  const result = { ...analysisResult };
  
  // 修复标题和内容的furigana
  if (result.titleWithFurigana) {
    result.titleWithFurigana = fixRubyTags(result.titleWithFurigana);
  }
  
  if (result.contentWithFurigana) {
    result.contentWithFurigana = fixRubyTags(result.contentWithFurigana);
  }
  
  if (result.subtitleWithFurigana) {
    result.subtitleWithFurigana = fixRubyTags(result.subtitleWithFurigana);
  }
  
  // 修复词汇中的ruby标签
  if (result.vocabulary && Array.isArray(result.vocabulary)) {
    result.vocabulary = result.vocabulary.map((vocab: any) => ({
      ...vocab,
      examples: vocab.examples ? vocab.examples.map((example: string) => fixRubyTags(example)) : [],
      commonCollocations: vocab.commonCollocations ? vocab.commonCollocations.map((collocation: any) => ({
        ...collocation,
        collocation: fixRubyTags(collocation.collocation || ''),
        meaning: fixRubyTags(collocation.meaning || ''),
        example: fixRubyTags(collocation.example || '')
      })) : []
    }));
  }
  
  // 修复语法中的ruby标签
  if (result.grammar && Array.isArray(result.grammar)) {
    result.grammar = result.grammar.map((gram: any) => ({
      ...gram,
      pattern: fixRubyTags(gram.pattern || ''),
      examples: gram.examples ? gram.examples.map((example: string) => fixRubyTags(example)) : [],
      similarGrammar: gram.similarGrammar ? gram.similarGrammar.map((similar: any) => ({
        ...similar,
        pattern: fixRubyTags(similar.pattern || '')
      })) : []
    }));
  }
  
  return result;
}

/**
 * 验证ruby标签格式是否正确
 * @param text 要验证的文本
 * @returns 验证结果和错误信息
 */
export function validateRubyTags(text: string): { isValid: boolean; errors: string[] } {
  if (!text) return { isValid: true, errors: [] };
  
  const errors: string[] = [];
  
  // 检查是否有孤立的<rt>标签
  const orphanRtPattern = /(?<!<ruby>[^<]*)<rt>[^<]*<\/rt>(?![^<]*<\/ruby>)/g;
  const orphanRtMatches = text.match(orphanRtPattern);
  if (orphanRtMatches) {
    errors.push(`发现 ${orphanRtMatches.length} 个孤立的<rt>标签`);
  }
  
  // 检查是否有未闭合的<ruby>标签
  const openRubyCount = (text.match(/<ruby>/g) || []).length;
  const closeRubyCount = (text.match(/<\/ruby>/g) || []).length;
  if (openRubyCount !== closeRubyCount) {
    errors.push(`<ruby>标签不匹配：开始标签 ${openRubyCount} 个，结束标签 ${closeRubyCount} 个`);
  }
  
  // 检查是否有未闭合的<rt>标签
  const openRtCount = (text.match(/<rt>/g) || []).length;
  const closeRtCount = (text.match(/<\/rt>/g) || []).length;
  if (openRtCount !== closeRtCount) {
    errors.push(`<rt>标签不匹配：开始标签 ${openRtCount} 个，结束标签 ${closeRtCount} 个`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
