// 用户认证工具类
class AuthManager {
  constructor() {
    this.currentUser = null;
    if (typeof window !== 'undefined') {
      this.loadUserFromStorage();
    }
  }

  // 从localStorage加载用户信息
  loadUserFromStorage() {
    try {
      const userData = localStorage.getItem('currentUser');
      if (userData) {
        this.currentUser = JSON.parse(userData);
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      this.logout();
    }
  }

  // 检查用户是否已登录
  isLoggedIn() {
    return this.currentUser !== null;
  }

  // 获取当前用户信息
  getCurrentUser() {
    return this.currentUser;
  }

  // 用户登录
  login(username, displayName) {
    const userData = {
      username,
      displayName,
      loginTime: new Date().toISOString()
    };
    
    this.currentUser = userData;
    if (typeof window !== 'undefined') {
      localStorage.setItem('currentUser', JSON.stringify(userData));
    }
    
    return userData;
  }

  // 用户登出
  logout() {
    this.currentUser = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('currentUser');
    }
  }

  // 获取用户显示名称
  getUserDisplayName() {
    return this.currentUser ? this.currentUser.displayName : '未登录';
  }

  // 获取用户名
  getUsername() {
    return this.currentUser ? this.currentUser.username : null;
  }

  // 获取登录时间
  getLoginTime() {
    return this.currentUser ? this.currentUser.loginTime : null;
  }

  // 检查是否为管理员
  isAdmin() {
    return this.currentUser && this.currentUser.username === 'admin';
  }

  // 更新用户信息
  updateUserInfo(updates) {
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...updates };
      if (typeof window !== 'undefined') {
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
      }
    }
  }
}

// 创建全局实例
const authManager = new AuthManager();

export default authManager;
