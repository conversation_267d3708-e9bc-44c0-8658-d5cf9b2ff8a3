# NHK 日语学习平台 - 开发者入门指南

本文档旨在帮助新加入的开发者快速理解本项目的技术架构、核心工作流程，并顺利地搭建本地开发环境。

## 1. 核心技术栈概览

本项目是一个基于 **Next.js** 的现代化全栈 Web 应用。与传统的前后端分离架构不同，它将所有后端逻辑（数据抓取、数据库交互、AI 分析等）都统一集成在 Next.js 的服务器环境中。这种模式极大地简化了开发和部署。

- **前端 (Frontend)**
  - **框架**: [**Next.js 15 (App Router)**](https://nextjs.org/) - 我们使用 React 19 RC 和 Next.js 最新的 App Router 架构，所有组件默认为服务器组件（Server Components），实现了高效的服务端渲染 (SSR)。
  - **UI**: [**React**](https://react.dev/) - 用于构建用户界面。
  - **样式**: [**Tailwind CSS**](https://tailwindcss.com/) - 一个实用优先的 CSS 框架，用于快速构建美观的界面。
  - **状态管理**: 主要通过 **URL State** 和 **Server Actions** 管理状态，对于复杂的客户端交互，则使用 React 的内置 Hooks (`useState`, `useEffect`)。

- **后端 (Backend)**
  - **核心**: [**Next.js Server Actions**](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) - 这是本项目的后端核心。所有后端逻辑，如启动抓取、保存设置、与数据库交互等，都封装在 `src/app/actions.ts` 文件中的异步函数里。这些函数可以直接在客户端组件中调用，Next.js 会自动处理网络请求和数据序列化。
  - **数据库 ORM**: [**Prisma**](https://www.prisma.io/) - 一个现代化的数据库工具集，用于以类型安全的方式操作数据库。我们的数据库结构定义在 `prisma/schema.prisma` 文件中。
  - **数据库**: [**SQLite**](https://www.sqlite.org/index.html) - 一个轻量级的、基于文件的 SQL 数据库，非常适合本地开发和中小型应用。

- **AI 集成**
  - **框架**: [**Google Genkit**](https://firebase.google.com/docs/genkit) - 一个用于构建、部署和监控 AI 驱动功能的开源框架。我们用它来定义和执行 AI 工作流（Flows），例如文章分析、语法检查和 AI 助教聊天。
  - **模型**: 主要使用 **Google Gemini** 系列模型，并通过数据库进行动态配置和管理。

- **开发工具**
  - **语言**: [**TypeScript**](https://www.typescriptlang.org/) - 为 JavaScript 提供了静态类型检查，保证了代码的健壮性。
  - **包管理器**: [**npm**](https://www.npmjs.com/)
  - **代码检查**: [**ESLint**](https://eslint.org/)

## 2. 项目架构详解

```mermaid
graph TD
    subgraph Browser
        A[React Components (UI)]
    end

    subgraph Next.js Server
        B[Server Actions (src/app/actions.ts)]
        C[Scraper (src/lib/server/scraper.ts)]
        D[Database Manager (src/lib/server/database.ts)]
        E[AI Flows (src/ai/flows/*)]
    end

    subgraph Database
        F[SQLite (data/nhk_news_new.db)]
        G[Prisma Schema (prisma/schema.prisma)]
    end

    subgraph External Services
        H[NHK News Website]
        I[Google AI (Gemini)]
    end

    A -- Calls --> B
    B -- Uses --> C
    B -- Uses --> D
    B -- Uses --> E
    C -- Scrapes --> H
    C -- Saves data via --> D
    D -- Reads/Writes --> F
    E -- Calls --> I
    F -- Is defined by --> G
```

1.  **用户界面 (Browser)**: 用户在浏览器中与 React 组件进行交互。
2.  **调用后端**: 当用户执行一个操作（如点击“开始抓取”）时，React 组件会直接调用一个从 `src/app/actions.ts` 中导入的 `async` 函数（Server Action）。
3.  **Server Action 执行**: 这个 Server Action 运行在 Next.js 服务器上。它作为总指挥，调用其他后端模块来完成任务。
4.  **核心逻辑模块**: 
    - `scraper.ts`: 负责从 NHK 网站抓取和解析新闻内容。
    - `database.ts`: 封装了所有 Prisma 调用，负责与 SQLite 数据库进行增删改查。
    - `analyze-article-flow.ts` 等 AI 模块：定义了使用 Genkit 和 Gemini API 进行 AI 分析的具体步骤。
5.  **数据库交互**: `database.ts` 根据 `prisma/schema.prisma` 中定义的模型，对数据库文件 `data/nhk_news_new.db` 进行操作。
6.  **外部服务**: `scraper.ts` 会向 NHK 新闻网站发起 HTTP 请求，而 AI Flows 则会调用 Google AI 的 API。

## 3. 开发环境搭建

请严格按照以下步骤操作，以确保顺利搭建本地开发环境。

### 第一步：克隆与安装

```bash
# 1. 克隆项目到本地
# git clone <repository_url>

# 2. 进入项目目录
cd nhk-japanese-learning

# 3. 安装所有依赖项
npm install
```

### 第二步：配置环境变量

1.  在项目根目录下，复制 `.env.example` 文件并重命名为 `.env`。
2.  打开 `.env` 文件，填入必要的环境变量。最关键的是 `GEMINI_API_KEY`。

    ```env
    # 数据库文件的绝对路径 (Windows 示例)
    DATABASE_URL="file:c:/path/to/your/project/nhk-japanese-learning/data/nhk_news_new.db"

    # 你的 Google Gemini API 密钥
    GEMINI_API_KEY="AIzaSy..."
    ```

    **重要提示**: `DATABASE_URL` 必须是一个以 `file:` 开头的**绝对路径**。请根据你的项目在你电脑上的实际位置进行修改。

### 第三步：初始化数据库

这是最关键的一步。我们将使用一个可靠的两步法来重置并初始化数据库。

```bash
# 1. 重置数据库，清空所有旧数据和表结构
# 这个命令会删除旧的 .db 文件，并创建一个新的空数据库
npx prisma migrate reset --force

# 2. 推送 Schema，根据 prisma/schema.prisma 创建所有表
npx prisma db push

# 3. 填充初始数据，运行种子脚本
# 这会将你在 prisma/seed.js 中定义的初始数据（如 API Key, RSS 源, AI提示词）插入数据库
npx prisma db seed
```

### 第四步：启动开发服务器

```bash
# 启动 Next.js 开发服务器
npm run dev
```

现在，你可以通过浏览器访问 `http://localhost:3000` (或 package.json 中指定的端口) 来查看应用了。

## 4. 核心工作流程解析

理解以下核心流程将帮助你快速定位和修改代码。

### 流程一：添加一个新的 RSS 源

1.  **UI**: 用户在 `/rss-manager` 页面的表单中填写信息。
2.  **Action**: 表单提交时调用 `saveRSSSourceAction` (位于 `actions.ts`)。
3.  **Database**: `saveRSSSourceAction` 调用 `dbManager.createRSSSource` 和 `dbManager.updateFieldMappings` (位于 `database.ts`)，将新数据写入 `rss_sources` 和 `rss_field_mappings` 表。
4.  **Revalidation**: `revalidatePath('/rss-manager')` 被调用，通知 Next.js 该页面的数据已过期，需要重新渲染。

### 流程二：执行一次新闻抓取

1.  **UI**: 用户在 `/scraper` 页面点击“开始抓取”按钮。
2.  **Action**: `startScrapingAction` 被调用。它创建一个 `EnhancedNHKScraper` 实例，并**在后台**启动 `scrapeAllActiveSources` 方法。
3.  **Scraper**: `EnhancedNHKScraper` 从数据库获取所有“活跃”的 RSS 源。
4.  **Loop**: 它遍历每个源，抓取 RSS XML，解析出文章条目。
5.  **Processing**: 对每个条目，它会：
    - 检查数据库中是否已存在相同的文章 URL，如果存在则跳过。
    - 抓取文章的完整页面内容。
    - 下载图片、解析视频元数据。
    - 将所有信息组装成一个 `articleData` 对象。
6.  **Database**: 调用 `dbManager.createArticle` 将 `articleData` 存入数据库。
7.  **AI Queue**: 如果该 RSS 源开启了 AI 处理，则调用 `dbManager.scheduleAIProcessing` 在 `ai_processing_queue` 表中创建一条新任务。

## 5. 开发规范与最佳实践

- **Server Actions 优先**: 尽可能将所有与服务器的交互都封装成 Server Actions。这使得代码更内聚，且易于维护。
- **数据库操作封装**: 所有 Prisma 调用都应通过 `src/lib/server/database.ts` 中的 `dbManager` 对象进行。不要在其他地方直接实例化 `PrismaClient`。
- **类型安全**: 充分利用 TypeScript。为所有函数参数、返回值和复杂对象定义明确的类型。
- **环境变量**: 敏感信息（如 API Key）和环境特定的配置（如数据库路径）必须通过 `.env` 文件管理。
- **后台任务**: 对于耗时较长的操作（如抓取、AI处理），使用“即发即忘”(fire-and-forget) 的方式在 Server Action 中启动它们（即不 `await` 其完成），并通过轮询另一个 Action 来获取进度。

---

希望这份指南能帮助你顺利开始！如果你在搭建或开发过程中遇到任何问题，请随时提问。
