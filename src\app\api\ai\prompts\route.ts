import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// GET /api/ai/prompts - 获取所有AI提示词
export async function GET() {
  try {
    const prompts = await dbManager.getAllAIPrompts();
    return NextResponse.json(prompts);
  } catch (error: any) {
    console.error('获取AI提示词失败:', error);
    return NextResponse.json(
      { error: '获取AI提示词失败', details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/ai/prompts - 创建新的AI提示词
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // 验证必需字段
    const requiredFields = ['name', 'type', 'category', 'title', 'content'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `缺少必需字段: ${field}` },
          { status: 400 }
        );
      }
    }

    const prompt = await dbManager.createAIPrompt(data);
    return NextResponse.json(prompt, { status: 201 });
  } catch (error: any) {
    console.error('创建AI提示词失败:', error);
    return NextResponse.json(
      { error: '创建AI提示词失败', details: error.message },
      { status: 500 }
    );
  }
}
