# NHK 日语学习平台 - 功能规格说明书

## 1. 系统概述

本系统是一个利用 AI 技术辅助日语学习的 Web 应用。它通过自动抓取、解析并处理 NHK 等新闻网站的日文文章，为用户提供一个沉浸式的、高效的日语学习环境。系统集成了文章阅读、词汇管理、语法分析和基于间隔重复（SRS）的复习功能，旨在帮助用户全面提升日语的读解能力。

## 2. 核心功能模块

### 2.1. 内容获取与管理 (RSS 模块)

- **功能描述**: 系统能够从用户配置的多个 RSS 源自动抓取新闻文章。管理员可以对 RSS 源进行增、删、改、查等操作。
- **技术实现**:
    - 后端脚本定时执行抓取任务。
    - `prisma/schema.prisma` 中的 `rss_sources` 表用于存储 RSS 源信息。
    - `articles` 表用于存储抓取到的文章原文和元数据。
- **主要字段**:
    - `rss_sources`: `name`, `url`, `category`, `is_active`, `max_articles`, `fetch_interval`
    - `articles`: `title`, `content`, `url`, `publish_time`, `fetch_time`

### 2.2. AI 智能处理模块

- **功能描述**: 这是系统的核心价值所在。抓取到的原生文章会进入一个 AI 处理队列，由后台服务调用大语言模型（LLM）进行一系列的分析和增强。
- **技术实现**:
    - 使用 `Genkit` 框架与 Google `Gemini` 系列模型进行交互。
    - `ai_processing_queue` 表管理待处理和已处理的文章，确保任务的可靠执行。
    - 分析结果存储在多个关联表中，方便查询和使用。
- **主要功能**:
    1.  **汉字注音 (Furigana)**: 为文章标题、副标题和正文中的所有汉字自动添加平假名注音。结果存储在 `articles` 表的 `*_furigana_html` 字段中。
    2.  **词汇提取**: 智能识别文章中的生词、重点词汇，并提取其读音、词性、中文/英文释义、JLPT 等级等信息。
        - `vocabulary` 表: 存储词汇的基本信息（词、读音、释义等），作为全系统的词库。
        - `article_vocabulary` 表: 记录某篇文章与某个词汇的关联关系。
    3.  **语法点分析**: 识别文章中的关键语法结构，并提供其用法、释义、JLPT 等级和例句。
        - `grammar_points` 表: 存储语法点的基本信息，构成语法库。
        - `article_grammar_points` 表: 记录某篇文章与某个语法点的关联。
    4.  **全文翻译**: 提供文章的中文或英文翻译，帮助用户理解。
        - `article_translations` 表: 存储文章的多语言翻译版本。
    5.  **难度分级**: 根据文章的词汇和语法复杂度，对文章进行难度评估（如 N5-N1）。

### 2.3. 新闻阅读模块

- **功能描述**: 用户在前端界面可以浏览、搜索和筛选经过 AI 处理后的新闻文章。这是用户与系统交互的主要界面。
- **技术实现**:
    - 使用 Next.js 服务端组件 (`src/app/(main)/page.tsx`) 进行数据获取和渲染。
    - `NewsReader` (`@/components/NewsReader.tsx`) 作为核心前端组件，负责展示文章列表和内容。
    - 支持根据难度 (`level`) 和关键词 (`search`) 进行筛选和分页。
- **交互特性**:
    - 点击文章卡片进入阅读器详情页。
    - 在阅读器中，可以点选单词或语法点查看其详细解释。
    - 用户可以将不熟悉的词汇或语法点一键加入自己的学习计划中。

### 2.4. 学习与复习模块 (SRS)

- **功能描述**: 系统为每个用户提供个性化的学习记录和复习计划。它采用间隔重复系统（Spaced Repetition System, SRS）算法，根据用户的熟练度动态安排复习时间。
- **技术实现**:
    - `user_learning_records` 表是此功能的核心，记录了用户、学习对象（文章、词汇、语法点）以及学习状态。
- **核心字段**:
    - `user_id`: 标识用户（**当前版本为固定值 "default_user"**）。
    - `record_type`: 学习类型 (article, vocabulary, grammar_point)。
    - `status`: 学习状态 (new, learning, mastered)。
    - `proficiency_level`, `ease_factor`, `interval`, `next_review_at`: SRS 算法的关键参数，用于计算下一次复习的时间。
- **用户体验**:
    - 用户在“学习中心”或“复习”页面，可以看到今日需要复习的卡片。
    - 在复习时，系统会展示词汇或语法点，用户需要回忆其含义，然后根据自己的掌握情况选择“忘记”、“模糊”或“掌握”，系统会据此更新下一次的复习计划。

### 2.5. 系统管理模块

- **功能描述**: 提供给管理员用于系统维护和配置的后台功能。
- **主要功能**:
    - **RSS 源管理**: 增删改查 RSS 订阅源。
    - **API Key 管理**: `api_keys` 表用于管理访问外部 AI 服务（如 Gemini）的密钥，支持多个密钥的轮换和优先级设置。
    - **系统设置**: `system_settings` 表提供键值对形式的全局配置，如每页文章数量 (`articles_per_page`)。
    - **日志查看**: `scraping_logs` 表记录了爬虫的运行日志，便于排查问题。

## 3. 数据模型

系统的核心数据模型定义在 `prisma/schema.prisma` 文件中，主要实体包括：

- `rss_sources`: RSS 订阅源。
- `articles`: 新闻文章。
- `vocabulary`: 词汇库。
- `grammar_points`: 语法库。
- `user_learning_records`: 用户学习记录。
- `ai_processing_queue`: AI 处理任务队列。
- 以及其他用于关联和日志的表。

这些数据表之间通过清晰的外键关系连接，构成了整个应用的数据基础。
