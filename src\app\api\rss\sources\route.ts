import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

export async function GET() {
  try {
    const sources = await dbManager.getAllRSSSources();
    return NextResponse.json(sources);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const sourceData = await request.json();
    const sourceId = await dbManager.createRSSSource(sourceData);
    return NextResponse.json({ success: true, sourceId, message: 'RSS源添加成功' });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
