'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
    BrainCircuit,
    Sparkles,
    CheckCircle,
    RotateCcw,
    ThumbsDown,
    ThumbsUp,
    Zap,
    BookText,
    Loader,
    Newspaper,
    ExternalLink,
    BookOpen,
    ArrowLeft,
    Volume2,
    Pause
} from 'lucide-react';
import { getStudyQueueAction, submitReviewAction, getActiveStudyQueueAction, getFilteredStudyQueueAction } from '@/app/actions';
import { useTTS } from '@/hooks/useTTS';
import { renderRubyText, renderExamples, renderCollocations, renderRelatedWords } from '@/utils/ruby-utils';
import VocabularyCard from './VocabularyCard';
import GrammarCard from './GrammarCard';

const Flashcard = ({ card, isAnswerVisible, onShowAnswer }: { card: any, isAnswerVisible: boolean, onShowAnswer: () => void }) => {
    const { type, content, sourceArticle } = card;
    const { isPlaying, speak, cancel } = useTTS();
    const [playingCardId, setPlayingCardId] = useState<string | null>(null);
    const [playingContentId, setPlayingContentId] = useState<string | null>(null);

    // 防御性检查：确保 content 存在
    if (!content) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">内容加载中...</p>
            </div>
        );
    }



    const handleSpeak = () => {
        const cardId = type === 'vocabulary' ? `${content?.word || 'vocab'}-${content?.reading || ''}` : (content?.pattern || 'grammar');

        if (playingCardId === cardId && isPlaying) {
            // 如果当前正在播放这个卡片，则停止
            cancel();
            setPlayingCardId(null);
        } else {
            // 播放发音
            const textToSpeak = type === 'vocabulary' ? (content?.reading || content?.word || '词汇') : (content?.pattern || content?.reading || '语法模式');
            setPlayingCardId(cardId);
            speak(textToSpeak, 'ja-JP');

            // 设置播放结束后的清理
            setTimeout(() => {
                setPlayingCardId(null);
            }, 3000); // 3秒后自动清理状态
        }
    };

    // 处理语义网络中单词的发音
    const handleContentSpeak = (text: string, contentId: string, language: 'ja-JP' | 'en-US' = 'ja-JP') => {
        if (playingContentId === contentId && isPlaying) {
            // 如果当前正在播放这个内容，则停止
            cancel();
            setPlayingContentId(null);
        } else {
            // 播放发音
            setPlayingContentId(contentId);
            speak(text, language);

            // 设置播放结束后的清理
            setTimeout(() => {
                setPlayingContentId(null);
            }, 3000); // 3秒后自动清理状态
        }
    };

    return (
        <div
            className={`w-full max-w-2xl ${isAnswerVisible ? 'min-h-[600px]' : 'min-h-[400px]'} bg-white rounded-xl shadow-lg border border-gray-200 p-8 flex flex-col ${isAnswerVisible ? 'justify-start' : 'justify-center'} items-center text-center cursor-pointer relative`}
            onClick={!isAnswerVisible ? onShowAnswer : undefined}
        >
            <div className="absolute top-4 right-4 flex items-center space-x-2">
                {sourceArticle && (
                    <a
                        href={`/news/${sourceArticle.id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-full hover:bg-indigo-100 transition-colors"
                        title={`查看原文：${sourceArticle.title}`}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <ExternalLink className="h-3 w-3" />
                        <span>原文</span>
                    </a>
                )}

            </div>

            {/* TTS按钮 - 左上角 */}
            <div className="absolute top-4 left-4">
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        handleSpeak();
                    }}
                    className={`p-2 rounded-lg transition-all ${
                        playingCardId === (type === 'vocabulary' ? `${content?.word || 'vocab'}-${content?.reading || ''}` : (content?.pattern || 'grammar')) && isPlaying
                            ? 'text-indigo-600 bg-indigo-50'
                            : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                    }`}
                    title="发音"
                >
                    {playingCardId === (type === 'vocabulary' ? `${content?.word || 'vocab'}-${content?.reading || ''}` : (content?.pattern || 'grammar')) && isPlaying ? (
                        <Pause className="h-5 w-5 animate-pulse" />
                    ) : (
                        <Volume2 className="h-5 w-5" />
                    )}
                </button>
            </div>
            
            {!isAnswerVisible ? (
                // Question side
                <div className="space-y-4">
                    <h2 className="text-5xl font-bold text-gray-900">{type === 'vocabulary' ? (content?.word || '词汇') : (content?.pattern || '语法模式')}</h2>
                    <div className="flex flex-col items-center space-y-4">
                        <button onClick={onShowAnswer} className="px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors">
                            显示答案
                        </button>
                    </div>
                </div>
            ) : (
                // Answer side
                <div className="text-left w-full space-y-4 max-h-[600px] overflow-y-auto">
                    <div className="flex items-center justify-center space-x-3 mb-4">
                        <h2 className="text-3xl font-bold text-gray-900">
                            {type === 'vocabulary' ? `${content?.word || '词汇'} (${content?.reading || ''})` : (content?.pattern || '语法模式')}
                        </h2>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                handleSpeak();
                            }}
                            className={`p-2 rounded-lg transition-all ${
                                playingCardId === (type === 'vocabulary' ? `${content?.word || 'vocab'}-${content?.reading || ''}` : (content?.pattern || 'grammar')) && isPlaying
                                    ? 'text-indigo-600 bg-indigo-50'
                                    : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                            }`}
                            title="发音"
                        >
                            {playingCardId === (type === 'vocabulary' ? `${content?.word || 'vocab'}-${content?.reading || ''}` : (content?.pattern || 'grammar')) && isPlaying ? (
                                <Pause className="h-4 w-4 animate-pulse" />
                            ) : (
                                <Volume2 className="h-4 w-4" />
                            )}
                        </button>
                    </div>

                    {type === 'vocabulary' ? (
                        <VocabularyCard
                            vocab={content}
                            onVocabSpeak={(vocab) => {
                                const cardId = `${vocab.word}-${vocab.reading}`;
                                if (playingCardId === cardId && isPlaying) {
                                    cancel();
                                    setPlayingCardId(null);
                                } else {
                                    const textToSpeak = vocab.reading || vocab.word;
                                    setPlayingCardId(cardId);
                                    speak(textToSpeak, 'ja-JP');
                                    setTimeout(() => setPlayingCardId(null), 3000);
                                }
                            }}
                            onContentSpeak={handleContentSpeak}
                            playingVocabId={playingCardId}
                            playingContentId={playingContentId}
                            isPlaying={isPlaying}
                            showActions={false}
                            compact={true}
                            className="border-0 p-0 hover:bg-transparent"
                        />
                    ) : (
                        <GrammarCard
                            grammar={content}
                            onGrammarSpeak={(grammar) => {
                                const cardId = grammar.pattern || 'grammar';
                                if (playingCardId === cardId && isPlaying) {
                                    cancel();
                                    setPlayingCardId(null);
                                } else {
                                    const textToSpeak = grammar.pattern || grammar.reading || '语法模式';
                                    setPlayingCardId(cardId);
                                    speak(textToSpeak, 'ja-JP');
                                    setTimeout(() => setPlayingCardId(null), 3000);
                                }
                            }}
                            onContentSpeak={handleContentSpeak}
                            playingGrammarId={playingCardId}
                            playingContentId={playingContentId}
                            isPlaying={isPlaying}
                            showActions={false}
                            compact={true}
                            className="border-0 p-0 hover:bg-transparent"
                        />
                    )}

                    {/* 来源文章 */}
                    {sourceArticle && (
                        <div className="space-y-2 pt-4 border-t border-gray-200">
                            <p className="text-sm text-gray-500">来源文章</p>
                            <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-md border border-indigo-200">
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-indigo-900 truncate" title={sourceArticle.title}>
                                        {sourceArticle.title}
                                    </p>
                                </div>
                                <a
                                    href={`/news/${sourceArticle.id}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex items-center space-x-1 ml-3 px-3 py-1 text-sm font-medium text-indigo-600 bg-white rounded-md hover:bg-indigo-100 transition-colors"
                                    onClick={(e) => e.stopPropagation()}
                                >
                                    <ExternalLink className="h-4 w-4" />
                                    <span>查看原文</span>
                                </a>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

interface StudySessionWithModeProps {
  mode: 'learn' | 'review' | 'mixed';
  contentType?: 'vocabulary' | 'grammar' | 'mixed';
  onModeChange?: () => void;
}

const StudySessionWithMode: React.FC<StudySessionWithModeProps> = ({ mode, contentType = 'mixed', onModeChange }) => {
  const { data: session, status } = useSession();
  const [studyQueue, setStudyQueue] = useState<(any)[]>([]);
  const [newCount, setNewCount] = useState(0);
  const [reviewCount, setReviewCount] = useState(0);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnswerVisible, setIsAnswerVisible] = useState(false);
  const [sessionComplete, setSessionComplete] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  
  const totalItems = useMemo(() => studyQueue.length, [studyQueue]);

  const initializeSession = useCallback(async () => {
    setLoading(true);

    if (status === 'loading') {
        setLoading(false);
        return;
    }

    // 在开发模式下，如果启用了自动登录，使用默认用户ID
    const userId = session?.user?.id ||
      (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' ? '1' : null);

    if (!userId) {
        setLoading(false);
        return;
    }

    // 使用新的过滤API或回退到原始API
    let result;
    if (contentType && contentType !== 'mixed' && mode !== 'mixed') {
        result = await getFilteredStudyQueueAction(userId, mode, contentType);
    } else {
        result = await getStudyQueueAction(userId);
    }

    if (result.success && result.data) {
        const { newItems, reviewItems } = result.data;
        const newItemsWithFlag = newItems.map((item: any) => ({ ...item, isNew: true }));
        const reviewItemsWithFlag = reviewItems.map((item: any) => ({ ...item, isNew: false }));

        // 根据模式过滤学习内容
        let filteredQueue = [];
        if (mode === 'learn') {
            filteredQueue = newItemsWithFlag;
        } else if (mode === 'review') {
            filteredQueue = reviewItemsWithFlag;
        } else {
            // mixed mode - 默认行为
            filteredQueue = [...newItemsWithFlag, ...reviewItemsWithFlag];
        }

        setStudyQueue(filteredQueue);
        setNewCount(newItemsWithFlag.length);
        setReviewCount(reviewItemsWithFlag.length);
    } else {
        console.error("Failed to fetch study queue:", result.error);
        setStudyQueue([]);
    }

    setCurrentIndex(0);
    setSessionComplete(false);
    setIsAnswerVisible(false);
    setStartTime(new Date());
    setLoading(false);
  }, [session, status, mode, contentType]);

  useEffect(() => {
    if (status !== 'loading') {
      initializeSession();
    }
  }, [initializeSession, status]);

  const handleNextCard = async () => {
    setIsAnswerVisible(false);
    if (currentIndex < totalItems - 1) {
      setCurrentIndex(prev => prev + 1);
    } else {
      // 当前队列处理完了，检查是否还有今天需要学习的内容
      await checkForMoreStudyItems();
    }
  };

  const checkForMoreStudyItems = async () => {
    // 在开发模式下，如果启用了自动登录，使用默认用户ID
    const userId = session?.user?.id ||
      (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' ? '1' : null);

    if (!userId) {
      setSessionComplete(true);
      return;
    }

    try {
      const result = await getActiveStudyQueueAction(userId);
      if (result.success && result.data) {
        const { newItems, reviewItems } = result.data;
        const allItems = [...newItems, ...reviewItems];

        if (allItems.length > 0) {
          // 还有需要学习的内容，继续学习会话
          console.log(`Found ${allItems.length} more items to study today`);

          // 根据模式过滤内容
          let filteredQueue = [];
          const newItemsWithFlag = newItems.map((item: any) => ({ ...item, isNew: true }));
          const reviewItemsWithFlag = reviewItems.map((item: any) => ({ ...item, isNew: false }));

          if (mode === 'learn') {
            filteredQueue = newItemsWithFlag;
          } else if (mode === 'review') {
            filteredQueue = reviewItemsWithFlag;
          } else {
            filteredQueue = [...newItemsWithFlag, ...reviewItemsWithFlag];
          }

          if (filteredQueue.length > 0) {
            // 更新学习队列，重置索引
            setStudyQueue(filteredQueue);
            setNewCount(newItemsWithFlag.length);
            setReviewCount(reviewItemsWithFlag.length);
            setCurrentIndex(0);
            console.log(`Continuing session with ${filteredQueue.length} items`);
          } else {
            // 当前模式下没有更多内容
            setSessionComplete(true);
          }
        } else {
          // 今天真的没有更多需要学习的内容了
          setSessionComplete(true);
        }
      } else {
        setSessionComplete(true);
      }
    } catch (error) {
      console.error('Failed to check for more study items:', error);
      setSessionComplete(true);
    }
  };
  
  const handleFeedback = async (quality: 1 | 2 | 3 | 4) => {
    const recordId = studyQueue[currentIndex]?.id;
    if (!session?.user?.id || !recordId) return;

    // 提交反馈
    await submitReviewAction(recordId, quality, session.user.id);

    // 直接进入下一张卡片，让 handleNextCard 处理队列更新逻辑
    await handleNextCard();
  };

  const getModeDisplayName = () => {
    const modeText = mode === 'learn' ? '学习' : mode === 'review' ? '复习' : '混合学习';
    const contentText = contentType === 'vocabulary' ? '词汇' :
                       contentType === 'grammar' ? '语法' : '内容';

    if (contentType === 'mixed') {
      return `${modeText}模式`;
    } else {
      return `${modeText}${contentText}`;
    }
  };

  if (loading) {
    return (
        <div className="flex h-full items-center justify-center py-12">
            <div className="text-center">
                <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
                <p className="text-gray-600">正在准备{getModeDisplayName()}...</p>
            </div>
        </div>
    );
  }

  if (sessionComplete) {
    const duration = startTime ? Math.round((new Date().getTime() - startTime.getTime()) / 1000) : 0;
    return (
        <div className="text-center py-12">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">太棒了！</h2>
            <p className="text-gray-600 mb-6">{getModeDisplayName()}任务已全部完成。</p>
            <div className="text-gray-700 mb-8">
                <p>总计完成 {totalItems} 个项目，用时 {Math.floor(duration / 60)}分 {duration % 60}秒。</p>
            </div>
            <div className="flex justify-center space-x-4">
                {onModeChange && (
                  <button
                      onClick={onModeChange}
                      className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors"
                  >
                      <ArrowLeft className="h-5 w-5" />
                      <span>选择其他模式</span>
                  </button>
                )}
                <button
                    onClick={initializeSession}
                    className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors"
                >
                    <RotateCcw className="h-5 w-5" />
                    <span>重新开始本轮</span>
                </button>
                <button
                    onClick={() => router.push('/')}
                    className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors"
                >
                    <Newspaper className="h-5 w-5" />
                    <span>去发现新内容</span>
                </button>
            </div>
        </div>
    );
  }

  if (studyQueue.length === 0) {
      const contentText = contentType === 'vocabulary' ? '词汇' :
                         contentType === 'grammar' ? '语法' : '词汇或语法';
      const modeText = mode === 'learn' ? `新${contentText}学习` :
                      mode === 'review' ? `${contentText}复习任务` : '学习任务';

      const emptyMessage = mode === 'learn'
        ? `暂无新的${contentText}需要学习。请先去阅读新闻，将遇到的重点内容加入学习计划。`
        : mode === 'review'
        ? `今天没有需要复习的${contentText}。您可以选择学习新内容或去阅读更多新闻。`
        : '你的学习队列是空的。请先去阅读新闻，并将遇到的重点词汇或语法点加入学习计划。';

      return (
          <div className="text-center py-12">
              <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">暂无{modeText}</h2>
              <p className="text-gray-600 max-w-md mx-auto mb-6">
                {emptyMessage}
              </p>
              <div className="flex justify-center space-x-4">
                {onModeChange && (
                  <button
                    onClick={onModeChange}
                    className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold hover:bg-gray-700 transition-colors"
                  >
                    <ArrowLeft className="h-5 w-5" />
                    <span>选择其他模式</span>
                  </button>
                )}
                <button
                  onClick={() => router.push('/')}
                  className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors"
                >
                  <Newspaper className="h-5 w-5" />
                  <span>去新闻列表看看</span>
                </button>
              </div>
          </div>
      );
  }

  const currentCard = studyQueue[currentIndex];
  const newCardsRemaining = mode === 'learn' ? studyQueue.length - currentIndex : 0;
  const reviewCardsRemaining = mode === 'review' ? studyQueue.length - currentIndex : 0;
  const mixedNewRemaining = mode === 'mixed' ? Math.max(0, newCount - (currentCard.isNew ? currentIndex : newCount)) : 0;
  const mixedReviewRemaining = mode === 'mixed' ? Math.max(0, reviewCount - (currentCard.isNew ? 0 : (currentIndex - newCount + 1))) : 0;

  return (
    <div className="space-y-8 flex flex-col items-center">
        {/* 模式指示器 */}
        <div className="w-full max-w-2xl">
            <div className="flex items-center justify-center space-x-2 mb-4">
                <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                    mode === 'learn' ? 'bg-blue-100 text-blue-800' :
                    mode === 'review' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                }`}>
                    {getModeDisplayName()}
                </div>
                {onModeChange && (
                    <button
                        onClick={onModeChange}
                        className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        切换模式
                    </button>
                )}
            </div>
        </div>

        {/* Progress Section */}
        <div className="w-full max-w-2xl space-y-3">
            <div className="flex justify-between items-center text-sm font-medium">
                {mode === 'learn' && (
                    <>
                        <span className="text-blue-600">新内容: {newCardsRemaining}</span>
                        <span className="text-gray-600">总计: {newCardsRemaining}</span>
                    </>
                )}
                {mode === 'review' && (
                    <>
                        <span className="text-green-600">待复习: {reviewCardsRemaining}</span>
                        <span className="text-gray-600">总计: {reviewCardsRemaining}</span>
                    </>
                )}
                {mode === 'mixed' && (
                    <>
                        <span className="text-blue-600">新卡片: {mixedNewRemaining}</span>
                        <span className="text-green-600">待复习: {mixedReviewRemaining}</span>
                        <span className="text-gray-600">总计: {mixedNewRemaining + mixedReviewRemaining}</span>
                    </>
                )}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-indigo-500 h-2 rounded-full" style={{width: `${(currentIndex / totalItems) * 100}%`}}></div>
            </div>
        </div>
        
        {/* Flashcard */}
        <Flashcard 
            card={currentCard}
            isAnswerVisible={isAnswerVisible}
            onShowAnswer={() => setIsAnswerVisible(true)}
        />
        
        {/* Feedback Buttons */}
        {isAnswerVisible && (
            <div className="w-full max-w-2xl grid grid-cols-4 gap-4 pt-4">
                <button onClick={() => handleFeedback(1)} className="flex flex-col items-center justify-center space-y-1 p-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors">
                    <ThumbsDown className="h-5 w-5"/>
                    <span className="text-sm font-semibold">重来</span>
                </button>
                 <button onClick={() => handleFeedback(2)} className="flex flex-col items-center justify-center space-y-1 p-3 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors">
                    <BrainCircuit className="h-5 w-5"/>
                    <span className="text-sm font-semibold">困难</span>
                </button>
                 <button onClick={() => handleFeedback(3)} className="flex flex-col items-center justify-center space-y-1 p-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                    <ThumbsUp className="h-5 w-5"/>
                    <span className="text-sm font-semibold">良好</span>
                </button>
                 <button onClick={() => handleFeedback(4)} className="flex flex-col items-center justify-center space-y-1 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                    <Zap className="h-5 w-5"/>
                    <span className="text-sm font-semibold">轻松</span>
                </button>
            </div>
        )}
    </div>
  );
};

export default StudySessionWithMode;
