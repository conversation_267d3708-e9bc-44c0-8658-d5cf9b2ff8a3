# 动态图片服务API实现方案

## 问题背景

在Next.js应用中，`npm run dev`（开发模式）和`npm run start`（生产模式）对新抓取的图片显示效果不同：

- **开发模式**：新抓取的图片能实时显示
- **生产模式**：新抓取的图片无法实时显示，需要重启服务

## 根本原因

- **开发模式**：Next.js开发服务器实时监控`public`目录，新文件立即可访问
- **生产模式**：Next.js生产服务器依赖构建时的静态文件映射，新文件需要重启才能识别

## 解决方案

创建动态API路由来提供媒体文件服务，绕过静态文件限制。

## 实现详情

### 1. 动态媒体服务API

**文件**: `src/app/api/media/[...path]/route.ts`

- 接收任意媒体文件路径参数
- 从文件系统动态读取文件
- 设置正确的Content-Type和缓存头
- 支持图片、视频、音频等多种媒体类型
- 包含安全检查，防止路径遍历攻击

### 2. 路径转换工具

**文件**: `src/utils/media-utils.ts`

提供以下工具函数：
- `convertToApiPath()`: 将静态路径转换为API路径
- `getMediaSrc()`: 获取媒体文件的最佳访问路径
- `isLocalMediaPath()`: 检查是否为本地媒体文件
- `extractMediaPath()`: 从API路径提取原始路径

### 3. 抓取器路径生成修改

**文件**: `src/lib/server/scraper.ts`

修改媒体文件路径生成逻辑：
```typescript
// 原来：/media/images/2025/01/26/image.jpg
// 现在：/api/media/images/2025/01/26/image.jpg
const dbPath = `/api/media/${type}s/${year}/${month}/${day}/${originalFilename}`;
```

### 4. 组件更新

**文件**: 
- `src/components/NewsCard.tsx`
- `src/components/ArticleReader.tsx`

使用新的工具函数处理媒体路径，确保向后兼容性。

## 技术优势

### ✅ 主要优势
1. **实时性**：新图片立即可访问，无需重启服务
2. **统一性**：开发模式和生产模式行为一致
3. **灵活性**：可以添加额外处理逻辑（压缩、水印等）
4. **安全性**：包含路径验证和访问控制
5. **向后兼容**：支持现有数据库中的旧路径格式

### ⚠️ 性能考虑
1. **轻微开销**：相比静态文件服务有少量性能开销
2. **缓存优化**：设置了长期缓存头（1年）
3. **内存管理**：可以添加内存缓存进一步优化

## 使用示例

### 新抓取的文件
```typescript
// 抓取器自动生成API路径
const imagePath = "/api/media/images/2025/01/26/news_image.jpg";
```

### 组件中使用
```typescript
// 自动处理新旧路径格式
const imageSrc = getMediaSrc(article.featured_image_path, article.featured_image_url);
```

### API访问
```
GET /api/media/images/2025/01/26/news_image.jpg
GET /api/media/videos/2025/01/26/video_folder/index.m3u8
GET /api/media/audios/2025/01/26/audio_file.mp3
```

## 部署说明

1. **无需额外配置**：方案完全基于Next.js内置功能
2. **自动生效**：新抓取的文件自动使用API路径
3. **渐进迁移**：现有文件继续工作，新文件使用新路径

## 测试验证

1. 启动开发服务器：`npm run dev`
2. 运行新闻抓取
3. 验证新图片在开发和生产模式下都能实时显示
4. 检查API路径是否正确响应

## 总结

这个方案以最小的代码修改量解决了生产模式下图片实时显示的问题，同时提供了更好的灵活性和控制能力。是一个优雅且实用的解决方案。
