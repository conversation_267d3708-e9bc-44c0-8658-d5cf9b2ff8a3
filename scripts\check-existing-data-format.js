const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkExistingDataFormat() {
  try {
    console.log('=== 检查现有数据格式 ===\n');

    // 查看有related_words_json数据的词汇
    const vocabulariesWithRelatedWords = await prisma.vocabulary.findMany({
      where: {
        related_words_json: {
          not: null
        }
      },
      take: 5,
      select: {
        id: true,
        word: true,
        reading: true,
        related_words_json: true
      }
    });

    console.log(`找到 ${vocabulariesWithRelatedWords.length} 个包含related_words_json的词汇\n`);

    vocabulariesWithRelatedWords.forEach((vocab, index) => {
      console.log(`${index + 1}. 词汇: ${vocab.word} (${vocab.reading})`);
      console.log(`   ID: ${vocab.id}`);
      
      try {
        const relatedWords = JSON.parse(vocab.related_words_json);
        console.log(`   related_words_json 格式:`);
        console.log(`   - 类型: ${typeof relatedWords}`);
        console.log(`   - 内容:`, relatedWords);
        
        // 检查每个类型的数据格式
        ['synonyms', 'antonyms', 'hypernyms', 'hyponyms', 'wordFamily', 'relatedConcepts'].forEach(type => {
          if (relatedWords[type]) {
            console.log(`   - ${type}: ${Array.isArray(relatedWords[type]) ? '数组' : '非数组'}`);
            if (Array.isArray(relatedWords[type]) && relatedWords[type].length > 0) {
              const firstItem = relatedWords[type][0];
              console.log(`     第一项类型: ${typeof firstItem}`);
              if (typeof firstItem === 'object') {
                console.log(`     第一项内容:`, firstItem);
              } else {
                console.log(`     第一项内容: "${firstItem}"`);
              }
            }
          }
        });
      } catch (error) {
        console.log(`   解析JSON失败:`, error.message);
      }
      
      console.log('');
    });

    // 统计总数
    const totalVocabularies = await prisma.vocabulary.count();
    const withRelatedWords = await prisma.vocabulary.count({
      where: {
        related_words_json: {
          not: null
        }
      }
    });

    console.log(`总词汇数: ${totalVocabularies}`);
    console.log(`包含related_words_json的词汇数: ${withRelatedWords}`);
    console.log(`比例: ${((withRelatedWords / totalVocabularies) * 100).toFixed(2)}%`);

  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkExistingDataFormat();
