'use client';

import React, { useState } from 'react';
import { Loader2, Play, RotateCcw, Co<PERSON>, Check, BarChart3, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle } from 'lucide-react';
import VocabularyCard from '@/components/VocabularyCard';
import GrammarCard from '@/components/GrammarCard';
import { useTTS } from '@/hooks/useTTS';

interface TestResult {
  id: string;
  timestamp: string;
  input: any;
  output: any;
  processingTime: number;
  success: boolean;
  error?: string;
}

export default function PromptTestPage() {
  const [activeTab, setActiveTab] = useState('article');
  const [isLoading, setIsLoading] = useState(false);
  const [isBatchTesting, setIsBatchTesting] = useState(false);
  const [batchCount, setBatchCount] = useState(3);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [copied, setCopied] = useState<string | null>(null);

  // AI模型设置
  const [modelName, setModelName] = useState('googleai/gemini-2.0-flash');

  // 文章分析测试状态
  const [articleTitle, setArticleTitle] = useState('');
  const [articleSubtitle, setArticleSubtitle] = useState('');
  const [articleContent, setArticleContent] = useState('');

  // 语法分析测试状态
  const [grammarText, setGrammarText] = useState('');

  // TTS功能
  const { speak, cancel, isPlaying } = useTTS();
  const [playingVocabId, setPlayingVocabId] = useState<string | null>(null);
  const [playingContentId, setPlayingContentId] = useState<string | null>(null);

  // 示例数据
  const sampleArticle = {
    title: '日本の経済成長が加速',
    subtitle: '新技術の導入により生産性が向上',
    content: '<h3>技術革新の影響</h3><p>日本の製造業では、AI技術の導入により生産性が大幅に向上している。特に自動車産業において、その効果が顕著に現れている。</p><p>専門家によると、この傾向は今後も続くと予想される。</p>'
  };

  const sampleGrammar = '新しい技術について説明します。この方法を使うことができれば、効率が向上するでしょう。';

  const loadSampleData = () => {
    if (activeTab === 'article') {
      setArticleTitle(sampleArticle.title);
      setArticleSubtitle(sampleArticle.subtitle);
      setArticleContent(sampleArticle.content);
    } else {
      setGrammarText(sampleGrammar);
    }
    console.log('✅ 示例数据已加载');
  };

  const runSingleTest = async () => {
    const startTime = Date.now();

    try {
      let response;
      let input;

      if (activeTab === 'article') {
        input = {
          title: articleTitle,
          subtitle: articleSubtitle,
          content: articleContent,
          modelName: modelName
        };
        response = await fetch('/api/ai/test/analyze-article', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input)
        });
      } else {
        input = {
          text: grammarText,
          modelName: modelName
        };
        response = await fetch('/api/ai/test/analyze-grammar', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input)
        });
      }

      const processingTime = Date.now() - startTime;
      const result = await response.json();

      const testResult: TestResult = {
        id: Date.now().toString(),
        timestamp: new Date().toLocaleString('zh-CN'),
        input,
        output: result,
        processingTime,
        success: response.ok
      };

      if (!response.ok) {
        testResult.error = result.error || '未知错误';
      }

      setTestResults(prev => [testResult, ...prev]);
      
      if (response.ok) {
        console.log(`✅ 测试完成，耗时 ${processingTime}ms`);
      } else {
        console.log(`❌ 测试失败: ${testResult.error}`);
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const testResult: TestResult = {
        id: Date.now().toString(),
        timestamp: new Date().toLocaleString('zh-CN'),
        input: activeTab === 'article' ? { title: articleTitle, subtitle: articleSubtitle, content: articleContent, modelName: modelName } : { text: grammarText, modelName: modelName },
        output: null,
        processingTime,
        success: false,
        error: error instanceof Error ? error.message : '网络错误'
      };

      setTestResults(prev => [testResult, ...prev]);
      console.log('❌ 测试失败: 网络错误');
    }
  };

  const runBatchTest = async () => {
    setIsBatchTesting(true);
    console.log(`🚀 开始批量测试，将运行 ${batchCount} 次`);
    
    for (let i = 0; i < batchCount; i++) {
      console.log(`⏳ 正在运行第 ${i + 1}/${batchCount} 次测试...`);
      await runSingleTest();
      
      // 在测试之间添加短暂延迟，避免API限制
      if (i < batchCount - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    setIsBatchTesting(false);
    console.log(`✅ 批量测试完成！共运行 ${batchCount} 次测试`);
  };

  const runTest = async () => {
    setIsLoading(true);
    await runSingleTest();
    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
    console.log('🗑️ 测试结果已清空');
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(id);
      setTimeout(() => setCopied(null), 2000);
      console.log('📋 已复制到剪贴板');
    } catch (error) {
      console.log('❌ 复制失败');
    }
  };

  // TTS处理函数
  const handleVocabSpeak = (vocab: any) => {
    const vocabId = `${vocab.word}-${vocab.reading}`;
    if (playingVocabId === vocabId && isPlaying) {
      cancel();
      setPlayingVocabId(null);
    } else {
      const textToSpeak = vocab.reading || vocab.word;
      setPlayingVocabId(vocabId);
      speak(textToSpeak, 'ja-JP');
      setTimeout(() => setPlayingVocabId(null), 3000);
    }
  };

  const handleContentSpeak = (text: string, contentId: string, language: 'ja-JP' | 'en-US' = 'ja-JP') => {
    if (playingContentId === contentId && isPlaying) {
      cancel();
      setPlayingContentId(null);
    } else {
      setPlayingContentId(contentId);
      speak(text, language);
      setTimeout(() => setPlayingContentId(null), 5000);
    }
  };

  const canRunTest = () => {
    if (activeTab === 'article') {
      return articleTitle.trim() !== '';
    } else {
      return grammarText.trim() !== '';
    }
  };

  // 分析函数
  const analyzeResults = () => {
    if (testResults.length < 2) return null;

    const successfulResults = testResults.filter(r => r.success);
    
    const analysis = {
      totalTests: testResults.length,
      successRate: (successfulResults.length / testResults.length * 100).toFixed(1),
      averageTime: Math.round(testResults.reduce((sum, r) => sum + r.processingTime, 0) / testResults.length),
      consistencyIssues: [] as string[],
      formatIssues: [] as string[]
    };

    // 检查一致性问题
    if (activeTab === 'article' && successfulResults.length > 1) {
      const vocabularyCounts = successfulResults.map(r => r.output?.vocabulary?.length || 0);
      const grammarCounts = successfulResults.map(r => r.output?.grammar?.length || 0);
      
      const vocabVariance = Math.max(...vocabularyCounts) - Math.min(...vocabularyCounts);
      const grammarVariance = Math.max(...grammarCounts) - Math.min(...grammarCounts);
      
      if (vocabVariance > 3) {
        analysis.consistencyIssues.push(`词汇数量变化较大 (${Math.min(...vocabularyCounts)}-${Math.max(...vocabularyCounts)})`);
      }
      if (grammarVariance > 2) {
        analysis.consistencyIssues.push(`语法点数量变化较大 (${Math.min(...grammarCounts)}-${Math.max(...grammarCounts)})`);
      }

      // 检查格式问题
      successfulResults.forEach((result, index) => {
        const output = result.output;
        if (!output?.titleWithFurigana) {
          analysis.formatIssues.push(`测试${index + 1}: 缺少标题振り仮名`);
        }
        if (output?.vocabulary?.some((v: any) => !v.examples || v.examples.length !== 3)) {
          analysis.formatIssues.push(`测试${index + 1}: 词汇例句数量不正确`);
        }
        if (output?.grammar?.some((g: any) => !g.examples || g.examples.length !== 3)) {
          analysis.formatIssues.push(`测试${index + 1}: 语法例句数量不正确`);
        }
      });
    }

    return analysis;
  };

  const analysis = analyzeResults();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI提示词测试</h1>
          <p className="text-gray-600 mt-2">
            测试和验证AI分析功能的稳定性和准确性
          </p>
        </div>
        <div className="flex gap-2">
          <button 
            onClick={loadSampleData}
            className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            加载示例数据
          </button>
          <button 
            onClick={clearResults}
            disabled={testResults.length === 0}
            className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            清空结果
          </button>
        </div>
      </div>

      {/* 分析结果面板 */}
      {analysis && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              测试结果分析
            </h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{analysis.totalTests}</div>
                <div className="text-sm text-gray-600">总测试次数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{analysis.successRate}%</div>
                <div className="text-sm text-gray-600">成功率</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{analysis.averageTime}ms</div>
                <div className="text-sm text-gray-600">平均耗时</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {analysis.consistencyIssues.length + analysis.formatIssues.length}
                </div>
                <div className="text-sm text-gray-600">发现问题</div>
              </div>
            </div>
            
            {(analysis.consistencyIssues.length > 0 || analysis.formatIssues.length > 0) && (
              <div className="space-y-3">
                {analysis.consistencyIssues.length > 0 && (
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-sm">一致性问题:</div>
                      <ul className="text-sm text-gray-600 list-disc list-inside">
                        {analysis.consistencyIssues.map((issue, i) => (
                          <li key={i}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
                
                {analysis.formatIssues.length > 0 && (
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
                    <div>
                      <div className="font-medium text-sm">格式问题:</div>
                      <ul className="text-sm text-gray-600 list-disc list-inside">
                        {analysis.formatIssues.map((issue, i) => (
                          <li key={i}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {analysis.consistencyIssues.length === 0 && analysis.formatIssues.length === 0 && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm">所有测试结果格式一致，未发现问题</span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 测试输入区域 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">测试输入</h3>
          </div>
          <div className="px-6 py-4">
            {/* Tab 切换 */}
            <div className="inline-flex h-10 items-center justify-center rounded-lg bg-gray-100 p-1 text-gray-500 w-full mb-4">
              <button
                className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all flex-1 ${
                  activeTab === 'article' ? 'bg-white text-gray-950 shadow-sm' : 'text-gray-500 hover:text-gray-900'
                }`}
                onClick={() => setActiveTab('article')}
              >
                文章分析
              </button>
              <button
                className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all flex-1 ${
                  activeTab === 'grammar' ? 'bg-white text-gray-950 shadow-sm' : 'text-gray-500 hover:text-gray-900'
                }`}
                onClick={() => setActiveTab('grammar')}
              >
                语法分析
              </button>
            </div>
            
            {/* AI模型设置 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">AI模型名称</label>
              <input
                value={modelName}
                onChange={(e) => setModelName(e.target.value)}
                placeholder="例如: googleai/gemini-2.0-flash"
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <p className="text-xs text-gray-500 mt-1">指定用于测试的AI模型名称</p>
            </div>

            {/* Tab 内容 */}
            {activeTab === 'article' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章标题 *</label>
                  <input
                    value={articleTitle}
                    onChange={(e) => setArticleTitle(e.target.value)}
                    placeholder="输入日语文章标题..."
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">副标题</label>
                  <input
                    value={articleSubtitle}
                    onChange={(e) => setArticleSubtitle(e.target.value)}
                    placeholder="输入日语副标题（可选）..."
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章内容</label>
                  <textarea
                    value={articleContent}
                    onChange={(e) => setArticleContent(e.target.value)}
                    placeholder="输入日语文章内容（支持HTML格式）..."
                    rows={8}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-vertical"
                  />
                </div>
              </div>
            )}
            
            {activeTab === 'grammar' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">日语文本 *</label>
                  <textarea
                    value={grammarText}
                    onChange={(e) => setGrammarText(e.target.value)}
                    placeholder="输入要分析语法的日语文本..."
                    rows={6}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-vertical"
                  />
                </div>
              </div>
            )}

            <div className="mt-6 space-y-3">
              <button 
                onClick={runTest} 
                disabled={!canRunTest() || isLoading || isBatchTesting}
                className="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {isLoading ? '测试中...' : '单次测试'}
              </button>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">批量测试次数</label>
                  <input
                    type="number"
                    min="2"
                    max="10"
                    value={batchCount}
                    onChange={(e) => setBatchCount(parseInt(e.target.value) || 3)}
                    disabled={isLoading || isBatchTesting}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                <div className="flex items-end">
                  <button 
                    onClick={runBatchTest}
                    disabled={!canRunTest() || isLoading || isBatchTesting}
                    className="whitespace-nowrap inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                  >
                    {isBatchTesting ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <BarChart3 className="w-4 h-4 mr-2" />
                    )}
                    {isBatchTesting ? '批量测试中...' : '批量测试'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 测试结果区域 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center justify-between">
              测试结果
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {testResults.length} 次测试
              </span>
            </h3>
          </div>
          <div className="px-6 py-4">
            {testResults.length === 0 ? (
              <div className="text-center text-gray-600 py-8">
                暂无测试结果
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {testResults.map((result) => (
                  <div key={result.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          result.success ? 'bg-indigo-100 text-indigo-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {result.success ? "成功" : "失败"}
                        </span>
                        <span className="text-sm text-gray-600">
                          {result.timestamp}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                          {result.processingTime}ms
                        </span>
                        <button
                          onClick={() => copyToClipboard(JSON.stringify(result.output, null, 2), result.id)}
                          className="text-gray-700 hover:bg-gray-100 p-1 rounded transition-colors"
                        >
                          {copied === result.id ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    {result.error && (
                      <div className="text-red-600 text-sm mb-2">
                        错误: {result.error}
                      </div>
                    )}
                    
                    {result.success && result.output && (
                      <div className="space-y-4">
                        {/* 词汇和语法可视化显示 */}
                        {result.output.vocabulary && result.output.vocabulary.length > 0 && (
                          <div>
                            <h4 className="text-sm font-semibold text-gray-700 mb-2">
                              词汇分析结果 ({result.output.vocabulary.length}个)
                            </h4>
                            <div className="space-y-2 max-h-64 overflow-y-auto">
                              {result.output.vocabulary.map((vocab: any, index: number) => (
                                <VocabularyCard
                                  key={index}
                                  vocab={vocab}
                                  onVocabSpeak={handleVocabSpeak}
                                  onContentSpeak={handleContentSpeak}
                                  playingVocabId={playingVocabId}
                                  playingContentId={playingContentId}
                                  isPlaying={isPlaying}
                                  compact={true}
                                  showActions={true}
                                  className=""
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {result.output.grammar && result.output.grammar.length > 0 && (
                          <div>
                            <h4 className="text-sm font-semibold text-gray-700 mb-2">
                              语法分析结果 ({result.output.grammar.length}个)
                            </h4>
                            <div className="space-y-2 max-h-64 overflow-y-auto">
                              {result.output.grammar.map((grammar: any, index: number) => (
                                <GrammarCard
                                  key={index}
                                  grammar={grammar}
                                  onGrammarSpeak={(g) => handleContentSpeak(g.pattern, `grammar-${g.id || index}`, 'ja-JP')}
                                  onContentSpeak={handleContentSpeak}
                                  playingGrammarId={playingContentId?.startsWith('grammar-') ? playingContentId : null}
                                  playingContentId={playingContentId}
                                  isPlaying={isPlaying}
                                  compact={true}
                                  showActions={true}
                                  className=""
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* JSON原始数据 */}
                        <div>
                          <h4 className="text-sm font-semibold text-gray-700 mb-2">JSON原始数据</h4>
                          <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                            {JSON.stringify(result.output, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
