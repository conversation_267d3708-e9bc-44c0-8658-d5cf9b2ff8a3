# API接口文档

## API概述

NHK日语学习平台采用Next.js全栈架构，主要API服务包括：
- **抓取管理API服务器**：端口3002，负责数据管理和抓取控制
- **Next.js集成API**：端口3000，RSS测试等功能已集成到主应用中

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息"
}
```

## Next.js 集成API (端口3000)

### 基础信息
- **基础URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **架构**: 集成在Next.js主应用中，通过Server Actions实现

### 2. 测试单个RSS源

**接口**: `POST /rss/test`

**描述**: 测试单个RSS源的有效性和内容解析

**请求参数**:
```json
{
  "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
  "fieldMappings": {
    "title": {
      "xpath_selector": "//item/title/text()",
      "is_required": true
    },
    "link": {
      "xpath_selector": "//item/link/text()",
      "is_required": true
    }
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "itemCount": 15,
  "sampleItems": [
    {
      "title": "新技術の開発について",
      "link": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
      "description": "最新の技術開発に関するニュース...",
      "pub_date": "2024-12-01T10:00:00Z"
    }
  ],
  "message": "✅ RSS源测试成功！解析到 15 个条目",
  "details": {
    "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
    "responseTime": "2.3s",
    "feedTitle": "NHKニュース",
    "feedDescription": "NHKのニュースサイト"
  }
}
```

### 3. 批量测试RSS源

**接口**: `POST /rss/test-batch`

**描述**: 批量测试多个RSS源

**请求参数**:
```json
{
  "urls": [
    "https://www3.nhk.or.jp/rss/news/cat0.xml",
    "https://www3.nhk.or.jp/rss/news/cat5.xml"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "results": [
    {
      "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
      "success": true,
      "itemCount": 15
    },
    {
      "url": "https://www3.nhk.or.jp/rss/news/cat5.xml",
      "success": false,
      "error": "连接超时"
    }
  ],
  "message": "批量测试完成，共测试 2 个RSS源"
}
```

## 抓取管理API (端口3002)

### 基础信息
- **基础URL**: `http://localhost:3002/api`
- **Content-Type**: `application/json`
- **CORS**: 支持跨域请求

### 说明

抓取管理API服务器主要负责数据抓取和管理功能。数据库统计、AI处理统计等功能已集成到Next.js主应用中，通过Server Actions实现，无需独立的HTTP API调用。

## 文章管理API

### 4. 获取文章列表

**接口**: `GET /articles`

**描述**: 获取文章列表，支持筛选和搜索

**查询参数**:
- `level`: 难度级别 (N5, N4, N3, N2, N1)
- `category`: 文章分类
- `search`: 搜索关键词
- `limit`: 返回数量限制 (默认50)

**请求示例**:
```
GET /articles?level=N3&category=科技&limit=20
```

**响应示例**:
```json
[
  {
    "id": 1,
    "title": "新しい技術の開発について",
    "subtitle": "最新技術の動向を解説",
    "content": "技術開発に関する詳細な内容...",
    "url": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
    "category": "科技",
    "level": "N3",
    "readTime": 5,
    "vocabularyCount": 25,
    "grammarPoints": 8,
    "publishTime": "2024-12-01",
    "rss_source_name": "NHK科技新闻",
    "ai_processing_status": "completed"
  }
]
```

### 5. 获取文章详情

**接口**: `GET /articles/:id`

**描述**: 获取单篇文章的详细信息，包括词汇和语法点

**响应示例**:
```json
{
  "id": 1,
  "title": "新しい技術の開発について",
  "content": "详细的文章内容...",
  "vocabulary": [
    {
      "id": 1,
      "word": "技術",
      "reading": "ぎじゅつ",
      "meaning_zh": "技术",
      "jlpt_level": "N3",
      "context": "新しい技術の開発",
      "is_key_vocabulary": true
    }
  ],
  "grammarPoints": [
    {
      "id": 1,
      "pattern": "について",
      "meaning_zh": "关于...",
      "explanation": "表示话题或对象的助词",
      "example_sentence": "技術について話す"
    }
  ],
  "translation": "关于新技术开发的中文翻译内容..."
}
```

## 词汇管理API

### 6. 获取词汇列表

**接口**: `GET /vocabulary`

**描述**: 获取词汇列表，支持筛选

**查询参数**:
- `level`: JLPT级别
- `status`: 学习状态 (new, learning, mastered, difficult)
- `search`: 搜索关键词

**响应示例**:
```json
[
  {
    "id": 1,
    "word": "技術",
    "reading": "ぎじゅつ",
    "meaning": "技术",
    "level": "N3",
    "status": "learning",
    "article_count": 5,
    "review_count": 3,
    "addedDate": "2024-11-15",
    "examples": [
      "新しい技術 - 新技术",
      "技術者 - 技术人员"
    ]
  }
]
```

### 7. 获取词汇统计

**接口**: `GET /vocabulary/stats`

**描述**: 获取词汇学习统计信息

**响应示例**:
```json
{
  "total": 2847,
  "byLevel": {
    "N5": 456,
    "N4": 623,
    "N3": 789,
    "N2": 567,
    "N1": 412
  },
  "mastered": 234,
  "learning": 567,
  "difficult": 89,
  "new": 1957
}
```

### 8. 更新词汇状态

**接口**: `PUT /vocabulary/:id/status`

**描述**: 更新词汇的学习状态

**请求参数**:
```json
{
  "status": "mastered"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "词汇状态更新成功"
}
```

## 学习统计API

### 9. 获取学习统计

**接口**: `GET /learning/stats`

**描述**: 获取用户学习统计信息

**响应示例**:
```json
{
  "continuousDays": 15,
  "totalVocabulary": 2847,
  "totalArticles": 156,
  "masteredItems": 234,
  "readArticles": 45,
  "averageLevel": 3,
  "weeklyActivity": [45, 60, 30, 75, 20, 0, 40],
  "totalStudyTime": 270
}
```

### 10. 获取用户进度

**接口**: `GET /learning/progress`

**描述**: 获取用户各级别的学习进度

**响应示例**:
```json
{
  "vocabularyProgress": {
    "N5": {
      "total": 456,
      "mastered": 234,
      "percentage": 51
    },
    "N4": {
      "total": 623,
      "mastered": 156,
      "percentage": 25
    }
  },
  "articleProgress": {
    "total_articles": 156,
    "read_articles": 45
  }
}
```

## 抓取控制API

### 11. 启动抓取任务

**接口**: `POST /scraping/start`

**描述**: 启动新闻抓取任务

**响应示例**:
```json
{
  "success": true,
  "sessionId": "scrape_1701432000_abc123",
  "message": "抓取任务已启动",
  "totalExpected": 50
}
```

### 12. 停止抓取任务

**接口**: `POST /scraping/stop`

**描述**: 停止正在运行的抓取任务

**请求参数**:
```json
{
  "sessionId": "scrape_1701432000_abc123"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "抓取任务已停止"
}
```

### 13. 获取抓取进度

**接口**: `GET /scraping/progress/:sessionId`

**描述**: 获取指定会话的抓取进度

**响应示例**:
```json
{
  "isRunning": true,
  "processedArticles": 25,
  "successCount": 23,
  "failCount": 2,
  "aiProcessingScheduled": 23,
  "currentStep": "处理中 (25/50)",
  "currentUrl": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
  "logs": [
    "[12:00:15] 开始抓取任务",
    "[12:00:16] 正在处理第 1 篇文章...",
    "[12:00:18] 文章处理成功: 新技術について"
  ]
}
```

### 14. 获取抓取会话历史

**接口**: `GET /scraping/sessions`

**描述**: 获取历史抓取会话记录

**响应示例**:
```json
[
  {
    "id": "scrape_1701432000_abc123",
    "startTime": "2024-12-01T12:00:00Z",
    "endTime": "2024-12-01T12:15:00Z",
    "status": "completed",
    "totalSources": 3,
    "totalArticles": 50,
    "successfulArticles": 47,
    "failedArticles": 3,
    "aiProcessingScheduled": 47
  }
]
```

## AI处理API

### 15. 启动AI处理器

**接口**: `POST /ai/start`

**描述**: 启动AI内容处理器

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理器已启动"
}
```

### 16. 停止AI处理器

**接口**: `POST /ai/stop`

**描述**: 停止AI内容处理器

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理器已停止"
}
```

### 17. 清空AI处理队列

**接口**: `DELETE /ai/queue/clear`

**描述**: 清空待处理的AI任务队列

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理队列已清空"
}
```

## RSS源管理API

### 18. 获取RSS源列表

**接口**: `GET /rss/sources`

**描述**: 获取所有RSS源配置

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "NHK科技新闻",
    "url": "https://www3.nhk.or.jp/rss/news/cat5.xml",
    "description": "NHK官方科技新闻RSS源",
    "category": "科技",
    "language": "ja",
    "is_active": 1,
    "max_articles": 15,
    "enable_ai_processing": 1,
    "content_selector": ".article-body, .content-body",
    "last_fetch_time": "2024-12-01T11:00:00Z",
    "last_fetch_count": 12,
    "total_fetched": 156,
    "success_rate": 0.95
  }
]
```

### 19. 添加RSS源

**接口**: `POST /rss/sources`

**描述**: 添加新的RSS源配置

**请求参数**:
```json
{
  "name": "新RSS源",
  "url": "https://example.com/rss.xml",
  "description": "RSS源描述",
  "category": "科技",
  "language": "ja",
  "max_articles": 20,
  "enable_ai_processing": true,
  "content_selector": ".article-body, .content-body"
}
```

**响应示例**:
```json
{
  "success": true,
  "sourceId": 2,
  "message": "RSS源添加成功"
}
```

## 数据管理API

### 20. 清空数据库

**接口**: `DELETE /database/clear`

**描述**: 清空所有数据库内容（危险操作）

**响应示例**:
```json
{
  "success": true,
  "message": "数据库已清空"
}
```

### 21. 导出数据

**接口**: `GET /database/export`

**描述**: 导出数据库内容为JSON格式

**响应示例**:
```json
{
  "exportTime": "2024-12-01T12:00:00Z",
  "version": "2.0.0",
  "data": {
    "articles": [...],
    "vocabulary": [...],
    "grammar_points": [...],
    "rss_sources": [...]
  }
}
```

## 错误代码说明

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
- `INVALID_PARAMS`: 参数验证失败
- `RSS_FETCH_FAILED`: RSS源获取失败
- `DATABASE_ERROR`: 数据库操作失败
- `AI_PROCESSING_ERROR`: AI处理失败
- `SCRAPING_IN_PROGRESS`: 抓取任务正在进行中

## 使用示例

### JavaScript客户端示例
```javascript
// 测试RSS源
const testRSS = async (url) => {
  try {
    const response = await fetch('http://localhost:3001/api/rss/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url })
    });
    
    const result = await response.json();
    console.log('RSS测试结果:', result);
    
  } catch (error) {
    console.error('请求失败:', error);
  }
};

// 获取文章列表
const getArticles = async (filters = {}) => {
  try {
    const params = new URLSearchParams(filters);
    const response = await fetch(`http://localhost:3002/api/articles?${params}`);
    const articles = await response.json();
    
    return articles;
    
  } catch (error) {
    console.error('获取文章失败:', error);
    return [];
  }
};

// 启动抓取任务
const startScraping = async () => {
  try {
    const response = await fetch('http://localhost:3002/api/scraping/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    const result = await response.json();
    console.log('抓取任务启动:', result);
    
    return result.sessionId;
    
  } catch (error) {
    console.error('启动抓取失败:', error);
  }
};
```

### Python客户端示例
```python
import requests
import json

class NHKLearningAPI:
    def __init__(self):
        self.rss_base_url = "http://localhost:3001/api"
        self.scraping_base_url = "http://localhost:3002/api"
    
    def test_rss_source(self, url, field_mappings=None):
        """测试RSS源"""
        payload = {"url": url}
        if field_mappings:
            payload["fieldMappings"] = field_mappings
            
        response = requests.post(
            f"{self.rss_base_url}/rss/test",
            json=payload
        )
        return response.json()
    
    def get_articles(self, **filters):
        """获取文章列表"""
        response = requests.get(
            f"{self.scraping_base_url}/articles",
            params=filters
        )
        return response.json()
    
    def get_database_stats(self):
        """获取数据库统计"""
        response = requests.get(f"{self.scraping_base_url}/database/stats")
        return response.json()

# 使用示例
api = NHKLearningAPI()

# 测试RSS源
result = api.test_rss_source("https://www3.nhk.or.jp/rss/news/cat0.xml")
print("RSS测试结果:", result)

# 获取N3级别的科技文章
articles = api.get_articles(level="N3", category="科技", limit=10)
print(f"找到 {len(articles)} 篇文章")

# 获取数据库统计
stats = api.get_database_stats()
print("数据库统计:", stats)
```

这个API文档提供了完整的接口说明和使用示例，帮助开发者快速理解和使用系统的各项功能。