import { NextResponse } from 'next/server';
import { analyzeGrammarText } from '@/ai/flows/grammar-analysis-flow';
import { GrammarAnalysisInput } from '@/ai/schemas/grammar-analysis-schema';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // 验证输入
    if (!body.text || typeof body.text !== 'string') {
      return NextResponse.json(
        { error: '文本是必需的' },
        { status: 400 }
      );
    }

    if (body.text.trim().length === 0) {
      return NextResponse.json(
        { error: '文本不能为空' },
        { status: 400 }
      );
    }

    // 构建分析输入
    const input: GrammarAnalysisInput = {
      text: body.text.trim(),
      modelName: body.modelName || 'googleai/gemini-2.5-flash'
    };

    console.log('开始AI语法分析测试:', {
      textLength: input.text.length,
      modelName: input.modelName,
      textPreview: input.text.substring(0, 50) + (input.text.length > 50 ? '...' : '')
    });

    const startTime = Date.now();
    
    // 调用AI分析
    const result = await analyzeGrammarText(input);
    
    const processingTime = Date.now() - startTime;
    
    console.log('AI语法分析测试完成:', {
      processingTime,
      grammarPointsFound: Array.isArray(result) ? result.length : 0,
      hasResult: !!result
    });

    // 验证结果格式
    if (!Array.isArray(result)) {
      console.warn('语法分析结果不是数组格式:', typeof result);
    }

    // 添加测试元数据
    const response = {
      grammarPoints: result,
      _testMetadata: {
        processingTime,
        timestamp: new Date().toISOString(),
        modelUsed: input.modelName,
        inputStats: {
          textLength: input.text.length,
          characterCount: input.text.length,
          wordCount: input.text.split(/\s+/).length
        },
        outputStats: {
          grammarPointsCount: Array.isArray(result) ? result.length : 0,
          isValidArray: Array.isArray(result),
          hasExamples: Array.isArray(result) && result.every(point => 
            point.examples && Array.isArray(point.examples) && point.examples.length > 0
          ),
          allHaveThreeExamples: Array.isArray(result) && result.every(point => 
            point.examples && Array.isArray(point.examples) && point.examples.length === 3
          )
        }
      }
    };

    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('AI语法分析测试失败:', error);
    
    return NextResponse.json(
      { 
        error: '分析失败', 
        details: error.message,
        _testMetadata: {
          timestamp: new Date().toISOString(),
          errorType: error.constructor.name
        }
      },
      { status: 500 }
    );
  }
}
