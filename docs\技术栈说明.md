# 技术栈说明

## 前端技术栈

### 核心框架
- **Next.js 15.3.3**
  - 现代化的React全栈框架
  - 支持App Router和Server Components
  - 提供服务端渲染(SSR)和静态生成(SSG)
  - 内置优化和性能提升

- **React 19.0.0-rc.0**
  - 最新版本的React框架
  - 支持函数组件和现代Hooks
  - 提供高效的虚拟DOM和组件化开发体验

- **TypeScript 5.5.3**
  - JavaScript的超集，提供静态类型检查
  - 提高代码质量和开发效率
  - 增强IDE支持和代码提示功能

### UI和样式
- **Tailwind CSS 3.4.1**
  - 实用优先的CSS框架
  - 提供丰富的预设样式类
  - 支持响应式设计和自定义主题

- **Lucide React 0.408.0**
  - 现代化的图标库
  - 提供一致的图标设计风格
  - 支持SVG图标和自定义样式

### 开发工具
- **ESLint**
  - JavaScript/TypeScript代码检查工具
  - 确保代码质量和一致性
  - 集成React和TypeScript规则

- **PostCSS 8.4.35 + Autoprefixer 10.4.18**
  - CSS后处理工具
  - 自动添加浏览器前缀
  - 优化CSS输出

## 后端技术栈

### 运行环境
- **Node.js**
  - JavaScript运行时环境
  - 支持ES模块和现代JavaScript特性
  - 提供丰富的npm生态系统

### Web框架
- **Next.js Server Actions**
  - 服务端函数，直接在组件中调用
  - 类型安全的服务端逻辑处理
  - 自动优化和缓存

### 数据库
- **Prisma ORM 5.17.0**
  - 现代化的数据库访问层
  - 类型安全的数据库操作
  - 自动生成类型定义和迁移

- **SQLite**
  - 轻量级的关系型数据库
  - 无需单独的数据库服务器
  - 支持ACID事务和SQL标准

### 网络请求
- **node-fetch 3.3.2**
  - Node.js环境下的fetch API实现
  - 用于HTTP请求和RSS内容抓取
  - 支持Promise和async/await

### HTML解析
- **Cheerio 1.0.0-rc.12**
  - 服务端的jQuery实现
  - 用于HTML内容解析和DOM操作
  - 支持CSS选择器和jQuery语法

### XML处理
- **xmldom 0.6.0 + xpath 0.0.32**
  - XML文档解析和XPath查询
  - 用于RSS XML内容的精确提取
  - 支持复杂的XML结构处理

### 网络代理
- **https-proxy-agent 7.0.2**
  - HTTPS代理支持
  - 用于网络请求的代理配置
  - 支持企业网络环境

### 进程管理
- **concurrently 7.6.0**
  - 并发运行多个npm脚本
  - 用于同时启动前端和后端服务
  - 提供彩色输出和进程管理

### 跨域支持
- **cors 2.8.5**
  - 跨域资源共享中间件
  - 支持前后端分离架构
  - 配置灵活的跨域策略

## 开发环境配置

### 包管理器
- **npm**
  - Node.js官方包管理器
  - 管理项目依赖和脚本
  - 支持语义化版本控制

### 代码质量工具
- **TypeScript ESLint 8.3.0**
  - TypeScript专用的ESLint配置
  - 提供类型安全的代码检查
  - 集成React Hooks规则

### 构建配置
- **Next.js内置构建系统**
  - 基于Webpack和SWC的优化构建
  - 支持JSX转换和热更新
  - 自动代码分割和优化

## 数据存储架构

### 数据库设计
- **关系型数据库**：使用SQLite存储结构化数据
- **文件存储**：媒体文件存储在本地文件系统
- **缓存机制**：浏览器localStorage用于用户状态管理

### 数据流架构
```
RSS源 → 抓取器 → 内容解析 → 数据库存储 → API服务 → 前端展示
                     ↓
              AI处理队列 → AI分析 → 结果存储
```

## 部署架构

### 开发环境
- **Next.js开发服务器**：集成的全栈开发服务器 (端口3000)
- **Server Actions**：服务端逻辑处理
- **热更新**：实时代码更新和页面刷新

### 生产环境
- **Next.js应用**：优化后的全栈应用
- **静态资源**：自动优化的静态文件
- **数据库**：SQLite文件数据库
- **媒体文件**：本地文件系统存储

## 安全考虑

### 前端安全
- **XSS防护**：React自动转义用户输入
- **CSRF防护**：API请求验证
- **内容安全策略**：限制外部资源加载

### 后端安全
- **输入验证**：严格的参数验证和清理
- **SQL注入防护**：参数化查询
- **文件上传安全**：限制文件类型和大小

### 数据安全
- **用户隔离**：每个用户的数据独立存储
- **本地存储**：敏感数据不传输到外部服务器
- **备份机制**：支持数据导出和备份