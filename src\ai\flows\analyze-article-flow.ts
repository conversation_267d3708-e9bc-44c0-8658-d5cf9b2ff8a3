
'use server';
/**
 * @fileOverview A flow for analyzing article content.
 *
 * This flow translates the article, extracts key vocabulary, and analyzes grammar points.
 */

import { ai } from '@/ai/genkit';
import {
  ArticleAnalysisInputSchema,
  ArticleAnalysisOutputSchema,
  type ArticleAnalysisInput,
  type ArticleAnalysisOutput,
} from '../schemas/analyze-article-schema';
import { dbManager } from '@/lib/server/database';

// This new schema is for the prompt's template variables, excluding the modelName.
const ArticlePromptInputSchema = ArticleAnalysisInputSchema.omit({ modelName: true });

// 动态创建提示词的函数
async function createAnalysisPrompt() {
  const [systemPrompt, mainPrompt] = await Promise.all([
    dbManager.getAIPromptByName('article_analysis_system'),
    dbManager.getAIPromptByName('article_analysis_prompt')
  ]);

  if (!systemPrompt || !mainPrompt) {
    throw new Error('无法从数据库获取文章分析提示词');
  }

  return ai.definePrompt({
    name: 'analyzeArticlePrompt',
    input: { schema: ArticlePromptInputSchema },
    output: { schema: ArticleAnalysisOutputSchema },
    system: systemPrompt.content,
    prompt: mainPrompt.content,
    config: {
      safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_ONLY_HIGH',
          },
      ],
    },
  });
}

const analyzeArticleFlow = ai.defineFlow(
  {
    name: 'analyzeArticleFlow',
    inputSchema: ArticleAnalysisInputSchema, // The flow still accepts the modelName
    outputSchema: ArticleAnalysisOutputSchema,
  },
  async (input) => {
    // Separate the modelName from the data needed for the prompt template.
    const { modelName, ...promptData } = input;
    const modelToUse = modelName || 'googleai/gemini-2.0-flash';

    // 获取AI温度设置
    const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
    const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

    // 动态创建提示词
    const analysisPrompt = await createAnalysisPrompt();

    // Call the prompt function directly. This is the correct pattern.
    // Pass the prompt data as the first argument, and generation options (like the model) as the second.
    const { output } = await analysisPrompt(promptData, {
      model: modelToUse,
      config: {
        temperature: temperature
      }
    });

    if (!output) {
      throw new Error("AI analysis failed to produce an output.");
    }
    return output;
  }
);

export async function analyzeArticle(input: ArticleAnalysisInput): Promise<ArticleAnalysisOutput> {
  return analyzeArticleFlow(input);
}
