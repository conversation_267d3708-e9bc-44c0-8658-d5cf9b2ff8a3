# NHK新闻抓取系统重构实施方案 (更新版)

## 项目概述

本文档详细描述了将现有的直接网页抓取系统重构为基于RSS源的抓取系统的完整实施方案。新系统将通过解析RSS源获取文章基本信息，然后根据原文URL抓取完整内容。

**重要更新：**
- 不考虑现有数据库兼容性，设计全新的最优数据库结构
- 支持灵活的RSS源字段映射配置
- 为每个RSS源独立设置抓取数量限制
- **新增AI处理控制功能，支持可选的AI内容分析**
- **重新设计数据库表结构，分离原始内容和AI处理结果**

## 1. 重构背景与目标

### 1.1 重构原因
- 直接网页抓取容易受到网站结构变化影响
- RSS源提供更稳定和标准化的数据格式
- 不同RSS源的结构可能存在差异，需要灵活的字段映射
- 需要精确控制每个RSS源的抓取数量
- 需要可选的AI处理功能来增强内容分析

### 1.2 重构目标
- 实现基于RSS源的新闻抓取
- 设计最优的全新数据库结构
- 提供灵活的RSS源字段映射配置
- 支持每个RSS源独立的抓取数量控制
- 集成可选的AI内容处理功能
- 确保抓取内容的完整性和准确性

## 2. 技术架构设计

### 2.1 系统架构图
```
用户界面 (React)
    ↓
RSS源管理模块 ← → 字段映射配置模块 ← → AI处理控制模块
    ↓                    ↓                    ↓
RSS解析器 ← → 内容抓取器 ← → 抓取数量控制 ← → AI内容分析器
    ↓              ↓              ↓              ↓
数据处理模块 ← → 媒体下载器 ← → 进度监控 ← → AI结果存储
    ↓              ↓              ↓              ↓
SQLite数据库 ← → 本地文件存储 ← → 日志系统 ← → AI处理队列
```

### 2.2 核心模块
1. **RSS源管理模块**: 管理RSS源配置和字段映射
2. **字段映射配置模块**: 灵活配置RSS字段与数据库字段的对应关系
3. **AI处理控制模块**: 控制是否启用AI分析功能
4. **RSS解析器**: 根据配置解析RSS XML内容
5. **内容抓取器**: 根据URL抓取完整文章
6. **AI内容分析器**: 可选的AI处理模块
7. **抓取数量控制模块**: 控制每个RSS源的抓取数量
8. **数据处理模块**: 处理和分析文章内容
9. **媒体下载器**: 下载图片和视频文件

## 3. 全新数据库设计

### 3.1 RSS源配置表 (rss_sources)
```sql
CREATE TABLE rss_sources (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- RSS源名称
  url TEXT UNIQUE NOT NULL,              -- RSS源URL
  description TEXT,                      -- RSS源描述
  category TEXT,                         -- 分类 (科技/经济/社会等)
  language TEXT DEFAULT 'ja',            -- 语言代码
  is_active BOOLEAN DEFAULT 1,           -- 是否启用
  max_articles INTEGER DEFAULT 10,       -- 每次抓取的最大文章数
  enable_ai_processing BOOLEAN DEFAULT 0, -- 是否启用AI处理
  content_selector TEXT,                 -- 内容选择器
  last_fetch_time DATETIME,              -- 最后抓取时间
  last_fetch_count INTEGER DEFAULT 0,    -- 最后一次抓取的文章数
  total_fetched INTEGER DEFAULT 0,       -- 总抓取文章数
  success_rate REAL DEFAULT 0.0,         -- 成功率
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**重要变更**: 抓取间隔配置已移至系统级别，通过 `system_settings` 表统一管理。

### 3.2 RSS字段映射配置表 (rss_field_mappings)
```sql
CREATE TABLE rss_field_mappings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER NOT NULL,        -- RSS源ID
  field_name TEXT NOT NULL,               -- 目标字段名 (title/link/description/pub_date/image_url)
  xpath_selector TEXT,                    -- XPath选择器
  css_selector TEXT,                      -- CSS选择器
  attribute_name TEXT,                    -- 属性名 (如src, href等)
  default_value TEXT,                     -- 默认值
  is_required BOOLEAN DEFAULT 0,         -- 是否必需字段
  transform_rule TEXT,                    -- 数据转换规则 (JSON格式)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id) ON DELETE CASCADE
);
```

### 3.3 文章基础信息表 (articles)
```sql
CREATE TABLE articles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER,                 -- RSS源ID
  guid TEXT UNIQUE,                      -- RSS条目唯一标识
  title TEXT NOT NULL,                   -- 文章标题
  subtitle TEXT,                         -- 文章副标题/摘要
  content TEXT NOT NULL,                 -- 文章正文
  content_html TEXT,                     -- 原始HTML内容
  url TEXT UNIQUE NOT NULL,              -- 原文链接
  category TEXT,                         -- 文章分类
  tags TEXT,                             -- 标签 (JSON数组)
  publish_time DATETIME,                 -- 发布时间
  fetch_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 抓取时间
  
  -- 媒体文件
  featured_image_url TEXT,               -- 特色图片URL
  featured_image_path TEXT,              -- 本地图片路径
  video_url TEXT,                        -- 视频URL
  video_path TEXT,                       -- 本地视频路径
  audio_url TEXT,                        -- 音频URL
  audio_path TEXT,                       -- 本地音频路径
  
  -- AI处理状态
  ai_processing_status TEXT DEFAULT 'pending', -- AI处理状态 (pending/processing/completed/failed/disabled)
  ai_processed_at DATETIME,              -- AI处理完成时间
  ai_processing_error TEXT,              -- AI处理错误信息
  
  -- 状态信息
  processing_status TEXT DEFAULT 'pending', -- 抓取处理状态 (pending/processing/completed/failed)
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id)
);
```



### 3.4 文章翻译表 (article_translations)
```sql
CREATE TABLE article_translations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,           -- 文章ID
  language_code TEXT NOT NULL,           -- 语言代码 (zh/en等)
  translated_title TEXT,                 -- 翻译标题
  translated_subtitle TEXT,              -- 翻译副标题
  translated_content TEXT,               -- 翻译正文
  translation_method TEXT DEFAULT 'ai',  -- 翻译方式 (ai/manual)
  quality_score REAL,                    -- 翻译质量评分
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  UNIQUE(article_id, language_code)
);
```

### 3.5 词汇表 (vocabulary)
```sql
CREATE TABLE vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  word TEXT NOT NULL,                    -- 词汇
  reading TEXT,                          -- 读音 (假名)
  meaning_zh TEXT,                       -- 中文释义
  meaning_en TEXT,                       -- 英文释义
  part_of_speech TEXT,                   -- 词性
  jlpt_level TEXT,                       -- JLPT级别
  frequency_rank INTEGER,                -- 使用频率排名
  difficulty_score REAL,                 -- 难度评分
  extraction_method TEXT DEFAULT 'ai',   -- 提取方式 (ai/manual)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(word, reading)
);
```

### 3.6 文章词汇关联表 (article_vocabulary)
```sql
CREATE TABLE article_vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  vocabulary_id INTEGER NOT NULL,
  position_start INTEGER,                -- 在文章中的起始位置
  position_end INTEGER,                  -- 在文章中的结束位置
  context TEXT,                          -- 上下文
  is_key_vocabulary BOOLEAN DEFAULT 0,   -- 是否为重点词汇
  extraction_confidence REAL,            -- 提取置信度
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary (id),
  UNIQUE(article_id, vocabulary_id, position_start)
);
```

### 3.7 语法点表 (grammar_points)
```sql
CREATE TABLE grammar_points (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  pattern TEXT NOT NULL,                 -- 语法模式
  name TEXT,                             -- 语法名称
  meaning_zh TEXT,                       -- 中文解释
  meaning_en TEXT,                       -- 英文解释
  explanation TEXT,                      -- 详细说明
  jlpt_level TEXT,                       -- JLPT级别
  difficulty_score REAL,                 -- 难度评分
  usage_frequency INTEGER DEFAULT 0,     -- 使用频率
  extraction_method TEXT DEFAULT 'ai',   -- 提取方式 (ai/manual)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(pattern)
);
```

### 3.8 文章语法点关联表 (article_grammar_points)
```sql
CREATE TABLE article_grammar_points (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  grammar_point_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  FOREIGN KEY (grammar_point_id) REFERENCES grammar_points (id),
  UNIQUE(article_id, grammar_point_id)
);
```

### 3.9 用户学习记录表 (user_learning_records)
```sql
CREATE TABLE user_learning_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT DEFAULT 'default_user',   -- 用户ID (暂时使用默认用户)
  article_id INTEGER,
  vocabulary_id INTEGER,
  grammar_point_id INTEGER,
  record_type TEXT NOT NULL,             -- 记录类型 (article/vocabulary/grammar)
  status TEXT DEFAULT 'new',             -- 学习状态 (new/learning/reviewing/mastered)
  proficiency_level INTEGER DEFAULT 0,   -- 熟练度等级 (0-5)
  review_count INTEGER DEFAULT 0,        -- 复习次数
  correct_count INTEGER DEFAULT 0,       -- 正确次数
  last_reviewed_at DATETIME,             -- 最后复习时间
  next_review_at DATETIME,               -- 下次复习时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id),
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary (id),
  FOREIGN KEY (grammar_point_id) REFERENCES grammar_points (id)
);
```

### 3.10 抓取日志表 (scraping_logs)
```sql
CREATE TABLE scraping_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER,
  session_id TEXT,                       -- 抓取会话ID
  log_level TEXT DEFAULT 'INFO',         -- 日志级别 (DEBUG/INFO/WARN/ERROR)
  message TEXT NOT NULL,                 -- 日志消息
  details TEXT,                          -- 详细信息 (JSON格式)
  url TEXT,                              -- 相关URL
  processing_time REAL,                  -- 处理时间(秒)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id)
);
```

### 3.11 AI处理队列表 (ai_processing_queue)
```sql
CREATE TABLE ai_processing_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,           -- 文章ID
  processing_type TEXT NOT NULL,         -- 处理类型 (translation/vocabulary/grammar/summary)
  priority INTEGER DEFAULT 0,            -- 优先级
  status TEXT DEFAULT 'pending',         -- 状态 (pending/processing/completed/failed)
  retry_count INTEGER DEFAULT 0,         -- 重试次数
  error_message TEXT,                    -- 错误信息
  scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 计划处理时间
  started_at DATETIME,                   -- 开始处理时间
  completed_at DATETIME,                 -- 完成时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE
);
```

## 4. AI处理控制系统设计

### 4.1 AI处理配置
```javascript
const aiProcessingConfig = {
  // 全局AI处理开关
  globalEnabled: true,
  
  // 各RSS源的AI处理设置
  rssSourceSettings: {
    enableAiProcessing: false,  // 是否启用AI处理
    processingTypes: [          // 启用的处理类型
      'translation',            // 翻译
      'vocabulary_extraction',  // 词汇提取
      'grammar_analysis',       // 语法分析
      'difficulty_assessment',  // 难度评估
      'summary_generation'      // 摘要生成
    ],
    processingMode: 'async',    // 处理模式 (sync/async)
    batchSize: 5,              // 批处理大小
    retryLimit: 3              // 重试限制
  },
  
  // AI模型配置
  modelSettings: {
    translationModel: 'gpt-4',
    vocabularyModel: 'gpt-3.5-turbo',
    grammarModel: 'gpt-3.5-turbo',
    summaryModel: 'gpt-3.5-turbo'
  }
};
```

### 4.2 AI处理流程
```javascript
class AIContentProcessor {
  constructor(config) {
    this.config = config;
    this.processingQueue = new AIProcessingQueue();
  }

  async processArticle(article, rssSource) {
    // 检查是否启用AI处理
    if (!rssSource.enable_ai_processing) {
      await this.updateArticleAIStatus(article.id, 'disabled');
      return;
    }

    // 更新处理状态
    await this.updateArticleAIStatus(article.id, 'processing');

    try {
      const processingTasks = [];

      // 根据配置添加处理任务
      if (this.config.processingTypes.includes('translation')) {
        processingTasks.push(this.translateContent(article));
      }

      if (this.config.processingTypes.includes('vocabulary_extraction')) {
        processingTasks.push(this.extractVocabulary(article));
      }

      if (this.config.processingTypes.includes('grammar_analysis')) {
        processingTasks.push(this.analyzeGrammar(article));
      }

      // 执行所有处理任务
      const results = await Promise.allSettled(processingTasks);
      
      // 保存处理结果
      await this.saveProcessingResults(article.id, results);
      
      // 更新完成状态
      await this.updateArticleAIStatus(article.id, 'completed');

    } catch (error) {
      await this.updateArticleAIStatus(article.id, 'failed', error.message);
      throw error;
    }
  }

  async translateContent(article) {
    // AI翻译逻辑
    const translation = await this.callAITranslation(article.content);
    
    return {
      type: 'translation',
      data: {
        title: await this.callAITranslation(article.title),
        subtitle: article.subtitle ? await this.callAITranslation(article.subtitle) : null,
        content: translation
      }
    };
  }

  async extractVocabulary(article) {
    // AI词汇提取逻辑
    const vocabulary = await this.callAIVocabularyExtraction(article.content);
    
    return {
      type: 'vocabulary',
      data: vocabulary
    };
  }

  async analyzeGrammar(article) {
    // AI语法分析逻辑
    const grammarPoints = await this.callAIGrammarAnalysis(article.content);
    
    return {
      type: 'grammar',
      data: grammarPoints
    };
  }
}
```

## 5. RSS源管理界面设计

### 5.1 RSS源列表界面
```
RSS源管理页面
├── 顶部操作栏
│   ├── 添加RSS源按钮
│   ├── 批量操作下拉菜单
│   ├── 搜索框
│   └── 筛选器 (分类/状态/语言)
├── RSS源卡片列表
│   ├── 基本信息区域
│   │   ├── 源名称 (可编辑)
│   │   ├── URL地址 (可编辑)
│   │   ├── 描述 (可编辑)
│   │   ├── 分类标签
│   │   └── 状态开关
│   ├── 抓取配置区域
│   │   ├── 最大文章数滑块 (1-100)
│   │   ├── 抓取间隔设置
│   │   └── 语言选择
│   ├── AI处理配置区域 ⭐ 新增
│   │   ├── AI处理开关
│   │   ├── 处理类型选择 (翻译/词汇/语法/摘要)
│   │   ├── 处理模式选择 (同步/异步)
│   │   └── 批处理大小设置
│   ├── 统计信息区域
│   │   ├── 总抓取数量
│   │   ├── 最后抓取时间
│   │   ├── 成功率指示器
│   │   ├── 最近抓取数量
│   │   └── AI处理进度 ⭐ 新增
│   ├── 字段映射配置区域
│   │   ├── 标题字段映射
│   │   ├── 链接字段映射
│   │   ├── 描述字段映射
│   │   ├── 发布时间字段映射
│   │   ├── 图片字段映射
│   │   └── 自定义字段映射
│   └── 操作按钮区域
│       ├── 测试RSS源
│       ├── 立即抓取
│       ├── AI处理队列 ⭐ 新增
│       ├── 查看日志
│       ├── 编辑配置
│       └── 删除源
└── 分页控件
```

### 5.2 AI处理配置界面 ⭐ 新增
```
AI处理配置弹窗
├── 全局AI设置
│   ├── 全局AI处理开关
│   ├── 默认AI模型选择
│   ├── API配置 (密钥/端点)
│   └── 并发处理限制
├── RSS源AI配置
│   ├── 启用AI处理开关
│   ├── 处理类型选择
│   │   ├── ☑ 内容翻译
│   │   ├── ☑ 词汇提取
│   │   ├── ☑ 语法分析
│   │   ├── ☑ 难度评估
│   │   └── ☑ 摘要生成
│   ├── 处理优先级设置
│   ├── 批处理配置
│   │   ├── 批处理大小
│   │   ├── 处理间隔
│   │   └── 重试次数
│   └── 质量控制
│       ├── 最低置信度阈值
│       ├── 结果验证规则
│       └── 人工审核触发条件
├── 处理队列监控
│   ├── 队列状态显示
│   ├── 处理进度条
│   ├── 错误统计
│   └── 性能指标
└── 操作按钮
    ├── 保存配置
    ├── 测试AI连接
    ├── 清空队列
    └── 取消
```

## 6. 核心功能实现

### 6.1 带AI控制的抓取控制器
```javascript
class EnhancedScrapingController {
  constructor(rssSource) {
    this.rssSource = rssSource;
    this.maxArticles = rssSource.max_articles;
    this.enableAI = rssSource.enable_ai_processing;
    this.aiProcessor = new AIContentProcessor();
    this.processedCount = 0;
    this.successCount = 0;
    this.failCount = 0;
  }

  async scrapeRSSSource() {
    const rssItems = await this.fetchRSSItems();
    const filteredItems = this.applyFilters(rssItems);
    const limitedItems = this.applyLimit(filteredItems);
    
    for (const item of limitedItems) {
      try {
        // 1. 抓取基础文章内容
        const article = await this.processRSSItem(item);
        
        // 2. 可选的AI处理
        if (this.enableAI && article) {
          await this.scheduleAIProcessing(article);
        }
        
        this.successCount++;
      } catch (error) {
        this.failCount++;
        console.error('Processing failed:', error);
      }
      
      this.processedCount++;
      this.updateProgress();
      
      // 请求间隔控制
      await this.delay(this.rssSource.fetch_interval);
    }
    
    return {
      total: limitedItems.length,
      success: this.successCount,
      failed: this.failCount,
      aiProcessingScheduled: this.enableAI ? this.successCount : 0
    };
  }

  async scheduleAIProcessing(article) {
    // 将文章添加到AI处理队列
    const processingTypes = ['translation', 'vocabulary_extraction', 'grammar_analysis'];
    
    for (const type of processingTypes) {
      await this.addToAIQueue(article.id, type);
    }
  }

  async addToAIQueue(articleId, processingType) {
    const db = this.getDatabase();
    
    const query = `
      INSERT INTO ai_processing_queue (article_id, processing_type, priority)
      VALUES (?, ?, ?)
    `;
    
    return new Promise((resolve, reject) => {
      db.run(query, [articleId, processingType, 0], function(err) {
        if (err) reject(err);
        else resolve(this.lastID);
      });
    });
  }
}
```

## 7. 实施计划

### 7.1 第一阶段：数据库重构 
- 设计并创建新的数据库结构
- 实现数据库初始化脚本
- 创建数据访问层 (DAO)
- 编写数据库操作的单元测试

### 7.2 第二阶段：RSS解析核心 (2-3周)
- 实现RSS源管理功能
- 开发字段映射配置系统
- 创建RSS解析器
- 实现抓取数量控制逻辑
- 开发内容抓取器

### 7.3 第三阶段：AI处理系统 (2-3周) ⭐ 新增
- 设计AI处理控制接口
- 实现AI处理队列系统
- 开发AI内容分析模块
- 创建AI结果存储机制
- 实现AI处理监控功能

### 7.4 第四阶段：用户界面开发 (2-3周)
- 设计并实现RSS源管理界面
- 开发字段映射配置界面
- 创建AI处理配置界面 ⭐ 新增
- 实现抓取进度监控界面
- 添加AI处理队列监控 ⭐ 新增
- 实现抓取日志查看功能
- 添加批量操作功能

### 7.5 第五阶段：测试与优化 (1-2周)
- 系统集成测试
- AI处理功能测试 ⭐ 新增
- 性能优化
- 错误处理完善
- 用户体验优化
- 文档编写

## 8. 预期RSS源示例配置

### 8.1 NHK新闻RSS源配置 (启用AI处理)
```json
{
  "name": "NHK新闻",
  "url": "https://www3.nhk.or.jp/news/article7",
  "category": "综合新闻",
  "max_articles": 20,
  "enable_ai_processing": true,
  "ai_processing_types": [
    "translation",
    "vocabulary_extraction", 
    "grammar_analysis",
    "difficulty_assessment"
  ],
  "field_mappings": {
    "title": {
      "xpath": "//item/title/text()",
      "isRequired": true,
      "transformRule": {
        "type": "text",
        "trim": true,
        "maxLength": 200
      }
    },
    "link": {
      "xpath": "//item/link/text()",
      "isRequired": true,
      "transformRule": {
        "type": "url",
        "makeAbsolute": true
      }
    },
    "description": {
      "xpath": "//item/description/text()",
      "isRequired": false,
      "transformRule": {
        "type": "html",
        "stripTags": true
      }
    },
    "pub_date": {
      "xpath": "//item/pubDate/text()",
      "isRequired": false,
      "transformRule": {
        "type": "datetime",
        "inputFormat": "RFC2822"
      }
    },
    "image_url": {
      "xpath": "//item/enclosure[@type='image/jpeg']/@url",
      "isRequired": false,
      "transformRule": {
        "type": "url",
        "makeAbsolute": true
      }
    }
  }
}
```

## 9. 总结

本更新版重构方案通过以下关键改进，将显著提升系统的智能化水平和可维护性：

### 9.1 核心优势
1. **全新数据库设计**: 分离原始内容和AI处理结果，支持灵活的数据管理
2. **灵活字段映射**: 支持不同RSS源的结构差异，通过可视化配置适应各种格式
3. **精确数量控制**: 为每个RSS源独立设置抓取数量，避免资源浪费
4. **可选AI处理**: 灵活控制是否启用AI分析，支持渐进式功能升级 ⭐
5. **智能内容分析**: AI驱动的翻译、词汇提取、语法分析等功能 ⭐
6. **异步处理队列**: 高效的AI处理队列系统，支持批处理和重试 ⭐
7. **完整的学习数据**: 支持词汇、语法点的详细记录和学习进度跟踪
8. **强大的监控系统**: 详细的日志记录和AI处理进度监控 ⭐

### 9.2 技术特色
- 基于XPath和CSS选择器的灵活字段提取
- 支持多种数据转换规则
- 可选的AI内容处理管道 ⭐
- 智能的处理队列和重试机制 ⭐
- 可视化的RSS源和AI配置界面
- 实时的抓取和AI处理进度监控
- 完善的错误处理和重试机制

### 9.3 AI处理能力 ⭐
- **内容翻译**: 自动将日语文章翻译为中文
- **词汇提取**: 智能识别和提取重点词汇
- **语法分析**: 自动分析文章中的语法点
- **难度评估**: AI评估文章的学习难度级别
- **摘要生成**: 自动生成文章摘要
- **质量控制**: 置信度评分和人工审核机制

通过这个重构方案，系统将能够：
- 🎯 **精确控制**每个RSS源的抓取数量和AI处理
- 🔧 **灵活配置**不同RSS源的字段映射和AI功能
- 🤖 **智能分析**利用AI提升内容的学习价值 ⭐
- 📈 **全面跟踪**学习进度和AI处理状态
- 🖥️ **直观管理**通过可视化界面配置一切
- ⚡ **高效处理**异步AI处理队列确保系统响应性 ⭐

这个方案为后续的AI功能扩展奠定了坚实的基础，可以根据需要逐步添加更多的AI分析功能。