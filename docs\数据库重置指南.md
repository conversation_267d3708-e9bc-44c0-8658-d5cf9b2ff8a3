# 数据库重置指南

## 概述

本指南提供了重置数据库的不同方案，适用于不同的场景需求。

## 重置方案

### 方案1：完全重置（推荐）

**适用场景**：
- 想要清理所有迁移历史
- 数据库结构有重大变更
- 开发环境需要从头开始

**执行步骤**：
```bash
# 停止开发服务器
# 执行完全重置
npm run db:reset-complete
```

**此方案会执行以下操作**：
1. ✅ 删除 `prisma/migrations` 文件夹中的所有迁移文件
2. ✅ 删除现有数据库文件 `data/nhk_news_new.db`
3. ✅ 重新生成 Prisma 客户端
4. ✅ 创建新的初始迁移
5. ✅ 填充种子数据（管理员用户、AI提示词等）

### 方案2：保留迁移历史的重置

**适用场景**：
- 只想清空数据，保留迁移历史
- 生产环境的数据重置

**执行步骤**：
```bash
# 重置数据但保留迁移文件
npm run db:reset
```

### 方案3：手动重置

**适用场景**：
- 需要更精细的控制
- 出现问题时的故障排除

**执行步骤**：
```bash
# 1. 停止所有服务
# 2. 手动删除迁移文件夹
rm -rf prisma/migrations

# 3. 手动删除数据库文件
rm data/nhk_news_new.db

# 4. 重新生成客户端
npx prisma generate

# 5. 创建新迁移
npx prisma migrate dev --name init

# 6. 填充种子数据
npx prisma db seed
```

## 重要注意事项

### ⚠️ 数据丢失警告
- **所有用户数据将被永久删除**
- **所有文章、词汇、学习记录将被清空**
- **只有种子数据会被重新创建**

### 📋 重置前检查清单
- [ ] 确认不需要保留任何现有数据
- [ ] 停止所有正在运行的开发服务器
- [ ] 备份重要数据（如果需要）
- [ ] 确保有网络连接（用于下载依赖）

### 🔄 重置后验证
重置完成后，可以通过以下方式验证：

1. **检查数据库文件**：
   ```bash
   ls -la data/nhk_news_new.db
   ```

2. **检查迁移文件**：
   ```bash
   ls -la prisma/migrations/
   ```

3. **启动应用验证**：
   ```bash
   npm run dev
   ```

4. **访问应用**：
   - 打开 http://localhost:3001
   - 检查是否能正常登录（<EMAIL> / admin123）
   - 验证基本功能是否正常

## 故障排除

### 问题1：权限错误
```bash
# Windows
# 以管理员身份运行命令提示符

# Linux/Mac
sudo npm run db:reset-complete
```

### 问题2：文件被占用
```bash
# 确保停止所有Node.js进程
pkill -f node

# 然后重新执行重置命令
npm run db:reset-complete
```

### 问题3：Prisma客户端错误
```bash
# 重新生成客户端
npx prisma generate

# 如果还有问题，删除node_modules重新安装
rm -rf node_modules
npm install
```

## 种子数据说明

重置后会自动创建以下种子数据：

### 用户数据
- **管理员用户**：
  - 邮箱：<EMAIL>
  - 密码：admin123
  - 角色：admin

### AI提示词
- 文章分析提示词
- 语法分析提示词
- AI助教对话提示词

### 系统设置
- 默认配置项
- API密钥配置

## 相关命令参考

```bash
# 查看数据库状态
npx prisma db push --accept-data-loss

# 查看迁移状态
npx prisma migrate status

# 重新生成客户端
npx prisma generate

# 查看数据库内容（开发工具）
npx prisma studio
```
