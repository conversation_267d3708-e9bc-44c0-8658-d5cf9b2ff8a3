# 功能说明文档目录

本目录包含NHK日语学习平台所有功能模块的详细说明文档。每个文档都包含功能说明、使用指南、界面详解和技术实现等内容。

## 📚 文档列表

### 核心功能模块

1. **[用户认证系统](./01-用户认证系统.md)**
   - 用户登录、注册、会话管理
   - 开发模式自动登录功能
   - 权限控制和安全机制

2. **[新闻阅读模块](./02-新闻阅读模块.md)**
   - 新闻列表展示和筛选
   - 文章详细阅读界面
   - 词汇标注和语法解析

3. **[学习统计面板](./03-学习统计面板.md)**
   - 学习进度可视化
   - 成就系统和活动统计
   - 个人学习数据分析

4. **[生词本管理](./04-生词本管理.md)**
   - 词汇收藏和分类管理
   - 学习状态跟踪
   - 复习系统和进度统计

5. **[AI助教功能](./05-AI助教功能.md)**
   - 智能问答和对话练习
   - 语法解析和学习建议
   - 情景对话模拟

### 系统管理模块

6. **[RSS源管理](./06-RSS源管理.md)**
   - RSS订阅源配置
   - 字段映射规则设置
   - 数据源测试和验证

7. **[抓取管理系统](./07-抓取管理系统.md)**
   - 新闻抓取任务控制
   - AI处理队列管理
   - 系统监控和数据统计

8. **[系统设置](./08-系统设置.md)**
   - 全局配置管理
   - AI模型配置
   - API密钥管理

### 辅助功能

9. **[开发工具](./09-开发工具.md)**
   - 开发模式功能
   - 调试工具和状态监控
   - 快速操作面板

## 📖 文档结构说明

每个功能文档都包含以下标准章节：

### 1. 功能概述
- 功能的主要用途和价值
- 适用场景和用户群体
- 与其他模块的关系

### 2. 使用指南
- 基本操作流程
- 常用功能说明
- 注意事项和最佳实践

### 3. 界面详解
- 页面布局和组件说明
- 每个界面元素的功能
- 数据库字段对应关系

### 4. 技术实现
- 相关代码文件列表
- 核心算法和逻辑
- API接口说明
- 数据库表结构

### 5. 配置说明
- 环境变量配置
- 系统参数设置
- 自定义选项

### 6. 故障排除
- 常见问题和解决方案
- 错误代码说明
- 调试方法

## 🔗 相关文档

- [系统概述](../系统概述.md) - 项目整体介绍
- [技术栈说明](../技术栈说明.md) - 技术架构详解
- [数据库设计文档](../数据库设计文档.md) - 数据结构说明
- [API接口文档](../前后端接口规范文档.md) - 接口规范
- [开发指南](../开发指南.md) - 开发环境配置

## 📝 文档维护

- **更新频率**: 随功能开发同步更新
- **版本控制**: 与代码版本保持一致
- **反馈渠道**: 通过GitHub Issues提交文档问题
- **贡献指南**: 欢迎提交文档改进建议

## 🎯 使用建议

1. **新用户**: 建议先阅读用户认证系统和新闻阅读模块
2. **开发者**: 重点关注技术实现章节和相关代码文件
3. **管理员**: 重点了解RSS源管理和抓取管理系统
4. **测试人员**: 参考故障排除章节进行问题定位

## ✅ 文档完成状态

所有功能说明文档已完成创建，包含以下9个核心模块：

| 序号 | 功能模块 | 文档状态 | 主要内容 |
|------|----------|----------|----------|
| 1 | 用户认证系统 | ✅ 已完成 | 登录、注册、自动登录、权限控制 |
| 2 | 新闻阅读模块 | ✅ 已完成 | 文章浏览、词汇标注、语法解析 |
| 3 | 学习统计面板 | ✅ 已完成 | 进度可视化、成就系统、数据分析 |
| 4 | 生词本管理 | ✅ 已完成 | 词汇收藏、复习系统、状态跟踪 |
| 5 | AI助教功能 | ✅ 已完成 | 智能问答、语法解析、情景对话 |
| 6 | RSS源管理 | ✅ 已完成 | 数据源配置、字段映射、测试验证 |
| 7 | 抓取管理系统 | ✅ 已完成 | 任务控制、进度监控、AI队列管理 |
| 8 | 系统设置 | ✅ 已完成 | AI模型配置、API密钥、系统参数 |
| 9 | 开发工具 | ✅ 已完成 | 自动登录、状态监控、调试辅助 |

### 📊 文档统计

- **总文档数**: 10个（含目录）
- **总页数**: 约150页
- **总字数**: 约8万字
- **涵盖功能**: 9个主要模块
- **技术细节**: 数据库表结构、API接口、代码文件
- **使用指南**: 详细的操作步骤和界面说明

---

*最后更新时间: 2024年12月*
*文档版本: v1.0*
