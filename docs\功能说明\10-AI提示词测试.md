# AI提示词测试功能

## 功能概述

AI提示词测试页面是一个专门用于测试和验证AI分析功能稳定性的管理工具。通过这个页面，管理员可以：

- 测试文章分析和语法分析功能
- 验证AI输出的一致性和格式正确性
- 进行批量测试以评估稳定性
- 分析测试结果并发现潜在问题

## 访问路径

- **URL**: `/admin/prompt-test`
- **权限**: 仅管理员可访问
- **导航**: 侧边栏 → 提示词测试

## 主要功能

### 1. 测试输入区域

#### 文章分析测试
- **标题**: 必填，输入日语文章标题
- **副标题**: 可选，输入日语副标题
- **内容**: 可选，输入日语文章内容（支持HTML格式）

#### 语法分析测试
- **文本**: 必填，输入要分析语法的日语文本

#### 示例数据
点击"加载示例数据"按钮可以快速填入预设的测试数据：
- 文章分析示例：包含标题、副标题和HTML格式内容
- 语法分析示例：包含多个语法点的日语句子

### 2. 测试执行

#### 单次测试
- 点击"单次测试"按钮执行一次AI分析
- 显示处理时间和结果状态
- 结果会添加到测试历史中

#### 批量测试
- 设置测试次数（2-10次）
- 点击"批量测试"按钮连续执行多次测试
- 用于验证AI输出的稳定性和一致性
- 测试间有1秒延迟，避免API限制

### 3. 结果展示

#### 测试历史
每个测试结果包含：
- **状态标识**: 成功/失败
- **时间戳**: 测试执行时间
- **处理时间**: AI分析耗时（毫秒）
- **结果内容**: 完整的JSON输出
- **复制功能**: 一键复制结果到剪贴板

#### 结果格式
测试结果包含额外的元数据：
```json
{
  // AI分析结果...
  "_testMetadata": {
    "processingTime": 1234,
    "timestamp": "2025-07-06T12:00:00.000Z",
    "modelUsed": "googleai/gemini-2.0-flash",
    "inputStats": {
      "titleLength": 15,
      "contentLength": 200
    },
    "outputStats": {
      "vocabularyCount": 8,
      "grammarCount": 3,
      "hasTitleWithFurigana": true
    }
  }
}
```

### 4. 结果分析

当有2次或以上测试结果时，系统会自动显示分析面板：

#### 统计指标
- **总测试次数**: 执行的测试总数
- **成功率**: 成功测试的百分比
- **平均耗时**: 所有测试的平均处理时间
- **发现问题**: 检测到的一致性和格式问题数量

#### 一致性检查
- **词汇数量变化**: 检查不同测试中词汇数量的差异
- **语法点数量变化**: 检查语法点数量的变化
- **阈值**: 词汇变化>3个或语法变化>2个时报告问题

#### 格式验证
- **振り仮名完整性**: 检查是否为所有汉字添加了振り仮名
- **例句数量**: 验证词汇和语法的例句是否为3个
- **必填字段**: 检查关键字段是否存在

## 使用场景

### 1. 提示词优化验证
- 修改提示词后，使用相同输入进行多次测试
- 对比优化前后的结果一致性
- 验证新提示词是否解决了已知问题

### 2. 稳定性评估
- 使用批量测试功能连续测试同一输入
- 分析结果的变化程度
- 识别不稳定的输出模式

### 3. 格式规范检查
- 验证AI输出是否符合预期格式
- 检查必填字段的完整性
- 确保例句数量的一致性

### 4. 性能监控
- 监控AI分析的处理时间
- 识别性能异常
- 评估不同输入长度对性能的影响

## 最佳实践

### 测试策略
1. **基准测试**: 使用标准示例数据建立基准
2. **边界测试**: 测试极短和极长的输入
3. **批量验证**: 对关键功能进行多次测试
4. **定期检查**: 定期运行测试以监控系统状态

### 问题诊断
1. **一致性问题**: 如果发现输出不一致，检查提示词的明确性
2. **格式问题**: 如果格式验证失败，检查输出schema定义
3. **性能问题**: 如果处理时间过长，考虑优化输入或模型选择

### 结果解读
- **成功率>95%**: 系统稳定，提示词效果良好
- **成功率85-95%**: 可接受范围，可考虑优化
- **成功率<85%**: 需要立即检查和修复

## 技术实现

### API端点
- `/api/ai/test/analyze-article`: 文章分析测试
- `/api/ai/test/analyze-grammar`: 语法分析测试

### 特殊功能
- **测试元数据**: 每个测试结果包含详细的统计信息
- **错误处理**: 完整的错误信息和类型记录
- **性能监控**: 精确的处理时间测量

### 数据格式
测试结果遵循标准的AI分析输出格式，并添加了测试专用的元数据字段，便于分析和调试。

## 注意事项

1. **权限控制**: 仅管理员可访问此功能
2. **API限制**: 批量测试时注意API调用频率限制
3. **数据隐私**: 测试数据仅在客户端存储，不会持久化
4. **资源消耗**: 大量测试可能消耗较多AI API配额

通过合理使用这个测试工具，可以有效提升AI分析功能的质量和稳定性。
