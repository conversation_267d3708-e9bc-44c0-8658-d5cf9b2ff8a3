import fetch, { RequestInfo, RequestInit, Response } from 'node-fetch';

class AntiCrawlerManager {
  private userAgents: string[];
  private retryConfig: { maxRetries: number; retryDelay: number; backoffMultiplier: number };

  constructor() {
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    ];
    this.retryConfig = { maxRetries: 3, retryDelay: 5000, backoffMultiplier: 2 };
  }

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private generateHeaders(url: string): { [key: string]: string } {
    const urlObj = new URL(url);
    return {
      'User-Agent': this.getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
      'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
      'Referer': `${urlObj.protocol}//${urlObj.hostname}/`,
    };
  }

  async safeRequest(url: RequestInfo, options: RequestInit = {}): Promise<Response> {
    let lastError: Error | undefined;
    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          const delay = this.retryConfig.retryDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 2);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const headers = this.generateHeaders(url.toString());
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        try {
          const response = await fetch(url, { ...options, headers, signal: controller.signal });
          clearTimeout(timeoutId);

          if (response.ok) return response;
          throw new Error(`HTTP错误 (${response.status}): ${response.statusText}`);
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      } catch (error: any) {
        lastError = error;
      }
    }
    throw new Error(`所有重试都失败了: ${lastError?.message}`);
  }
}

export const antiCrawler = new AntiCrawlerManager();
