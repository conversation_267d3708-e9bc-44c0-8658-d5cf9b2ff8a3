'use client';
import React, { useState } from 'react';
import { User, Settings as SettingsIcon, Shield } from 'lucide-react';
import UserSettings from '@/components/UserSettings';
import Settings from '@/components/Settings';

interface UnifiedSettingsProps {
  userId: string;
  userRole: string;
  // System settings props
  initialModels?: any[];
  initialDefaultModelId?: string | null;
  initialAITemperature?: number;
  initialApiKeys?: any[];
  initialAIPrompts?: any[];
}

const UnifiedSettings: React.FC<UnifiedSettingsProps> = ({
  userId,
  userRole,
  initialModels = [],
  initialDefaultModelId = null,
  initialAITemperature = 0.7,
  initialApiKeys = [],
  initialAIPrompts = []
}) => {
  const [activeTab, setActiveTab] = useState<'user' | 'system'>('user');
  const isAdmin = userRole === 'admin';

  const tabs = [
    {
      id: 'user' as const,
      label: '个人设置',
      icon: User,
      description: '管理您的个人偏好和界面设置'
    },
    ...(isAdmin ? [{
      id: 'system' as const,
      label: '系统设置',
      icon: Shield,
      description: '管理系统级配置和默认设置'
    }] : [])
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">设置</h1>
        <p className="text-gray-600 mt-1">
          {isAdmin ? '管理您的个人偏好和系统配置' : '管理您的个人学习偏好'}
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className={`mr-2 h-5 w-5 ${
                    activeTab === tab.id ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`} />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
        
        {/* 标签页描述 */}
        <div className="mt-4">
          {tabs.map((tab) => (
            activeTab === tab.id && (
              <p key={tab.id} className="text-sm text-gray-600">
                {tab.description}
              </p>
            )
          ))}
        </div>
      </div>

      {/* 标签页内容 */}
      <div className="tab-content">
        {activeTab === 'user' && (
          <div className="animate-fadeIn">
            <UserSettings userId={userId} />
          </div>
        )}
        
        {activeTab === 'system' && isAdmin && (
          <div className="animate-fadeIn">
            <Settings
              initialModels={initialModels}
              initialDefaultModelId={initialDefaultModelId}
              initialAITemperature={initialAITemperature}
              initialApiKeys={initialApiKeys}
              initialAIPrompts={initialAIPrompts}
            />
          </div>
        )}
      </div>

      {/* 添加一些自定义样式 */}
      <style jsx>{`
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .tab-content {
          min-height: 400px;
        }
      `}</style>
    </div>
  );
};

export default UnifiedSettings;
