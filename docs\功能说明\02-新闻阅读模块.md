# 新闻阅读模块功能说明

## 1. 功能概述

### 1.1 主要功能
新闻阅读模块是平台的核心功能，提供NHK新闻文章的浏览、阅读和学习功能。支持文章列表展示、详细阅读、词汇标注、语法解析等智能学习辅助功能。

### 1.2 核心特性
- **文章浏览**: 新闻列表展示和分类筛选
- **智能阅读**: 词汇标注和语法点解析
- **学习辅助**: 汉字注音(Furigana)和翻译功能
- **个性化**: 基于JLPT等级的内容推荐
- **多媒体**: 支持音频、视频和图片内容
- **学习记录**: 阅读进度和学习状态跟踪

### 1.3 适用场景
- 日语学习者的新闻阅读练习
- 词汇和语法的实际应用学习
- 听力练习（音频新闻）
- 阅读理解能力提升

## 2. 使用指南

### 2.1 浏览新闻列表
1. 进入主页面，查看最新新闻列表
2. 使用筛选器按JLPT等级、分类筛选
3. 使用搜索框查找特定主题的文章
4. 点击文章卡片进入详细阅读

### 2.2 阅读文章
1. 点击文章标题进入阅读页面
2. 查看汉字注音辅助理解
3. 点击词汇查看详细解释
4. 使用翻译功能辅助理解
5. 收藏重要词汇到生词本

### 2.3 学习功能使用
1. **词汇学习**: 点击文章中的词汇查看释义
2. **语法分析**: 查看文章中的语法点解析
3. **听力练习**: 播放文章音频进行听力训练
4. **阅读记录**: 系统自动记录阅读进度

## 3. 界面详解

### 3.1 新闻列表页面 (`/`)

#### 页面布局
- **顶部**: 搜索栏和筛选器
- **主体**: 新闻卡片网格布局
- **底部**: 分页导航

#### 筛选器组件
| 筛选项 | 选项值 | 数据库对应 | 说明 |
|--------|--------|------------|------|
| 分类 | 科技、社会、经济等 | articles.category | 新闻分类 |
| 搜索关键词 | 文本输入 | articles.title, content | 标题和内容搜索 |

#### 新闻卡片 (NewsCard)
| 显示项 | 数据库字段 | 说明 |
|--------|------------|------|
| 文章标题 | articles.title | 新闻标题 |
| 副标题 | articles.subtitle | 新闻副标题 |
| 发布时间 | articles.publish_time | 新闻发布时间 |
| 分类标签 | articles.category | 新闻分类 |

| 特色图片 | articles.featured_image_path | 新闻配图 |
| 媒体标识 | articles.video_url, audio_url | 音视频内容标识 |

#### 操作按钮
- **阅读按钮**: 进入文章详细页面
- **收藏按钮**: 收藏文章到个人列表
- **播放按钮**: 播放音频内容（如有）

### 3.2 文章阅读页面 (`/article/[id]`)

#### 页面结构
- **文章头部**: 标题、元信息、操作按钮
- **文章正文**: 带注音的文章内容
- **侧边栏**: 词汇列表、语法点（桌面端）
- **底部**: 相关文章推荐

#### 文章头部信息
| 显示项 | 数据库字段 | 说明 |
|--------|------------|------|
| 文章标题 | articles.title_furigana_html | 带注音的标题 |
| 副标题 | articles.subtitle_furigana_html | 带注音的副标题 |
| 发布时间 | articles.publish_time | 格式化的发布时间 |
| 来源 | rss_sources.name | 新闻来源 |
| 分类 | articles.category | 新闻分类 |
| 难度等级 | articles.jlpt_level | JLPT等级 |

#### 文章正文
| 功能 | 实现方式 | 数据来源 |
|------|----------|----------|
| 汉字注音 | HTML渲染 | articles.content_furigana_html |
| 词汇标注 | 交互式高亮 | article_vocabulary关联表 |
| 语法标注 | 颜色标识 | article_grammar_points关联表 |
| 翻译显示 | 切换显示 | article_translations表 |

#### 词汇解释弹窗
| 显示项 | 数据库字段 | 说明 |
|--------|------------|------|
| 词汇 | vocabulary.word | 原始词汇 |
| 读音 | vocabulary.reading | 假名读音 |
| 中文释义 | vocabulary.meaning_zh | 中文解释 |
| 英文释义 | vocabulary.meaning_en | 英文解释 |
| 词性 | vocabulary.part_of_speech | 词性标注 |
| JLPT等级 | vocabulary.jlpt_level | 词汇等级 |
| 例句 | vocabulary.related_words_json | 相关例句 |

### 3.3 多媒体播放器

#### 音频播放器
- **控制按钮**: 播放/暂停、进度条
- **数据来源**: articles.audio_url 或 audio_path
- **功能**: 支持倍速播放、循环播放

#### 视频播放器
- **控制按钮**: 播放/暂停、全屏、音量
- **数据来源**: articles.video_url 或 video_path
- **功能**: 支持字幕显示（如有）

## 4. 技术实现

### 4.1 核心代码文件

#### 主页面组件
- **文件**: `src/app/(main)/page.tsx`
- **功能**: 新闻列表页面的服务端渲染
- **关键逻辑**:
  - 服务端数据获取
  - 搜索参数处理
  - 分页逻辑实现

#### 新闻阅读器组件
- **文件**: `src/components/NewsReader.tsx`
- **功能**: 新闻列表展示和交互
- **主要功能**:
  - 文章列表渲染
  - 筛选和搜索功能
  - 分页导航

#### 新闻卡片组件
- **文件**: `src/components/NewsCard.tsx`
- **功能**: 单个新闻项的展示
- **主要功能**:
  - 文章信息展示
  - 媒体类型标识
  - 交互按钮处理

#### 文章阅读器组件
- **文件**: `src/components/ArticleReader.tsx`
- **功能**: 文章详细内容展示
- **主要功能**:
  - 富文本内容渲染
  - 词汇交互处理
  - 翻译切换功能

### 4.2 数据库表结构

#### articles表（主表）
```sql
CREATE TABLE articles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER,
  title TEXT NOT NULL,
  subtitle TEXT,
  content TEXT,
  content_html TEXT,
  title_furigana_html TEXT,
  subtitle_furigana_html TEXT,
  content_furigana_html TEXT,
  url TEXT UNIQUE NOT NULL,
  category TEXT,
  jlpt_level TEXT,
  publish_time DATETIME,
  fetch_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  featured_image_url TEXT,
  featured_image_path TEXT,
  audio_url TEXT,
  audio_path TEXT,
  video_url TEXT,
  video_path TEXT,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources(id)
);
```

#### article_vocabulary表（词汇关联）
```sql
CREATE TABLE article_vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  vocabulary_id INTEGER NOT NULL,
  position_start INTEGER,
  position_end INTEGER,
  context TEXT,
  FOREIGN KEY (article_id) REFERENCES articles(id),
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary(id)
);
```

#### article_translations表（翻译）
```sql
CREATE TABLE article_translations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  language TEXT NOT NULL,
  title_translation TEXT,
  subtitle_translation TEXT,
  content_translation TEXT,
  FOREIGN KEY (article_id) REFERENCES articles(id)
);
```

### 4.3 API接口

#### 获取文章列表
- **路径**: `GET /api/articles`
- **参数**: `{level?, search?, page?, limit?}`
- **返回**: `{articles[], totalCount, currentPage}`

#### 获取文章详情
- **路径**: `GET /api/articles/[id]`
- **返回**: 完整的文章信息，包含词汇和语法点

#### 获取文章词汇
- **路径**: `GET /api/articles/[id]/vocabulary`
- **返回**: 文章相关的词汇列表

#### 获取文章翻译
- **路径**: `GET /api/articles/[id]/translations`
- **参数**: `{language}`
- **返回**: 指定语言的翻译内容

## 5. 配置说明

### 5.1 显示配置

#### 分页设置
- **配置项**: `articles_per_page`
- **存储位置**: system_settings表
- **默认值**: 10
- **说明**: 每页显示的文章数量

#### 难度等级配置
- **支持等级**: N5, N4, N3, N2, N1
- **显示颜色**: 不同等级使用不同颜色标识
- **筛选逻辑**: 支持单选和全选

### 5.2 媒体文件配置

#### 文件存储路径
```env
# 媒体文件存储目录
MEDIA_DIR="./public/media"
UPLOAD_DIR="./uploads"
```

#### 支持的媒体格式
- **音频**: MP3, M4A, WAV
- **视频**: MP4, M3U8
- **图片**: JPG, PNG, WebP

## 6. 故障排除

### 6.1 常见问题

#### 文章列表不显示
**症状**: 页面空白或显示"暂无文章"
**解决方案**:
1. 检查数据库中是否有文章数据
2. 确认RSS抓取是否正常运行
3. 检查API接口是否正常响应

#### 词汇标注不显示
**症状**: 文章中的词汇没有交互效果
**解决方案**:
1. 检查article_vocabulary表是否有数据
2. 确认AI处理是否完成
3. 检查前端JavaScript是否正常加载

#### 音频/视频无法播放
**症状**: 媒体播放器显示错误
**解决方案**:
1. 检查媒体文件是否存在
2. 确认文件路径配置正确
3. 检查浏览器媒体支持

### 6.2 性能优化

#### 图片懒加载
- 使用Next.js Image组件
- 自动优化图片格式和尺寸

#### 内容缓存
- 服务端渲染缓存
- 静态资源CDN加速

#### 数据库优化
```sql
-- 创建索引提高查询性能
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_jlpt_level ON articles(jlpt_level);
CREATE INDEX idx_articles_publish_time ON articles(publish_time);
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
