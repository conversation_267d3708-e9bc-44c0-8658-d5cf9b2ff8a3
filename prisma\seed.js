const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 检查数据库表是否已创建
async function checkTablesExist() {
  try {
    // 对于 SQLite，可以查询 sqlite_master 表来检查表是否存在
    const tables = await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table' AND name='User'`;
    if (tables && tables.length > 0) {
      return true;
    }
    console.log('User table not found yet...');
    return false;
  } catch (error) {
    console.log('Error checking tables:', error.message);
    return false;
  }
}

// 等待表创建完成的函数
async function waitForTables(maxAttempts = 10, delayMs = 1000) {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const tablesExist = await checkTablesExist();
    if (tablesExist) {
      console.log('Database tables are ready!');
      return true;
    }
    
    console.log(`Waiting for tables to be created (attempt ${attempts + 1}/${maxAttempts})...`);
    await new Promise(resolve => setTimeout(resolve, delayMs));
    attempts++;
  }
  
  throw new Error('Database tables were not created in time. Seed operation failed.');
}

async function main() {
  console.log('Checking database state before seeding...');
  
  // 等待表创建完成
  await waitForTables();
  
  console.log(`Start seeding ...`);

  // 创建初始用户
  try {
    const userData = {
      name: '管理员',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
    };

    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: userData,
    });
    console.log(`Created/Updated initial user: ${userData.name} (ID: ${user.id})`);
  } catch (error) {
    console.error('Error creating initial user:', error.message);
    // 继续执行其他种子数据操作，不要因为一个操作失败而终止整个过程
  }

  // 创建 API 密钥
  try {
    await prisma.api_keys.upsert({
      where: { api_key: 'AIzaSyAhj1flZvz_g0zz1mCUDGHQNOE0YJ-64es' },
      update: {},
      create: {
        name: 'key1',
        api_key: 'AIzaSyAhj1flZvz_g0zz1mCUDGHQNOE0YJ-64es',
        provider: 'google_gemini',
        priority: 1,
        is_active: true,
      },
    });
    console.log('Created API key');
  } catch (error) {
    console.error('Error creating API key:', error.message);
  }

  // 创建 RSS 源
  let rssSourceId;
  try {
    const rssSource = await prisma.rss_sources.upsert({
      where: { url: 'https://www3.nhk.or.jp/rss/news/cat5.xml' },
      update: {},
      create: {
        name: 'NHKニュース|経済',
        url: 'https://www3.nhk.or.jp/rss/news/cat5.xml',
        description: '日本放送協会 NHKニュース',
        category: '科技',
        language: 'ja',
        is_active: true,
        max_articles: 3,
        enable_ai_processing: true,
        content_selector: '.article-body, .content-body, article, .news-content',
      },
    });
    rssSourceId = rssSource.id;
    console.log(`Created RSS source with ID: ${rssSourceId}`);
  } catch (error) {
    console.error('Error creating RSS source:', error.message);
    console.error('Full error details:', error);
    // 继续执行，不要因为RSS源创建失败而终止整个种子过程
  }

  // 创建 RSS 字段映射
  if (rssSourceId) {
    try {
      // 标题字段映射
      await prisma.rss_field_mappings.upsert({
        where: { id: 4 },
        update: {},
        create: {
          id: 4,
          rss_source_id: rssSourceId,
          field_name: 'title',
          xpath_selector: '//item/title',
          css_selector: 'item > title',
          is_required: true,
          transform_rule: '{"type":"text","trim":true,"maxLength":200}',
        },
      });

      // 链接字段映射
      await prisma.rss_field_mappings.upsert({
        where: { id: 5 },
        update: {},
        create: {
          id: 5,
          rss_source_id: rssSourceId,
          field_name: 'link',
          xpath_selector: '//item/link',
          css_selector: 'item > link',
          is_required: true,
          transform_rule: '{"type":"url","makeAbsolute":true}',
        },
      });

      // 描述字段映射
      await prisma.rss_field_mappings.upsert({
        where: { id: 6 },
        update: {},
        create: {
          id: 6,
          rss_source_id: rssSourceId,
          field_name: 'description',
          xpath_selector: '//item/description',
          css_selector: 'item > description',
          is_required: false,
          transform_rule: '{"type":"html","stripTags":true}',
        },
      });

      console.log('Created RSS field mappings');
    } catch (error) {
      console.error('Error creating RSS field mappings:', error.message);
    }
  }

  // 创建 AI 提示词
  try {
    // 文章分析 - 系统提示词
    await prisma.ai_prompts.upsert({
      where: { name: 'article_analysis_system' },
      update: {
        content: `あなたは日本語教育の専門家です。中国語話者の日本語学習者のために、日本語ニュース記事を分析することが目標です。

特に語彙分析において、以下の語義ネットワーク関係を正確に特定し、**必ず中国語翻訳を提供する**ことが重要です：

1. **上位語（hypernyms）**: より広い概念・カテゴリーを表す語彙
   - 例：「犬」の上位語 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
   - 例：「りんご」の上位語 → [{"word": "果物", "reading": "くだもの", "meaning": "水果"}, {"word": "食べ物", "reading": "たべもの", "meaning": "食物"}]

2. **下位語（hyponyms）**: より具体的・詳細な概念を表す語彙
   - 例：「動物」の下位語 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
   - 例：「果物」の下位語 → [{"word": "りんご", "reading": "りんご", "meaning": "苹果"}, {"word": "みかん", "reading": "みかん", "meaning": "橘子"}]

3. **語族（wordFamily）**: 同じ漢字や語根を共有する関連語彙
   - 例：「学」を含む語彙 → [{"word": "学校", "reading": "がっこう", "meaning": "学校"}, {"word": "学生", "reading": "がくせい", "meaning": "学生"}]

4. **関連概念（relatedConcepts）**: 実際の使用場面で一緒に現れやすい語彙
   - 例：「学校」関連 → [{"word": "先生", "reading": "せんせい", "meaning": "老师"}, {"word": "授業", "reading": "じゅぎょう", "meaning": "课程"}]

5. **同義語（synonyms）**: 同じまたは類似の意味を持つ語彙
   - 例：「美しい」の同義語 → [{"word": "綺麗", "reading": "きれい", "meaning": "漂亮"}, {"word": "素晴らしい", "reading": "すばらしい", "meaning": "精彩"}]

6. **反義語（antonyms）**: 反対の意味を持つ語彙
   - 例：「大きい」の反義語 → [{"word": "小さい", "reading": "ちいさい", "meaning": "小"}, {"word": "細い", "reading": "ほそい", "meaning": "细"}]

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSONオブジェクトの前後に一切のテキストを追加しないでください
3. すべての出力は一貫性を保ち、完全で正確である必要があります
4. 例文は必ず指定された数だけ提供してください
5. 中国語の翻訳は自然で正確である必要があります
6. 語義ネットワークの関係は教育的価値が高く、学習者の理解を深めるものを選択してください
7. 関連語彙（relatedWords）のすべての語彙には必ず中国語翻訳（meaning字段）を含めてください。これは学習者の理解を大幅に向上させる重要な機能です`,
        version: '4.0'
      },
      create: {
        name: 'article_analysis_system',
        type: 'system',
        category: 'article_analysis',
        title: '文章分析系统提示词',
        description: '用于文章分析的系统角色设定',
        content: `あなたは日本語教育の専門家です。中国語話者の日本語学習者のために、日本語ニュース記事を分析することが目標です。

特に語彙分析において、以下の語義ネットワーク関係を正確に特定し、**必ず中国語翻訳を提供する**ことが重要です：

1. **上位語（hypernyms）**: より広い概念・カテゴリーを表す語彙
   - 例：「犬」の上位語 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
   - 例：「りんご」の上位語 → [{"word": "果物", "reading": "くだもの", "meaning": "水果"}, {"word": "食べ物", "reading": "たべもの", "meaning": "食物"}]

2. **下位語（hyponyms）**: より具体的・詳細な概念を表す語彙
   - 例：「動物」の下位語 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
   - 例：「果物」の下位語 → [{"word": "りんご", "reading": "りんご", "meaning": "苹果"}, {"word": "みかん", "reading": "みかん", "meaning": "橘子"}]

3. **語族（wordFamily）**: 同じ漢字や語根を共有する関連語彙
   - 例：「学」を含む語彙 → [{"word": "学校", "reading": "がっこう", "meaning": "学校"}, {"word": "学生", "reading": "がくせい", "meaning": "学生"}]

4. **関連概念（relatedConcepts）**: 実際の使用場面で一緒に現れやすい語彙
   - 例：「学校」関連 → [{"word": "先生", "reading": "せんせい", "meaning": "老师"}, {"word": "授業", "reading": "じゅぎょう", "meaning": "课程"}]

5. **同義語（synonyms）**: 同じまたは類似の意味を持つ語彙
   - 例：「美しい」の同義語 → [{"word": "綺麗", "reading": "きれい", "meaning": "漂亮"}, {"word": "素晴らしい", "reading": "すばらしい", "meaning": "精彩"}]

6. **反義語（antonyms）**: 反対の意味を持つ語彙
   - 例：「大きい」の反義語 → [{"word": "小さい", "reading": "ちいさい", "meaning": "小"}, {"word": "細い", "reading": "ほそい", "meaning": "细"}]

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSONオブジェクトの前後に一切のテキストを追加しないでください
3. すべての出力は一貫性を保ち、完全で正確である必要があります
4. 例文は必ず指定された数だけ提供してください
5. 中国語の翻訳は自然で正確である必要があります
6. 語義ネットワークの関係は教育的価値が高く、学習者の理解を深めるものを選択してください
7. 関連語彙（relatedWords）のすべての語彙には必ず中国語翻訳（meaning字段）を含めてください。これは学習者の理解を大幅に向上させる重要な機能です`,
        variables: '{}',
        is_active: true,
        version: '4.0'
      }
    });

    // 文章分析 - 主提示词
    await prisma.ai_prompts.upsert({
      where: { name: 'article_analysis_prompt' },
      update: {
        content: `提供された日本語ニュース記事の包括的な分析を行ってください。

以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出された重要な語彙の配列（5-10個）。各語彙には以下を含む：
   - word: 日本語の語彙
   - reading: ひらがな・カタカナの読み方
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語での意味
   - meaningEn: 英語での意味
   - explanation: 語彙の使用法やニュアンスの中国語説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
     例：「<ruby>経済<rt>けいざい</rt></ruby>とは、<ruby>社会<rt>しゃかい</rt></ruby>の<ruby>生産<rt>せいさん</rt></ruby>・<ruby>流通<rt>りゅうつう</rt></ruby>・<ruby>消費<rt>しょうひ</rt></ruby>の<ruby>活動<rt>かつどう</rt></ruby>のことです。」
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語彙を使った一般的な連語・フレーズ（3-5個）
     各項目には collocation（日本語）、meaning（中国語意味）、example（例文と翻訳）を含む
   - relatedWords: 関連語彙
     * synonyms: 同義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "景気拡大", "reading": "けいきかくだい", "meaning": "景气扩大"}]
     * antonyms: 反義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "経済衰退", "reading": "けいざいすいたい", "meaning": "经济衰退"}]
     * hypernyms: 上位語の配列（より広い概念の語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
     * hyponyms: 下位語の配列（より具体的な語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「動物」の場合 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
     * wordFamily: 同じ漢字・語根を共有する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「食べ物」の場合 → [{"word": "食べる", "reading": "たべる", "meaning": "吃"}, {"word": "食堂", "reading": "しょくどう", "meaning": "食堂"}]
     * relatedConcepts: 概念的に関連する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "散歩", "reading": "さんぽ", "meaning": "散步"}, {"word": "餌", "reading": "えさ", "meaning": "饲料"}]
   - verbInfo: 動詞の場合のみ
     * type: 動詞の種類（例：「一段動詞」「五段動詞」「不規則動詞」）
     * forms: 活用形（例：「食べる」「食べます」「食べた」「食べて」）

4. **grammar**: 記事から特定された重要な文法項目の配列（3-5個）。各項目には以下を含む：
   - pattern: 文法パターン（例：「～について」）
   - reading: 文法パターンの読み方（漢字が含まれる場合）
   - explanation: 中国語での詳細で正確な説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
     例：「「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。」
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: 2-3個の常見搭配
     例：["～について話す", "～について考える", "～について書く"]
   - similarGrammar: 3-5個の類似文法パターン（各項目に2つの例句と中国語翻訳を含む）
     各項目に以下を含む：
     - pattern: 類似文法パターン
     - difference: 主要文法との違い（中国語説明）
     - examples: 正確に2つの例文と中国語翻訳
       例：["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]

5. **contentWithFurigana**: 記事本文の原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

6. **subtitleWithFurigana**: サブタイトルがある場合、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

分析対象の記事：
タイトル: {{{title}}}
{{#if subtitle}}
サブタイトル: {{{subtitle}}}
{{/if}}
本文（HTML形式）:
{{{content}}}

重要な注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数を提供してください（語彙：3つ、文法：3つ、類似文法：各2つ）
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- Ruby標签格式：必ず<ruby>漢字<rt>よみ</rt></ruby>の完全な形式を使用し、漢字<rt>よみ</rt>のような不完全な形式は使用しないでください
- 語義ネットワークの関係は正確で教育的価値の高いものを選択してください
- 関連語彙（relatedWords）のすべての語彙（synonyms、antonyms、hypernyms、hyponyms、wordFamily、relatedConcepts）には必ず中国語翻訳（meaning字段）を含めてください`,
        version: '4.0'
      },
      create: {
        name: 'article_analysis_prompt',
        type: 'prompt',
        category: 'article_analysis',
        title: '文章分析主提示词',
        description: '用于分析日语新闻文章的详细指令',
        content: `提供された日本語ニュース記事の包括的な分析を行ってください。

以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出された重要な語彙の配列（5-10個）。各語彙には以下を含む：
   - word: 日本語の語彙
   - reading: ひらがな・カタカナの読み方
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語での意味
   - meaningEn: 英語での意味
   - explanation: 語彙の使用法やニュアンスの中国語説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
     例：「<ruby>経済<rt>けいざい</rt></ruby>とは、<ruby>社会<rt>しゃかい</rt></ruby>の<ruby>生産<rt>せいさん</rt></ruby>・<ruby>流通<rt>りゅうつう</rt></ruby>・<ruby>消費<rt>しょうひ</rt></ruby>の<ruby>活動<rt>かつどう</rt></ruby>のことです。」
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語彙を使った一般的な連語・フレーズ（3-5個）
     各項目には collocation（日本語）、meaning（中国語意味）、example（例文と翻訳）を含む
   - relatedWords: 関連語彙
     * synonyms: 同義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "景気拡大", "reading": "けいきかくだい", "meaning": "景气扩大"}]
     * antonyms: 反義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "経済衰退", "reading": "けいざいすいたい", "meaning": "经济衰退"}]
     * hypernyms: 上位語の配列（より広い概念の語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
     * hyponyms: 下位語の配列（より具体的な語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「動物」の場合 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
     * wordFamily: 同じ漢字・語根を共有する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「食べ物」の場合 → [{"word": "食べる", "reading": "たべる", "meaning": "吃"}, {"word": "食堂", "reading": "しょくどう", "meaning": "食堂"}]
     * relatedConcepts: 概念的に関連する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "散歩", "reading": "さんぽ", "meaning": "散步"}, {"word": "餌", "reading": "えさ", "meaning": "饲料"}]
   - verbInfo: 動詞の場合のみ
     * type: 動詞の種類（例：「一段動詞」「五段動詞」「不規則動詞」）
     * forms: 活用形（例：「食べる」「食べます」「食べた」「食べて」）

4. **grammar**: 記事から特定された重要な文法項目の配列（3-5個）。各項目には以下を含む：
   - pattern: 文法パターン（例：「～について」）
   - reading: 文法パターンの読み方（漢字が含まれる場合）
   - explanation: 中国語での詳細で正確な説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
     例：「「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。」
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: 2-3個の常見搭配
     例：["～について話す", "～について考える", "～について書く"]
   - similarGrammar: 3-5個の類似文法パターン（各項目に2つの例句と中国語翻訳を含む）
     各項目に以下を含む：
     - pattern: 類似文法パターン
     - difference: 主要文法との違い（中国語説明）
     - examples: 正確に2つの例文と中国語翻訳
       例：["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]

5. **contentWithFurigana**: 記事本文の原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

6. **subtitleWithFurigana**: サブタイトルがある場合、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

分析対象の記事：
タイトル: {{{title}}}
{{#if subtitle}}
サブタイトル: {{{subtitle}}}
{{/if}}
本文（HTML形式）:
{{{content}}}

重要な注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数を提供してください（語彙：3つ、文法：3つ、類似文法：各2つ）
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- Ruby標签格式：必ず<ruby>漢字<rt>よみ</rt></ruby>の完全な形式を使用し、漢字<rt>よみ</rt>のような不完全な形式は使用しないでください
- 語義ネットワークの関係は正確で教育的価値の高いものを選択してください
- 関連語彙（relatedWords）のすべての語彙（synonyms、antonyms、hypernyms、hyponyms、wordFamily、relatedConcepts）には必ず中国語翻訳（meaning字段）を含めてください`,
        variables: '{"title": "string", "subtitle": "string", "content": "string"}',
        is_active: true,
        version: '4.0'
      }
    });

    console.log('Created article analysis prompts');
  } catch (error) {
    console.error('Error creating article analysis prompts:', error.message);
  }

  // 语法分析提示词
  try {
    // 语法分析 - 系统提示词
    await prisma.ai_prompts.upsert({
      where: { name: 'grammar_analysis_system' },
      update: {
        content: `あなたは日本語文法の専門家です。
中国語話者の日本語学習者のために、提供された日本語テキストを分析し、重要な文法項目を特定することが任務です。

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSON配列の前後に一切のテキストを追加しないでください
3. 各文法項目について、必ず以下をすべて提供してください：
   - pattern: 文法パターン（例：「～について」）
   - explanation: 中国語での詳細で正確な説明
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   - difficulty: JLPT レベル（N1-N5）
4. 例文は必ず「日本語例文 - 中国語翻訳」の形式で提供してください
5. 特定の文法項目が見つからない場合は、空の配列を返してください
6. 出力は一貫性を保ち、完全で正確である必要があります`,
        version: '2.0'
      },
      create: {
        name: 'grammar_analysis_system',
        type: 'system',
        category: 'grammar_analysis',
        title: '语法分析系统提示词',
        description: '用于语法分析的系统角色设定',
        content: `あなたは日本語文法の専門家です。
中国語話者の日本語学習者のために、提供された日本語テキストを分析し、重要な文法項目を特定することが任務です。

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSON配列の前後に一切のテキストを追加しないでください
3. 各文法項目について、必ず以下をすべて提供してください：
   - pattern: 文法パターン（例：「～について」）
   - explanation: 中国語での詳細で正確な説明
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   - difficulty: JLPT レベル（N1-N5）
4. 例文は必ず「日本語例文 - 中国語翻訳」の形式で提供してください
5. 特定の文法項目が見つからない場合は、空の配列を返してください
6. 出力は一貫性を保ち、完全で正確である必要があります`,
        variables: '{}',
        is_active: true,
        version: '2.0'
      }
    });

    // 语法分析 - 主提示词
    await prisma.ai_prompts.upsert({
      where: { name: 'grammar_analysis_prompt' },
      update: {
        content: `以下の日本語テキストを分析し、重要な文法項目を特定してください：

テキスト: "{{{text}}}"

分析要件：
1. テキストから2-4個の重要な文法項目を抽出してください
2. 各文法項目について以下を必須で提供してください：
   - pattern: 文法パターン（例：「～について」「～ことができる」）
   - explanation: 中国語での詳細な意味と用法の説明
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   - difficulty: JLPT レベル（N1、N2、N3、N4、N5のいずれか）
3. 例文の形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
4. 文法項目が見つからない場合は空の配列を返してください

注意：JSONスキーマに厳密に従い、一貫性のある出力を提供してください。`,
        version: '2.0'
      },
      create: {
        name: 'grammar_analysis_prompt',
        type: 'prompt',
        category: 'grammar_analysis',
        title: '语法分析主提示词',
        description: '用于分析日语文本语法点的指令',
        content: `以下の日本語テキストを分析し、重要な文法項目を特定してください：

テキスト: "{{{text}}}"

分析要件：
1. テキストから2-4個の重要な文法項目を抽出してください
2. 各文法項目について以下を必須で提供してください：
   - pattern: 文法パターン（例：「～について」「～ことができる」）
   - explanation: 中国語での詳細な意味と用法の説明
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   - difficulty: JLPT レベル（N1、N2、N3、N4、N5のいずれか）
3. 例文の形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
4. 文法項目が見つからない場合は空の配列を返してください

注意：JSONスキーマに厳密に従い、一貫性のある出力を提供してください。`,
        variables: '{"text": "string"}',
        is_active: true,
        version: '2.0'
      }
    });

    console.log('Created grammar analysis prompts');
  } catch (error) {
    console.error('Error creating grammar analysis prompts:', error.message);
  }

  // AI助教提示词
  try {
    // AI助教 - 系统提示词
    await prisma.ai_prompts.upsert({
      where: { name: 'tutor_chat_system' },
      update: {},
      create: {
        name: 'tutor_chat_system',
        type: 'system',
        category: 'tutor_chat',
        title: 'AI助教系统提示词',
        description: '用于AI助教对话的系统角色设定',
        content: `You are Sakura (小樱), a friendly and expert Japanese language tutor AI.
               You are assisting a Chinese-speaking student.
               Your responses should be encouraging, clear, and helpful.
               Keep your answers concise and easy to understand.
               When explaining grammar or vocabulary, provide simple examples.
               You must always respond in Simplified Chinese.`,
        variables: '{}',
        is_active: true,
        version: '1.0'
      }
    });

    console.log('Created tutor chat prompts');
  } catch (error) {
    console.error('Error creating tutor chat prompts:', error.message);
  }

  console.log(`Seeding finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });