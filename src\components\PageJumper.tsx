'use client';
import React, { useState } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { ArrowRight, Hash } from 'lucide-react';

interface PageJumperProps {
  currentPage: number;
  totalPages: number;
  className?: string;
}

const PageJumper: React.FC<PageJumperProps> = ({ 
  currentPage, 
  totalPages, 
  className = '' 
}) => {
  const [inputPage, setInputPage] = useState('');
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const handleJumpToPage = () => {
    const pageNumber = parseInt(inputPage, 10);
    
    if (isNaN(pageNumber) || pageNumber < 1 || pageNumber > totalPages) {
      alert(`请输入有效的页数 (1-${totalPages})`);
      return;
    }

    const params = new URLSearchParams(searchParams);
    params.set('page', pageNumber.toString());
    
    router.push(`${pathname}?${params.toString()}`);
    setInputPage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers
    if (value === '' || /^\d+$/.test(value)) {
      setInputPage(value);
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-1 text-sm text-gray-600">
        <Hash className="h-4 w-4" />
        <span>跳转到</span>
      </div>
      
      <input
        type="text"
        value={inputPage}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        placeholder={`1-${totalPages}`}
        className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
        maxLength={totalPages.toString().length}
      />
      
      <span className="text-sm text-gray-600">页</span>
      
      <button
        onClick={handleJumpToPage}
        disabled={!inputPage}
        className="flex items-center space-x-1 px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
      >
        <span>跳转</span>
        <ArrowRight className="h-3 w-3" />
      </button>
    </div>
  );
};

export default PageJumper;
