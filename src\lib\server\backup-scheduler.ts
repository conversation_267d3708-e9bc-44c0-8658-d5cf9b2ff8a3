import { dbManager } from './database';

class BackupScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  /**
   * 启动自动备份调度器
   */
  async start() {
    if (this.isRunning) {
      console.log('备份调度器已在运行中');
      return;
    }

    const settings = await dbManager.getBackupSettings();
    
    if (!settings.enabled) {
      console.log('自动备份已禁用');
      return;
    }

    const intervalMs = settings.intervalHours * 60 * 60 * 1000; // 转换为毫秒
    
    console.log(`启动自动备份调度器，间隔: ${settings.intervalHours} 小时`);
    
    this.isRunning = true;
    
    // 立即执行一次备份检查
    this.performBackupCheck();
    
    // 设置定时器
    this.intervalId = setInterval(() => {
      this.performBackupCheck();
    }, intervalMs);
  }

  /**
   * 停止自动备份调度器
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('自动备份调度器已停止');
  }

  /**
   * 重启调度器（用于设置更新后）
   */
  async restart() {
    this.stop();
    await this.start();
  }

  /**
   * 检查是否需要执行备份
   */
  private async performBackupCheck() {
    try {
      const settings = await dbManager.getBackupSettings();
      
      if (!settings.enabled) {
        console.log('自动备份已禁用，停止调度器');
        this.stop();
        return;
      }

      // 获取最新的备份文件
      const backupFiles = await dbManager.getBackupFiles();
      const now = new Date();
      const intervalMs = settings.intervalHours * 60 * 60 * 1000;

      let shouldBackup = false;

      if (backupFiles.length === 0) {
        // 没有备份文件，需要创建
        shouldBackup = true;
        console.log('没有找到备份文件，将创建首次备份');
      } else {
        // 检查最新备份的时间
        const latestBackup = backupFiles[0]; // 已按时间排序
        const timeSinceLastBackup = now.getTime() - new Date(latestBackup.createdAt).getTime();
        
        if (timeSinceLastBackup >= intervalMs) {
          shouldBackup = true;
          console.log(`距离上次备份已过 ${Math.round(timeSinceLastBackup / (60 * 60 * 1000))} 小时，需要创建新备份`);
        }
      }

      if (shouldBackup) {
        console.log('开始执行自动备份...');
        const result = await dbManager.createDatabaseBackup();
        
        if (result.success) {
          console.log(`自动备份创建成功: ${result.filename}`);
          
          // 清理旧备份文件
          const cleanupResult = await dbManager.cleanupOldBackups();
          if (cleanupResult.success && cleanupResult.deletedCount && cleanupResult.deletedCount > 0) {
            console.log(`清理了 ${cleanupResult.deletedCount} 个旧备份文件`);
          }
        } else {
          console.error(`自动备份创建失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('执行备份检查时发生错误:', error);
    }
  }

  /**
   * 获取调度器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      hasInterval: this.intervalId !== null
    };
  }
}

// 创建全局实例
export const backupScheduler = new BackupScheduler();

// 在应用启动时自动启动调度器
if (typeof window === 'undefined') { // 只在服务器端运行
  // 延迟启动，确保数据库连接已建立
  setTimeout(() => {
    backupScheduler.start().catch(error => {
      console.error('启动备份调度器失败:', error);
    });
  }, 5000); // 5秒后启动
}
