import { NextRequest, NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';
import fs from 'fs';

// 删除备份文件
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;
    
    const result = await dbManager.deleteBackupFile(filename);
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: '备份文件删除成功'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 });
    }
  } catch (error: any) {
    console.error('删除备份文件失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// 下载备份文件
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;
    
    const filePath = dbManager.getBackupFilePath(filename);
    
    if (!filePath) {
      return NextResponse.json({
        success: false,
        error: '备份文件不存在'
      }, { status: 404 });
    }
    
    const fileBuffer = fs.readFileSync(filePath);
    
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (error: any) {
    console.error('下载备份文件失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
