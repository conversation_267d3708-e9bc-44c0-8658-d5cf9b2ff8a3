import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function createUser(name: string, email: string, password: string, role: string = 'user') {
  // 检查邮箱是否已存在
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    throw new Error('邮箱已被注册');
  }

  // 创建用户
  const user = await prisma.user.create({
    data: {
      name,
      email,
      password, // 直接存储原始密码
      role
    }
  });

  // 返回不包含密码的用户信息
  const { password: _, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

export async function getUserById(id: number) {
  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return null;
  }

  // 返回不包含密码的用户信息
  const { password: _, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

export async function getUserByEmail(email: string) {
  const user = await prisma.user.findUnique({
    where: { email }
  });

  if (!user) {
    return null;
  }

  // 返回不包含密码的用户信息
  const { password: _, ...userWithoutPassword } = user;
  return userWithoutPassword;
}

export async function updateUserProfile(id: number, data: { name?: string; email?: string }) {
  // 如果要更新邮箱，先检查是否已被使用
  if (data.email) {
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email }
    });

    if (existingUser && existingUser.id !== id) {
      throw new Error('邮箱已被注册');
    }
  }

  const updatedUser = await prisma.user.update({
    where: { id },
    data
  });

  // 返回不包含密码的用户信息
  const { password: _, ...userWithoutPassword } = updatedUser;
  return userWithoutPassword;
}

export async function changePassword(id: number, currentPassword: string, newPassword: string) {
  // 获取用户信息
  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    throw new Error('用户不存在');
  }

  // 验证当前密码
  if (currentPassword !== user.password) {
    throw new Error('当前密码不正确');
  }

  // 更新密码
  await prisma.user.update({
    where: { id },
    data: { password: newPassword } // 直接存储原始密码
  });

  return { success: true };
}