const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentDbStructure() {
  try {
    console.log('=== 检查当前数据库结构和数据 ===\n');

    // 检查 user_learning_records 表结构
    console.log('1. 检查 user_learning_records 表结构...');
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(user_learning_records)`;
    console.log('当前字段：');
    tableInfo.forEach(field => {
      console.log(`  - ${field.name}: ${field.type} ${field.notnull ? 'NOT NULL' : ''} ${field.dflt_value ? `DEFAULT ${field.dflt_value}` : ''}`);
    });

    console.log('\n2. 检查数据统计...');
    
    // 检查总记录数
    const totalRecords = await prisma.user_learning_records.count();
    console.log(`总学习记录数: ${totalRecords}`);

    // 检查状态分布
    const statusStats = await prisma.user_learning_records.groupBy({
      by: ['status'],
      _count: { id: true }
    });
    console.log('状态分布：');
    statusStats.forEach(stat => {
      console.log(`  - ${stat.status}: ${stat._count.id} 条`);
    });

    // 检查记录类型分布
    const typeStats = await prisma.user_learning_records.groupBy({
      by: ['record_type'],
      _count: { id: true }
    });
    console.log('记录类型分布：');
    typeStats.forEach(stat => {
      console.log(`  - ${stat.record_type}: ${stat._count.id} 条`);
    });

    // 检查是否已有 FSRS 字段
    console.log('\n3. 检查 FSRS 字段存在情况...');
    const fsrsFields = ['fsrs_due', 'fsrs_stability', 'fsrs_difficulty', 'fsrs_reps', 'fsrs_state'];
    const hasAnyFsrsField = tableInfo.some(field => fsrsFields.includes(field.name));
    
    if (hasAnyFsrsField) {
      console.log('⚠️  发现部分 FSRS 字段已存在');
      fsrsFields.forEach(fieldName => {
        const exists = tableInfo.some(field => field.name === fieldName);
        console.log(`  - ${fieldName}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
      });
    } else {
      console.log('✅ 未发现 FSRS 字段，可以安全添加');
    }

    // 检查旧字段存在情况
    console.log('\n4. 检查旧 SRS 字段存在情况...');
    const oldFields = ['last_reviewed_at', 'next_review_at', 'ease_factor', 'interval', 'repetitions'];
    oldFields.forEach(fieldName => {
      const exists = tableInfo.some(field => field.name === fieldName);
      console.log(`  - ${fieldName}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
    });

    // 检查其他重要表
    console.log('\n5. 检查其他重要数据...');
    const articleCount = await prisma.articles.count();
    const vocabularyCount = await prisma.vocabulary.count();
    const grammarCount = await prisma.grammar_points.count();
    
    console.log(`文章数量: ${articleCount}`);
    console.log(`词汇数量: ${vocabularyCount}`);
    console.log(`语法点数量: ${grammarCount}`);

    console.log('\n=== 检查完成 ===');

  } catch (error) {
    console.error('检查数据库结构失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentDbStructure();
