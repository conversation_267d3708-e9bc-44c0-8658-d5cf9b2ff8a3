# 学习统计面板功能说明

## 1. 功能概述

### 1.1 主要功能
学习统计面板是用户学习进度的可视化展示中心，提供个人学习数据分析、成就展示、学习趋势追踪等功能，帮助用户了解自己的学习状况并制定学习计划。

### 1.2 核心特性
- **学习概览**: 总体学习进度和成就展示
- **数据可视化**: 图表展示学习趋势和分布
- **个性化分析**: 基于用户行为的学习建议
- **目标跟踪**: 学习目标设定和完成情况
- **成就系统**: 学习里程碑和徽章系统
- **学习日历**: 每日学习活动记录

### 1.3 适用场景
- 用户查看个人学习进度
- 制定和调整学习计划
- 激励持续学习的动力
- 分析学习效果和薄弱环节

## 2. 使用指南

### 2.1 查看学习概览
1. 登录后进入Dashboard页面
2. 查看顶部的关键指标卡片
3. 了解总体学习进度和成就

### 2.2 分析学习数据
1. 查看词汇学习进度图表
2. 分析各JLPT等级的掌握情况
3. 查看学习活跃度趋势

### 2.3 设定学习目标
1. 点击目标设定区域
2. 设置每日/每周学习目标
3. 跟踪目标完成情况

### 2.4 查看成就系统
1. 浏览已获得的成就徽章
2. 查看进行中的挑战
3. 了解下一个成就的要求

## 3. 界面详解

### 3.1 Dashboard主页面 (`/dashboard`)

#### 页面布局
- **顶部**: 关键指标卡片区域
- **中部**: 图表和可视化区域
- **底部**: 详细数据和成就展示

#### 关键指标卡片
| 指标名称 | 数据来源 | 计算方式 | 说明 |
|----------|----------|----------|------|
| 学习天数 | user_learning_records.created_at | COUNT(DISTINCT DATE(created_at)) | 最近30天的活跃学习天数 |
| 已学词汇 | user_learning_records | COUNT(status != 'new') | 非新状态的词汇数量 |
| 掌握词汇 | user_learning_records | COUNT(status = 'mastered') | 已掌握的词汇数量 |
| 完成文章 | user_learning_records | COUNT(record_type = 'article', status = 'completed') | 已完成阅读的文章数量 |

#### 学习进度图表
| 图表类型 | 数据维度 | 数据源 | 说明 |
|----------|----------|--------|------|
| 词汇掌握分布 | JLPT等级 | vocabulary + user_learning_records | 各等级词汇的学习状态分布 |
| 学习活跃度 | 时间序列 | user_learning_records.created_at | 最近30天的学习活动趋势 |
| 文章阅读进度 | 分类统计 | articles + user_learning_records | 不同类别文章的阅读完成情况 |
| 学习效率分析 | 复习成功率 | user_learning_records.review_data | 词汇复习的正确率统计 |

### 3.2 词汇学习统计

#### 等级分布图表
```
N5: ████████████ 120/150 (80%)
N4: ████████░░░░ 95/180 (53%)
N3: ████░░░░░░░░ 45/220 (20%)
N2: ██░░░░░░░░░░ 20/280 (7%)
N1: ░░░░░░░░░░░░ 5/350 (1%)
```

#### 学习状态分布
| 状态 | 数据库值 | 显示颜色 | 统计方式 |
|------|----------|----------|----------|
| 新词汇 | 'new' | 灰色 | 未开始学习的词汇 |
| 学习中 | 'learning' | 蓝色 | 正在学习但未掌握 |
| 已掌握 | 'mastered' | 绿色 | 完全掌握的词汇 |
| 困难词汇 | 'difficult' | 红色 | 多次复习仍未掌握 |

### 3.3 学习活动日历

#### 日历视图
- **数据源**: user_learning_records.created_at
- **显示方式**: 热力图形式，颜色深度表示活跃度
- **交互功能**: 点击日期查看当日学习详情

#### 活跃度计算
```javascript
// 活跃度 = 当日学习记录数量
const activityLevel = dailyRecords.length;
const colorIntensity = Math.min(activityLevel / 10, 1); // 最大值为1
```

### 3.4 成就系统

#### 成就类别
| 类别 | 成就示例 | 触发条件 | 数据源 |
|------|----------|----------|--------|
| 学习里程碑 | "词汇达人" | 掌握100个词汇 | user_learning_records |
| 连续学习 | "坚持不懈" | 连续学习7天 | user_learning_records.created_at |
| 阅读成就 | "新闻通" | 阅读50篇文章 | user_learning_records |
| 等级突破 | "N4征服者" | 掌握N4级别80%词汇 | vocabulary + user_learning_records |

#### 成就数据结构
```sql
CREATE TABLE user_achievements (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  achievement_type TEXT NOT NULL,
  achievement_name TEXT NOT NULL,
  description TEXT,
  earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 4. 技术实现

### 4.1 核心代码文件

#### EnhancedDashboard组件
- **文件**: `src/components/EnhancedDashboard.tsx`
- **功能**: 增强版学习统计面板主组件
- **主要功能**:
  - 统计数据获取和展示
  - 图表组件集成
  - 响应式布局实现
  - 活动热力图和趋势图表
  - 智能学习建议

#### 数据获取服务
- **文件**: `src/lib/server/database.ts`
- **相关方法**:
  - `getLearningStats(userId)`: 获取学习统计数据
  - `getUserProgress(userId)`: 获取用户学习进度
  - `getVocabularyStats(userId)`: 获取词汇统计
  - `getStudyQueue(userId)`: 获取学习队列

#### 图表组件
- **文件**: `src/components/charts/`
- **包含组件**:
  - `ProgressChart.tsx`: 进度图表
  - `ActivityHeatmap.tsx`: 活动热力图
  - `VocabularyDistribution.tsx`: 词汇分布图

### 4.2 数据库查询逻辑

#### 学习统计查询
```sql
-- 获取用户学习统计
SELECT 
  COUNT(DISTINCT DATE(created_at)) as active_days,
  COUNT(CASE WHEN record_type = 'vocabulary' AND status != 'new' THEN 1 END) as learned_vocab,
  COUNT(CASE WHEN record_type = 'vocabulary' AND status = 'mastered' THEN 1 END) as mastered_vocab,
  COUNT(CASE WHEN record_type = 'article' AND status = 'completed' THEN 1 END) as completed_articles
FROM user_learning_records 
WHERE user_id = ? AND created_at >= date('now', '-30 days');
```

#### 词汇进度查询
```sql
-- 获取各等级词汇掌握情况
SELECT 
  v.jlpt_level,
  COUNT(*) as total,
  COUNT(ulr.id) as learned,
  COUNT(CASE WHEN ulr.status = 'mastered' THEN 1 END) as mastered
FROM vocabulary v
LEFT JOIN user_learning_records ulr ON v.id = ulr.vocabulary_id AND ulr.user_id = ?
GROUP BY v.jlpt_level
ORDER BY v.jlpt_level;
```

### 4.3 API接口

#### 获取学习统计
- **路径**: `GET /api/user/stats`
- **参数**: `{userId}`
- **返回**: 
```json
{
  "activeDays": 15,
  "learnedVocab": 245,
  "masteredVocab": 180,
  "completedArticles": 32
}
```

#### 获取学习进度
- **路径**: `GET /api/user/progress`
- **参数**: `{userId}`
- **返回**:
```json
{
  "vocabularyProgress": {
    "N5": {"total": 150, "mastered": 120, "percentage": 80},
    "N4": {"total": 180, "mastered": 95, "percentage": 53}
  },
  "articleProgress": {
    "total": 500,
    "completed": 32,
    "percentage": 6.4
  }
}
```

#### 获取学习活动
- **路径**: `GET /api/user/activity`
- **参数**: `{userId, startDate, endDate}`
- **返回**: 每日学习活动数据

## 5. 配置说明

### 5.1 统计配置

#### 时间范围设置
- **默认统计周期**: 30天
- **活动热力图**: 最近365天
- **趋势分析**: 最近90天

#### 成就系统配置
```javascript
const ACHIEVEMENTS = {
  VOCABULARY_MILESTONES: [10, 50, 100, 200, 500, 1000],
  READING_MILESTONES: [5, 20, 50, 100, 200],
  STREAK_MILESTONES: [3, 7, 14, 30, 60, 100]
};
```

### 5.2 可视化配置

#### 图表主题
- **颜色方案**: 基于Tailwind CSS调色板
- **响应式**: 支持移动端和桌面端
- **动画效果**: 使用Framer Motion

#### 数据刷新频率
- **实时数据**: 用户操作后立即更新
- **统计数据**: 每小时更新一次
- **历史数据**: 每日凌晨更新

## 6. 故障排除

### 6.1 常见问题

#### 统计数据不准确
**症状**: 显示的学习数据与实际不符
**解决方案**:
1. 检查user_learning_records表数据完整性
2. 确认统计查询逻辑正确
3. 清除缓存重新计算

#### 图表不显示
**症状**: 统计图表区域空白
**解决方案**:
1. 检查图表库是否正确加载
2. 确认数据格式符合图表要求
3. 检查浏览器控制台错误

#### 成就不触发
**症状**: 达到条件但成就未解锁
**解决方案**:
1. 检查成就触发逻辑
2. 确认数据库触发器正常
3. 手动重新计算成就状态

### 6.2 性能优化

#### 数据缓存策略
```javascript
// 缓存学习统计数据
const cacheKey = `user_stats_${userId}`;
const cachedStats = await redis.get(cacheKey);
if (!cachedStats) {
  const stats = await calculateUserStats(userId);
  await redis.setex(cacheKey, 3600, JSON.stringify(stats));
  return stats;
}
```

#### 查询优化
```sql
-- 创建复合索引优化查询
CREATE INDEX idx_user_learning_records_user_date 
ON user_learning_records(user_id, created_at);

CREATE INDEX idx_user_learning_records_user_type_status 
ON user_learning_records(user_id, record_type, status);
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
