#!/usr/bin/env node

/**
 * 从seed文件应用最新的AI提示词到数据库
 * 
 * 这个脚本读取seed文件中的提示词内容并更新到数据库中
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function applySeedPrompts() {
  console.log('🔄 从seed文件应用最新的AI提示词到数据库...');
  console.log('');

  try {
    // 文章分析主提示词 - 包含模板变量的完整版本
    const articleAnalysisPrompt = `提供された日本語ニュース記事の包括的な分析を行ってください。

以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出された重要な語彙の配列（5-10個）。各語彙には以下を含む：
   - word: 日本語の語彙
   - reading: ひらがな・カタカナの読み方
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語での意味
   - meaningEn: 英語での意味
   - explanation: 語彙の使用法やニュアンスの中国語説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語彙を使った一般的な連語・フレーズ（3-5個）
     各項目には collocation（日本語）、meaning（中国語意味）、example（例文と翻訳）を含む
   - relatedWords: 関連語彙（重要：すべての関連語彙に中国語翻訳を含める）
     * synonyms: 同義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "景気拡大", "reading": "けいきかくだい", "meaning": "景气扩大"}]
     * antonyms: 反義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "経済衰退", "reading": "けいざいすいたい", "meaning": "经济衰退"}]
     * hypernyms: 上位語の配列（より広い概念の語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
     * hyponyms: 下位語の配列（より具体的な語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「動物」の場合 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
     * wordFamily: 同じ漢字・語根を共有する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「食べ物」の場合 → [{"word": "食べる", "reading": "たべる", "meaning": "吃"}, {"word": "食堂", "reading": "しょくどう", "meaning": "食堂"}]
     * relatedConcepts: 概念的に関連する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "散歩", "reading": "さんぽ", "meaning": "散步"}, {"word": "餌", "reading": "えさ", "meaning": "饲料"}]
   - verbInfo: 動詞の場合のみ
     * type: 動詞の種類（例：「一段動詞」「五段動詞」「不規則動詞」）
     * forms: 活用形（例：「食べる」「食べます」「食べた」「食べて」）

4. **grammar**: 記事から特定された重要な文法項目の配列（3-5個）。各項目には以下を含む：
   - pattern: 文法パターン（例：「～について」）
   - reading: 文法パターンの読み方（漢字が含まれる場合）
   - explanation: 中国語での詳細で正確な説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
   - commonCollocations: 2-3個の常見搭配
   - similarGrammar: 3-5個の類似文法パターン（各項目に2つの例句と中国語翻訳を含む）
   - difficulty: JLPTレベル（例：「N4」）

5. **contentWithFurigana**: 記事本文の原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

6. **subtitleWithFurigana**: サブタイトルがある場合、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

分析対象の記事：
タイトル: {{{title}}}
{{#if subtitle}}
サブタイトル: {{{subtitle}}}
{{/if}}
本文（HTML形式）:
{{{content}}}

重要な注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数（3つ）を提供してください
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- Ruby標签格式：必ず<ruby>漢字<rt>よみ</rt></ruby>の完全な形式を使用し、漢字<rt>よみ</rt>のような不完全な形式は使用しないでください
- 語義ネットワークの関係は正確で教育的価値の高いものを選択してください
- **重要：relatedWordsのすべての語彙（synonyms、antonyms、hypernyms、hyponyms、wordFamily、relatedConcepts）には必ず中国語翻訳（meaning字段）を含めてください**`;

    // 更新文章分析主提示词
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_prompt' },
      data: {
        content: articleAnalysisPrompt,
        version: '4.0'
      }
    });

    console.log('✅ 文章分析主提示词已更新（包含模板变量）');

    console.log('');
    console.log('🎉 AI提示词应用完成！');
    console.log('');
    console.log('📋 更新内容：');
    console.log('1. ✅ 恢复了模板变量部分（分析対象の記事）');
    console.log('2. ✅ 保持了语义网络中文翻译功能');
    console.log('3. ✅ 版本号：v4.0');

  } catch (error) {
    console.error('❌ 应用AI提示词失败:', error);
    console.error('详细错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行应用
applySeedPrompts();
