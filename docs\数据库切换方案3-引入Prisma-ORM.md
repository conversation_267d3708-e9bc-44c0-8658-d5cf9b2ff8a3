# 数据库切换方案3: 引入 Prisma ORM - 详细实施步骤

## 1. 概述

本方案旨在提供一个通过引入专业 ORM 框架 **Prisma**，来实现灵活、类型安全的数据库管理的详细技术实现路线图。

**核心目标**: 将所有原生SQL查询重构为使用 Prisma Client，利用 Prisma 的能力轻松支持 SQLite 和 PostgreSQL，并获得完整的类型安全。

## 2. 实施步骤

### 步骤一: 安装与初始化

1.  **安装依赖 (`package.json`)**:
    *   运行以下命令安装 Prisma CLI 和 Prisma Client：
      ```bash
      npm install prisma --save-dev
      npm install @prisma/client
      ```

2.  **初始化 Prisma**:
    *   在项目根目录运行初始化命令：
      ```bash
      npx prisma init
      ```
    *   该命令会创建：
        *   `prisma/` 目录：用于存放 Prisma 相关文件。
        *   `prisma/schema.prisma`：**核心配置文件**，用于定义数据模型。
        *   `.env` 文件：自动添加 `DATABASE_URL` 环境变量。

### 步骤二: 定义 Prisma Schema

**目标文件**: `prisma/schema.prisma`

**改动内容**: 这是本次重构的**核心**。你需要将 `scripts/init-new-db-optimized.js` 中所有的 `CREATE TABLE` 语句，用 Prisma 的 schema 语法重新定义一遍。

1.  **配置数据源**:
    *   `schema.prisma` 文件的头部需要配置数据源。为了支持双数据库，可以这样配置：
      ```prisma
      generator client {
        provider = "prisma-client-js"
      }

      datasource db {
        provider = "postgresql" // 生产环境使用
        // provider = "sqlite" // 开发环境使用
        url      = env("DATABASE_URL")
      }
      ```
    *   你只需要通过注释来切换 `provider`，Prisma 就会自动生成对应方言的 SQL。

2.  **定义数据模型**:
    *   为数据库中的每一张表创建一个 `model`。Prisma 的语法直观易懂。
    *   **示例 (`articles` 表)**:
      ```prisma
      model articles {
        id                   Int       @id @default(autoincrement())
        rss_source_id        Int?
        guid                 String?   @unique
        title                String
        subtitle             String?
        content              String
        content_html         String?
        url                  String    @unique
        publish_time         DateTime?
        created_at           DateTime  @default(now())
        updated_at           DateTime  @updatedAt

        // 定义表之间的关系
        rss_source           rss_sources? @relation(fields: [rss_source_id], references: [id])
        ai_analysis_results  ai_analysis_results[]
        // ... 其他关系
      }

      model rss_sources {
        id        Int      @id @default(autoincrement())
        name      String
        url       String   @unique
        articles  articles[] // 反向关系
      }
      ```

### 步骤三: 数据库迁移

1.  **生成迁移文件**:
    *   在定义好所有模型后，运行迁移命令：
      ```bash
      npx prisma migrate dev --name "initial-migration"
      ```
2.  **Prisma 的作用**:
    *   Prisma 会读取 `schema.prisma` 文件。
    *   它会自动生成一个 `prisma/migrations` 目录，并在其中创建一个包含**纯SQL**的迁移文件。这个SQL文件是**针对你当前配置的 `provider` (SQLite 或 PostgreSQL) 生成的**，完美解决了SQL方言问题。
    *   它会自动将这个迁移应用到你的数据库。
    *   从此以后，`scripts/init-new-db-optimized.js` 文件不再需要，数据库的结构变更完全由 Prisma 的迁移系统管理。

### 步骤四: 重构数据访问层 (`DatabaseManager`)

**目标文件**: `src/lib/server/database.ts`

**改动内容**: 所有的原生SQL查询都将被类型安全的 Prisma Client 调用所取代。

1.  **引入 Prisma Client**:
    *   创建一个 Prisma Client 的单例实例。
      ```typescript
      import { PrismaClient } from '@prisma/client';
      const prisma = new PrismaClient();
      ```

2.  **重写查询方法**:
    *   **`getArticles` 方法**:
        *   **原逻辑**: `db.query('SELECT * FROM articles ...')`
        *   **新逻辑 (使用 Prisma Client)**:
          ```typescript
          async getArticles(filters: any) {
            return await prisma.articles.findMany({
              where: {
                category: filters.category,
                title: {
                  contains: filters.search,
                },
              },
              include: { // 类似 SQL JOIN
                rss_source: true,
              },
              orderBy: {
                publish_time: 'desc',
              },
              take: filters.limit,
            });
          }
          ```
    *   **`createArticle` 方法**:
        *   **原逻辑**: `db.run('INSERT INTO articles ...')`
        *   **新逻辑**:
          ```typescript
          async createArticle(data: any) {
            return await prisma.articles.create({
              data: {
                title: data.title,
                content: data.content,
                // ... 其他字段
              },
            });
          }
          ```
    *   **事务处理**: Prisma 的事务操作非常简单和安全。
        *   **新逻辑**:
          ```typescript
          await prisma.$transaction([
            prisma.articles.update({ where: { id: 1 }, data: { ... } }),
            prisma.vocabulary.create({ data: { ... } }),
          ]);
          ```

### 步骤五: 全面测试

*   由于数据访问层的实现已完全改变，需要对所有依赖 `dbManager` 的 Server Action 进行彻底的测试，确保业务逻辑在新架构下依然正确。

## 3. 总结

引入 Prisma 是一个架构级的升级。虽然初期的重构工作量（主要是重写数据查询）较大，但它带来了巨大的长期收益：
*   **数据库无关性**：轻松切换开发和生产数据库。
*   **类型安全**：在编译时就能发现大量的潜在错误。
*   **现代化的开发体验**：自动补全、清晰的API，大幅提升开发效率。
*   **健壮的迁移系统**：让数据库结构变更变得安全、可追溯。

对于一个需要长期维护和发展的项目来说，**方案3是技术上最先进、最稳妥的选择**。