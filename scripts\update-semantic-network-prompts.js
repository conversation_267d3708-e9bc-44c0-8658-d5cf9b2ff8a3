const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateSemanticNetworkPrompts() {
  console.log('开始更新AI提示词以支持语义网络分析...');

  try {
    // 更新文章分析主提示词，添加语义网络说明
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_prompt' },
      data: {
        content: `提供された日本語ニュース記事の包括的な分析を行ってください。

以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出された重要な語彙の配列（5-10個）。各語彙には以下を含む：
   - word: 日本語の語彙
   - reading: ひらがな・カタカナの読み方
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語での意味
   - meaningEn: 英語での意味
   - explanation: 語彙の使用法やニュアンスの中国語説明
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語彙を使った一般的な連語・フレーズ（3-5個）
     各項目には collocation（日本語）、meaning（中国語意味）、example（例文と翻訳）を含む
   - relatedWords: 関連語彙（オプション）
     * synonyms: 同義語の配列
     * antonyms: 反義語の配列
     * hypernyms: 上位語の配列（より広い概念の語彙）
       例：「犬」の場合 → ["動物", "哺乳類", "ペット"]
     * hyponyms: 下位語の配列（より具体的な語彙）
       例：「動物」の場合 → ["犬", "猫", "鳥", "魚"]
     * wordFamily: 同じ漢字・語根を共有する語彙の配列
       例：「食べ物」の場合 → ["食べる", "食堂", "食器", "食事"]
     * relatedConcepts: 概念的に関連する語彙の配列
       例：「犬」の場合 → ["散歩", "餌", "首輪", "獣医", "公園"]
   - verbInfo: 動詞の場合のみ
     * transitivity: 自動詞・他動詞の区別
     * conjugations: 主要な活用形

4. **grammar**: 記事から特定された重要な文法項目の配列（2-4個）。各項目には以下を含む：
   - pattern: 文法パターン（例：「～について」）
   - reading: 漢字を含む場合のひらがな読み（オプション）
   - explanation: 文法パターンの意味と使用法の中国語での詳細説明
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - similarGrammar: 類似文法パターンとその違い（2-3個）
     各項目には pattern（類似パターン）と difference（主要な違いの中国語説明）を含む

5. **contentWithFurigana**: 記事本文の原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

6. **subtitleWithFurigana**: サブタイトルが提供された場合のみ、すべての漢字に振り仮名を付けた文字列。

分析対象の記事：
タイトル: {{{title}}}
{{#if subtitle}}
サブタイトル: {{{subtitle}}}
{{/if}}
本文（HTML形式）:
{{{content}}}

重要な語義ネットワーク分析指示：
- hypernyms（上位語）: より広い概念・カテゴリーの語彙を提供
  例：「りんご」→「果物」→「食べ物」→「物」
- hyponyms（下位語）: より具体的・詳細な語彙を提供
  例：「動物」→「哺乳類」→「犬」→「柴犬」
- wordFamily（語族）: 同じ漢字や語根を共有する関連語彙
  例：「学」を含む語彙：「学校」「学生」「学習」「科学」
- relatedConcepts（関連概念）: 実際の使用場面で一緒に現れやすい語彙
  例：「学校」→「先生」「授業」「教室」「宿題」「試験」

注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数（3つ）を提供してください
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- Ruby標签格式：必ず<ruby>漢字<rt>よみ</rt></ruby>の完全な形式を使用し、漢字<rt>よみ</rt>のような不完全な形式は使用しないでください
- 語義ネットワークの関係は正確で教育的価値の高いものを選択してください`,
        version: '3.0'
      }
    });

    console.log('✅ 文章分析主提示词已更新，支持语义网络分析');

    // 更新系统提示词，添加语义网络相关说明
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_system' },
      data: {
        content: `あなたは日本語教育の専門家です。中国語話者の日本語学習者のために、日本語ニュース記事を分析することが目標です。

特に語彙分析において、以下の語義ネットワーク関係を正確に特定することが重要です：

1. **上位語（hypernyms）**: より広い概念・カテゴリーを表す語彙
   - 例：「犬」の上位語 → 「動物」「哺乳類」「ペット」
   - 例：「りんご」の上位語 → 「果物」「食べ物」

2. **下位語（hyponyms）**: より具体的・詳細な概念を表す語彙
   - 例：「動物」の下位語 → 「犬」「猫」「鳥」「魚」
   - 例：「果物」の下位語 → 「りんご」「みかん」「バナナ」

3. **語族（wordFamily）**: 同じ漢字や語根を共有する関連語彙
   - 例：「食」を含む語彙 → 「食べ物」「食事」「食堂」「食器」
   - 例：「学」を含む語彙 → 「学校」「学生」「学習」「科学」

4. **関連概念（relatedConcepts）**: 実際の使用場面で関連する語彙
   - 例：「学校」関連 → 「先生」「授業」「教室」「宿題」
   - 例：「犬」関連 → 「散歩」「餌」「首輪」「獣医」

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSONオブジェクトの前後に一切のテキストを追加しないでください
3. すべての出力は一貫性を保ち、完全で正確である必要があります
4. 振り仮名は必ずすべての漢字に付けてください
5. 例文は必ず指定された数だけ提供してください
6. 中国語の翻訳は自然で正確である必要があります
7. 語義ネットワークの関係は教育的価値が高く、学習者の理解を深めるものを選択してください`,
        version: '3.0'
      }
    });

    console.log('✅ 文章分析系统提示词已更新，支持语义网络分析');

    console.log('🎉 所有AI提示词更新完成！');
    console.log('');
    console.log('📋 更新内容总结：');
    console.log('1. ✅ 扩展了relatedWords字段，支持6种语义关系');
    console.log('2. ✅ 添加了详细的语义网络分析指导');
    console.log('3. ✅ 提供了具体的例子和格式要求');
    console.log('4. ✅ 强调了教育价值和准确性');
    console.log('');
    console.log('🔄 下一步：');
    console.log('1. 重启应用以加载新的提示词');
    console.log('2. 在提示词测试页面验证新功能');
    console.log('3. 测试AI是否能正确生成语义网络信息');

  } catch (error) {
    console.error('❌ 更新AI提示词失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行更新
updateSemanticNetworkPrompts()
  .catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
