import { Suspense } from 'react';
import { Loader } from 'lucide-react';
import VocabularyBankWrapper from './VocabularyBankWrapper';

function LoadingFallback() {
  return (
    <div className="flex h-full items-center justify-center py-12">
      <div className="text-center">
        <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
        <p className="text-gray-600">正在加载词汇库...</p>
      </div>
    </div>
  );
}

export default function VocabularyPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <VocabularyBankWrapper />
    </Suspense>
  );
}
