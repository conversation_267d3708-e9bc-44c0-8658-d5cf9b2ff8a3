const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createFsrsReviewLogsTable() {
  try {
    console.log('=== 创建 fsrs_review_logs 表 ===\n');

    // 检查表是否已存在
    try {
      await prisma.$queryRaw`SELECT 1 FROM fsrs_review_logs LIMIT 1`;
      console.log('✅ fsrs_review_logs 表已存在，无需创建');
      return;
    } catch (error) {
      console.log('📝 fsrs_review_logs 表不存在，开始创建...');
    }

    // 创建 fsrs_review_logs 表
    const createTableSQL = `
      CREATE TABLE "fsrs_review_logs" (
        "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
        "user_id" INTEGER NOT NULL,
        "learning_record_id" INTEGER NOT NULL,
        "rating" INTEGER NOT NULL,
        "state" TEXT NOT NULL,
        "due" DATETIME NOT NULL,
        "stability" REAL NOT NULL,
        "difficulty" REAL NOT NULL,
        "elapsed_days" INTEGER NOT NULL,
        "last_elapsed_days" INTEGER NOT NULL,
        "scheduled_days" INTEGER NOT NULL,
        "learning_steps" INTEGER NOT NULL,
        "review_time" DATETIME NOT NULL,
        "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "fsrs_review_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
        CONSTRAINT "fsrs_review_logs_learning_record_id_fkey" FOREIGN KEY ("learning_record_id") REFERENCES "user_learning_records" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
      )
    `;

    console.log('1. 创建表结构...');
    await prisma.$executeRawUnsafe(createTableSQL);
    console.log('✅ fsrs_review_logs 表创建成功');

    // 验证表创建
    console.log('\n2. 验证表结构...');
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(fsrs_review_logs)`;
    console.log('表字段：');
    tableInfo.forEach(field => {
      console.log(`  - ${field.name}: ${field.type} ${field.notnull ? 'NOT NULL' : ''} ${field.dflt_value ? `DEFAULT ${field.dflt_value}` : ''}`);
    });

    // 检查外键约束
    console.log('\n3. 检查外键约束...');
    const foreignKeys = await prisma.$queryRaw`PRAGMA foreign_key_list(fsrs_review_logs)`;
    console.log('外键约束：');
    foreignKeys.forEach(fk => {
      console.log(`  - ${fk.from} -> ${fk.table}.${fk.to}`);
    });

    console.log('\n=== fsrs_review_logs 表创建完成 ===');
    console.log('✅ 表结构正确');
    console.log('✅ 外键约束已设置');
    console.log('✅ 可以开始记录复习日志');

  } catch (error) {
    console.error('创建 fsrs_review_logs 表失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createFsrsReviewLogsTable();
