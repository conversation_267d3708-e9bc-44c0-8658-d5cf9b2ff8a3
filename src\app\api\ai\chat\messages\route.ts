import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// POST /api/ai/chat/messages - 添加聊天消息
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { sessionId, role, content, metadata } = body;
    
    // 验证必需字段
    if (!sessionId || !role || !content) {
      return NextResponse.json(
        { 
          success: false, 
          error: '缺少必需字段: sessionId, role, content' 
        },
        { status: 400 }
      );
    }

    // 验证role值
    if (!['user', 'assistant', 'system'].includes(role)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'role必须是user、assistant或system之一' 
        },
        { status: 400 }
      );
    }
    
    const message = await dbManager.addChatMessage(
      sessionId, 
      role, 
      content, 
      metadata ? JSON.stringify(metadata) : undefined
    );
    
    return NextResponse.json({
      success: true,
      data: message
    }, { status: 201 });
  } catch (error: any) {
    console.error('添加聊天消息失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '添加聊天消息失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// GET /api/ai/chat/messages?sessionId=xxx - 获取会话的消息列表
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const limit = searchParams.get('limit');
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: '缺少sessionId参数' },
        { status: 400 }
      );
    }

    const sessionIdInt = parseInt(sessionId);
    if (isNaN(sessionIdInt)) {
      return NextResponse.json(
        { success: false, error: '无效的sessionId' },
        { status: 400 }
      );
    }
    
    const messages = await dbManager.getChatMessages(
      sessionIdInt, 
      limit ? parseInt(limit) : undefined
    );
    
    return NextResponse.json({
      success: true,
      data: messages
    });
  } catch (error: any) {
    console.error('获取聊天消息失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取聊天消息失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}
