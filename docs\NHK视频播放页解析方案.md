# NHK视频播放页解析与抓取方案 (最终修正版)

## 1. 问题背景

在抓取NHK新闻时，我们发现部分新闻的视频链接并非指向直接的媒体文件（如 `.mp4`），而是指向一个内嵌了视频播放器的独立HTML页面。

**当前问题**:
我们现有的抓取逻辑在遇到这种HTML页面链接时，无法从中提取出真实的视频源文件URL，导致视频无法被下载到本地，影响了学习资源的持久化。

**目标**:
制定一个可靠的、经过验证的方案，能够智能地解析这种内嵌播放器的HTML页面，并成功提取、下载其中的视频文件。

## 2. 案例分析 (最终修正)

以用户提供的URL为例进行最终分析：
`https://www3.nhk.or.jp/news/html/20250701/movie/k10014849441_202507011209_202507011209.html`

### 2.1 页面结构分析

经过对该页面HTML源代码的最终、彻底的检查，我们确认了以下事实：

- **之前的假设是错误的**：页面中确实不存在一个固定的、名为 `fms_video_path` 的JavaScript变量。
- **不存在明文的.mp4链接**：在HTML的顶层结构或用户可见的标签中，确实没有直接的 `.mp4` 链接。

**核心发现**:
真正的`.mp4`文件地址被硬编码为一个字符串字面量，存储在页面一个`<script>`标签内的JavaScript代码里，并被赋给一个动态的或不固定的变量。

**结论**:
要成功提取这个URL，我们**不能依赖任何固定的变量名**。我们的抓取器不能再简单地把 `.../movie/...html` 这样的链接当作视频文件，而是必须将其视为一个“情报页面”，并从这个页面中智能地提取出真实的视频URL。

## 3. 解决方案设计 (最终方案)

为了解决这个问题，我提议在我们的抓取脚本 `src/lib/server/scraper.ts` 中的 `extractAndDownloadEmbeddedVideo` 函数内，实施以下经过验证的智能提取逻辑：

1.  **访问播放页**: 抓取器首先正常请求用户提供的HTML播放页URL。

2.  **内容提取**: 获取到HTML页面的完整文本内容。

3.  **智能正则匹配**: 使用一个通用的正则表达式来从页面的 **`<script>` 标签文本**中捕获视频文件的URL。一个健壮的表达式是：
    -   `/"([^"]+\.mp4)"/`
    -   这个表达式会安全地匹配任何被双引号包裹的、以 `.mp4` 结尾的字符串，无论它被赋给什么变量。这确保了即使NHK未来修改脚本中的变量名，我们的抓取逻辑依然有效。

4.  **URL补全**:
    -   检查提取到的URL。如果它是以`//`开头的协议相对地址，就在其前面拼接`https:`。
    -   如果它是以`/`开头的绝对路径，就与`https://www3.nhk.or.jp`拼接。
    -   如果它已经是完整的URL，则直接使用。

5.  **下载视频**: 使用经过补全的、真实的`.mp4`文件URL，调用现有的`downloadMedia`函数来下载视频文件到本地。

6.  **错误处理**: 如果在以上任何步骤中失败（例如，没能匹配到`.mp4`链接），则记录一条警告日志，并返回`null`，表示该视频无法下载。

## 4. 结论

通过实施这个最终修正的方案，我们的抓取器将具备处理两种视频链接的能力：
-   **直接链接**: 直接下载媒体文件。
-   **内嵌播放页链接**: 先解析页面，通过灵活的正则匹配提取真实URL，再进行下载。

这将极大地提高视频抓取的成功率和系统的健壮性，确保学习资源的完整性和持久性，完美地满足了用户的需求。
