
'use client';
import React, { useState, useEffect, useTransition } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  TestTube, 
  Globe, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Save,
  X,
  Loader,
  FileJson,
  PlusCircle,
  MinusCircle,
  Sparkles
} from 'lucide-react';
import { 
    saveRSSSourceAction, 
    deleteRSSSourceAction, 
    toggleRSSSourceActiveAction,
    testRSSSourceAction
} from '@/app/actions';

interface RSSSource {
  id: number;
  name: string;
  url: string;
  description: string | null;
  category: string | null;
  language: string | null;
  is_active: boolean;
  max_articles: number | null;
  enable_ai_processing: boolean;
  content_selector: string | null;
  last_fetch_time: Date | string | null;
  last_fetch_count: number | null;
  total_fetched: number | null;
  success_rate: number | null;
  created_at: Date | string;
  updated_at: Date | string;
  fieldMappings?: FieldMapping[];
}

interface FieldMapping {
  id?: number;
  rss_source_id?: number;
  field_name: string;
  xpath_selector: string;
  css_selector?: string;
  attribute_name?: string;
  default_value?: string;
  is_required: boolean;
  transform_rule?: string;
}

interface RSSSourceManagerProps {
    initialSources: RSSSource[];
}

const RSSSourceManager: React.FC<RSSSourceManagerProps> = ({ initialSources }) => {
  const [sources, setSources] = useState<RSSSource[]>(initialSources);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSource, setEditingSource] = useState<RSSSource | null>(null);
  const [testingSource, setTestingSource] = useState<number | null>(null);
  const [testResults, setTestResults] = useState<any>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'fieldMappings'>('basic');
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [aiAnalyzing, setAiAnalyzing] = useState(false);
  const [isPending, startTransition] = useTransition();

  // 新RSS源表单数据
  const [formData, setFormData] = useState({
    id: null as number | null,
    name: '',
    url: '',
    description: '',
    category: '综合新闻',
    language: 'ja',
    max_articles: 15,
    enable_ai_processing: false,
    content_selector: '.article-body, .content-body, article, .news-content',
    is_active: true
  });

  // 默认字段映射
  const defaultFieldMappings: FieldMapping[] = [
    {
      field_name: 'title', xpath_selector: '//item/title', css_selector: 'item > title', is_required: true,
      transform_rule: JSON.stringify({ type: 'text', trim: true, maxLength: 200 })
    },
    {
      field_name: 'link', xpath_selector: '//item/link', css_selector: 'item > link', is_required: true,
      transform_rule: JSON.stringify({ type: 'url', makeAbsolute: true })
    },
    {
      field_name: 'description', xpath_selector: '//item/description', css_selector: 'item > description', is_required: false,
      transform_rule: JSON.stringify({ type: 'html', stripTags: true })
    },
    {
      field_name: 'pub_date', xpath_selector: '//item/pubDate', css_selector: 'item > pubDate', is_required: false,
      transform_rule: JSON.stringify({ type: 'datetime', inputFormat: 'RFC2822' })
    },
    {
      field_name: 'guid', xpath_selector: '//item/guid', css_selector: 'item > guid', is_required: false,
      transform_rule: JSON.stringify({ type: 'text', trim: true })
    },
    {
      field_name: 'image_url', xpath_selector: '//item/enclosure[@type="image/jpeg"]/@url', css_selector: 'item > enclosure[type="image/jpeg"]', attribute_name: 'url', is_required: false,
      transform_rule: JSON.stringify({ type: 'url', makeAbsolute: true })
    }
  ];

  useEffect(() => {
    setSources(initialSources);
  }, [initialSources]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();

    // 验证必填字段
    if (!formData.name?.trim()) {
      alert('请输入RSS源名称');
      return;
    }
    if (!formData.url?.trim()) {
      alert('请输入RSS源URL');
      return;
    }
    if (!formData.category?.trim()) {
      alert('请输入分类信息');
      return;
    }

    startTransition(async () => {
        const result = await saveRSSSourceAction(formData, fieldMappings);
        if (result.success) {
            setShowAddForm(false);
            setEditingSource(null);
            // Revalidation will happen through router.refresh() or similar in a full app
        } else {
            console.error(`Save failed: ${result.error}`);
        }
    });
  };

  const handleEdit = (source: RSSSource) => {
    setFormData({
      id: source.id,
      name: source.name,
      url: source.url,
      description: source.description || '',
      category: source.category || '',
      language: source.language || '',
      max_articles: source.max_articles || 10,
      enable_ai_processing: source.enable_ai_processing,
      content_selector: source.content_selector || '',
      is_active: source.is_active
    });
    setEditingSource(source);
    setShowAddForm(true);
    setActiveTab('basic');
    setFieldMappings(source.fieldMappings || defaultFieldMappings);
  };

  const handleDelete = (id: number) => {
    startTransition(() => {
        deleteRSSSourceAction(id);
    });
  };

  const handleTest = async (source: RSSSource) => {
    setTestingSource(source.id);
    const result = await testRSSSourceAction(source.url);
    setTestResults((prev:any) => ({
        ...prev,
        [source.id]: result.success ? result.data : { success: false, message: result.error }
    }));
    setTestingSource(null);
  };

  const handleToggleActive = (source: RSSSource) => {
    startTransition(() => {
        toggleRSSSourceActiveAction(source);
    });
  };

  const handleFieldMappingChange = (index: number, field: string, value: any) => {
    const newMappings = [...fieldMappings];
    newMappings[index] = { ...newMappings[index], [field]: value };
    setFieldMappings(newMappings);
  };

  const addNewFieldMapping = () => {
    setFieldMappings([...fieldMappings, {
        field_name: '', xpath_selector: '', css_selector: '', attribute_name: '', default_value: '', is_required: false,
        transform_rule: JSON.stringify({ type: 'text', trim: true }),
        rss_source_id: editingSource?.id
    }]);
  };

  const removeFieldMapping = (index: number) => {
    setFieldMappings(fieldMappings.filter((_, i) => i !== index));
  };

  const analyzeRSSWithAI = async () => {
    if (!formData.url) {
        console.error('请先输入RSS源URL');
        return;
    }
    setAiAnalyzing(true);
    const result = await testRSSSourceAction(formData.url);

    if (result.success && result.data) {
        const testData = result.data;
        const sampleItem = testData.sampleItems?.[0];

        // 从RSS条目的category字段提取分类信息
        let extractedCategory = '';
        if (sampleItem?.category) {
            extractedCategory = sampleItem.category;
        }

        setFormData(prev => ({
            ...prev,
            name: testData.feedTitle || prev.name,
            description: testData.feedDescription || prev.description,
            category: extractedCategory || prev.category
        }));

        if (sampleItem) {
            // Simplified mapping generation
            const newMappings: FieldMapping[] = defaultFieldMappings.filter(m => sampleItem[m.field_name]);
            setFieldMappings(newMappings.length > 0 ? newMappings : defaultFieldMappings);
        }
        console.log('AI分析完成！');
    } else {
        console.error(`AI分析失败: ${result.error}`);
    }
    setAiAnalyzing(false);
  };


  


  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">RSS源管理</h2>
          <p className="text-gray-600 mt-1">管理新闻数据源，配置RSS源和字段映射规则</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => {
              setFormData({ id: null, name: '', url: '', description: '', category: '', language: 'ja', max_articles: 15, enable_ai_processing: false, content_selector: '.article-body, .content-body, article, .news-content', is_active: true });
              setEditingSource(null);
              setFieldMappings(defaultFieldMappings);
              setShowAddForm(true);
              setActiveTab('basic');
            }}
            disabled={isPending}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>添加RSS源</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {sources.map((source) => (
          <div key={source.id} className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{source.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${source.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>{source.is_active ? '活跃' : '停用'}</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">{source.category}</span>
                  {source.enable_ai_processing && <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">AI处理</span>}
                </div>
                <p className="text-sm text-blue-600 break-all mb-3">{source.url}</p>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button onClick={() => handleTest(source)} disabled={testingSource === source.id || isPending} className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors">
                  {testingSource === source.id ? <Loader className="h-4 w-4 animate-spin" /> : <TestTube className="h-4 w-4" />}<span>测试</span>
                </button>
                <button onClick={() => handleToggleActive(source)} disabled={isPending} className={`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed ${source.is_active ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`}>
                  {source.is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}<span>{source.is_active ? '停用' : '启用'}</span>
                </button>
                <button onClick={() => handleEdit(source)} disabled={isPending} className="flex items-center space-x-1 px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg hover:bg-indigo-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors">
                  <Edit className="h-4 w-4" /><span>编辑</span>
                </button>
                <button onClick={() => handleDelete(source.id)} disabled={isPending} className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg disabled:text-gray-400 disabled:cursor-not-allowed transition-all"><Trash2 className="h-4 w-4" /></button>
              </div>
            </div>
            {testResults[source.id] && (
              <div className={`mt-4 p-3 rounded-lg border ${testResults[source.id].success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-center space-x-2 mb-2">
                  {testResults[source.id].success ? <CheckCircle className="h-4 w-4 text-green-600" /> : <AlertCircle className="h-4 w-4 text-red-600" />}
                  <span className={`text-sm font-medium ${testResults[source.id].success ? 'text-green-800' : 'text-red-800'}`}>{testResults[source.id].message}</span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {sources.length === 0 && (
        <div className="text-center py-12"><Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" /><h3 className="text-lg font-medium text-gray-900 mb-2">没有RSS源</h3><p className="text-gray-600 mb-4">点击"添加RSS源"按钮来添加第一个新闻源</p></div>
      )}

      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{editingSource ? '编辑RSS源' : '添加RSS源'}</h3>
                <button onClick={() => { setShowAddForm(false); setEditingSource(null); }} className="p-2 text-gray-400 hover:text-gray-600"><X className="h-5 w-5" /></button>
              </div>
              <div className="border-b border-gray-200 mb-6">
                <nav className="-mb-px flex space-x-8">
                  <button onClick={() => setActiveTab('basic')} className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'basic' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300'}`}>基本信息</button>
                  <button onClick={() => setActiveTab('fieldMappings')} className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'fieldMappings' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300'}`}>字段映射配置</button>
                </nav>
              </div>
              <form onSubmit={handleSave}>
                {activeTab === 'basic' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">名称</label>
                        <input type="text" value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">URL</label>
                        <div className="flex">
                            <input type="url" value={formData.url} onChange={(e) => setFormData({...formData, url: e.target.value})} className="flex-grow px-3 py-2 border border-gray-300 rounded-l-lg" required />
                            <button type="button" onClick={analyzeRSSWithAI} disabled={aiAnalyzing} className="px-3 py-2 bg-purple-100 text-purple-700 rounded-r-lg border-t border-b border-r border-purple-200 hover:bg-purple-200 disabled:opacity-50">
                                {aiAnalyzing ? <Loader className="h-4 w-4 animate-spin"/> : <Sparkles className="h-4 w-4"/>}
                            </button>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                      <textarea value={formData.description} onChange={(e) => setFormData({...formData, description: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" rows={2}></textarea>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          分类 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={formData.category || ''}
                          onChange={(e) => setFormData({...formData, category: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="请输入分类名称"
                          required
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          该RSS源的所有文章将使用此分类
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">每次抓取最大文章数</label>
                        <input type="number" value={formData.max_articles} onChange={(e) => setFormData({...formData, max_articles: parseInt(e.target.value)})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">正文内容选择器</label>
                      <input type="text" value={formData.content_selector} onChange={(e) => setFormData({...formData, content_selector: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder=".article-body, .content, article" />
                    </div>
                     <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                           <input id="is_active" type="checkbox" checked={formData.is_active} onChange={(e) => setFormData({...formData, is_active: e.target.checked})} className="h-4 w-4 text-indigo-600 border-gray-300 rounded" />
                           <label htmlFor="is_active" className="text-sm font-medium text-gray-700">启用此源</label>
                        </div>
                        <div className="flex items-center space-x-2">
                           <input id="enable_ai_processing" type="checkbox" checked={formData.enable_ai_processing} onChange={(e) => setFormData({...formData, enable_ai_processing: e.target.checked})} className="h-4 w-4 text-indigo-600 border-gray-300 rounded" />
                           <label htmlFor="enable_ai_processing" className="text-sm font-medium text-gray-700">启用AI处理</label>
                        </div>
                    </div>
                  </div>
                )}
                {activeTab === 'fieldMappings' && (
                  <div className="space-y-6">
                     <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800">
                        <p>字段映射用于从RSS源XML中精确提取数据。支持XPath和CSS选择器。XPath优先级更高。</p>
                    </div>
                    {fieldMappings.map((mapping, index) => (
                      <div key={index} className="p-4 border border-gray-200 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-xs font-medium text-gray-500 mb-1">目标字段</label>
                            <input type="text" value={mapping.field_name} onChange={(e) => handleFieldMappingChange(index, 'field_name', e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm" />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 mb-1">XPath 选择器</label>
                            <input type="text" value={mapping.xpath_selector} onChange={(e) => handleFieldMappingChange(index, 'xpath_selector', e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm" />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 mb-1">CSS 选择器 (备用)</label>
                            <input type="text" value={mapping.css_selector || ''} onChange={(e) => handleFieldMappingChange(index, 'css_selector', e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm" />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 mb-1">属性名称 (可选)</label>
                            <input type="text" value={mapping.attribute_name || ''} onChange={(e) => handleFieldMappingChange(index, 'attribute_name', e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm" placeholder="e.g., href, src"/>
                          </div>
                        </div>
                        <div className="mt-4 flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                               <input id={`is_required_${index}`} type="checkbox" checked={mapping.is_required} onChange={(e) => handleFieldMappingChange(index, 'is_required', e.target.checked)} className="h-4 w-4 text-indigo-600 border-gray-300 rounded" />
                               <label htmlFor={`is_required_${index}`} className="text-sm font-medium text-gray-700">必需字段</label>
                            </div>
                           <button type="button" onClick={() => removeFieldMapping(index)} className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"><MinusCircle className="h-5 w-5" /></button>
                        </div>
                      </div>
                    ))}
                    <button type="button" onClick={addNewFieldMapping} className="flex items-center space-x-2 text-sm text-indigo-600 hover:text-indigo-800 font-medium">
                        <PlusCircle className="h-5 w-5" />
                        <span>添加映射字段</span>
                    </button>
                  </div>
                )}
                <div className="flex justify-end space-x-3 pt-4 mt-6 border-t border-gray-200">
                  <button type="button" onClick={() => { setShowAddForm(false); setEditingSource(null); }} className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                  <button type="submit" disabled={isPending} className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400">
                    {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}<span>{editingSource ? '更新' : '添加'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RSSSourceManager;
