'use client';
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NewsContextType {
  selectedArticle: any | null;
  setSelectedArticle: (article: any | null) => void;
  resetToNewsList: () => void;
}

const NewsContext = createContext<NewsContextType | undefined>(undefined);

export function NewsProvider({ children }: { children: ReactNode }) {
  const [selectedArticle, setSelectedArticle] = useState<any | null>(null);

  const resetToNewsList = () => {
    setSelectedArticle(null);
  };

  return (
    <NewsContext.Provider value={{
      selectedArticle,
      setSelectedArticle,
      resetToNewsList
    }}>
      {children}
    </NewsContext.Provider>
  );
}

export function useNews() {
  const context = useContext(NewsContext);
  if (context === undefined) {
    throw new Error('useNews must be used within a NewsProvider');
  }
  return context;
}

// 安全版本的useNews，不会抛出错误
export function useNewsSafe() {
  const context = useContext(NewsContext);
  return context; // 可能为undefined
}
