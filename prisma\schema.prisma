generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
  engineType      = "library"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model rss_sources {
  id                   Int                  @id @default(autoincrement())
  name                 String
  url                  String               @unique
  description          String?
  category             String?
  language             String?              @default("ja")
  is_active            Boolean              @default(true)
  max_articles         Int?                 @default(10)
  enable_ai_processing Boolean              @default(false)
  content_selector     String?
  last_fetch_time      DateTime?
  last_fetch_count     Int?                 @default(0)
  total_fetched        Int?                 @default(0)
  success_rate         Float?               @default(0.0)
  created_at           DateTime             @default(now())
  updated_at           DateTime             @updatedAt
  articles             articles[]
  rss_field_mappings   rss_field_mappings[]
  scraping_logs        scraping_logs[]
}

model rss_field_mappings {
  id             Int         @id @default(autoincrement())
  rss_source_id  Int
  field_name     String
  xpath_selector String?
  css_selector   String?
  attribute_name String?
  default_value  String?
  is_required    <PERSON><PERSON><PERSON>     @default(false)
  transform_rule String?
  created_at     DateTime    @default(now())
  rss_source     rss_sources @relation(fields: [rss_source_id], references: [id], onDelete: Cascade)
}

model articles {
  id                     Int                      @id @default(autoincrement())
  rss_source_id          Int?
  guid                   String?                  @unique
  title                  String
  subtitle               String?
  content                String?
  content_html           String?
  url                    String                   @unique
  publish_time           DateTime?
  fetch_time             DateTime?                @default(now())
  featured_image_url     String?
  featured_image_path    String?
  video_url              String?
  video_path             String?
  video_metadata_json    String?
  video_m3u8_content     String?
  video_download_status  String?                  @default("disabled")
  use_local_video        Boolean?                 @default(false)
  audio_url              String?
  audio_path             String?
  ai_processing_status   String                   @default("pending")
  ai_processed_at        DateTime?
  ai_processing_error    String?
  processing_status      String                   @default("pending")
  title_furigana_html    String?
  content_furigana_html  String?
  subtitle_furigana_html String?
  created_at             DateTime                 @default(now())
  updated_at             DateTime                 @updatedAt
  ai_processing_queue    ai_processing_queue[]
  article_grammar_points article_grammar_points[]
  article_translations   article_translations[]
  article_vocabulary     article_vocabulary[]
  rss_source             rss_sources?             @relation(fields: [rss_source_id], references: [id])
  media_files            media_files[]
  user_article_reads     user_article_reads[]
  user_learning_records  user_learning_records[]
}

model article_translations {
  id                  Int      @id @default(autoincrement())
  article_id          Int
  language_code       String
  translated_title    String?
  translated_subtitle String?
  translated_content  String?
  translation_method  String?  @default("ai")
  quality_score       Float?
  created_at          DateTime @default(now())
  article             articles @relation(fields: [article_id], references: [id], onDelete: Cascade)

  @@unique([article_id, language_code])
}

model vocabulary {
  id                       Int                     @id @default(autoincrement())
  word                     String
  reading                  String?
  meaning_zh               String?
  meaning_en               String?
  part_of_speech           String?
  jlpt_level               String?
  frequency_rank           Int?
  difficulty_score         Float?
  extraction_method        String?                 @default("ai")
  related_words_json       String?
  verb_info_json           String?
  common_collocations_json String?
  explanation_ja           String?
  created_at               DateTime                @default(now())
  article_vocabulary       article_vocabulary[]
  user_learning_records    user_learning_records[]

  @@unique([word, reading])
}

model article_vocabulary {
  id                    Int        @id @default(autoincrement())
  article_id            Int
  vocabulary_id         Int
  position_start        Int?
  position_end          Int?
  context               String?
  is_key_vocabulary     Boolean?   @default(false)
  extraction_confidence Float?
  created_at            DateTime   @default(now())
  vocabulary            vocabulary @relation(fields: [vocabulary_id], references: [id])
  article               articles   @relation(fields: [article_id], references: [id], onDelete: Cascade)

  @@unique([article_id, vocabulary_id, position_start])
}

model grammar_points {
  id                       Int                      @id @default(autoincrement())
  pattern                  String                   @unique
  name                     String?
  reading                  String?
  meaning_zh               String?
  meaning_en               String?
  explanation              String?
  explanation_ja           String?
  common_collocations_json String?
  jlpt_level               String?
  difficulty_score         Float?
  usage_frequency          Int?                     @default(0)
  extraction_method        String?                  @default("ai")
  similar_grammar_json     String?
  examples_json            String?
  created_at               DateTime                 @default(now())
  article_grammar_points   article_grammar_points[]
  user_learning_records    user_learning_records[]
}

model article_grammar_points {
  id               Int            @id @default(autoincrement())
  article_id       Int
  grammar_point_id Int
  created_at       DateTime       @default(now())
  grammar_point    grammar_points @relation(fields: [grammar_point_id], references: [id])
  article          articles       @relation(fields: [article_id], references: [id], onDelete: Cascade)

  @@unique([article_id, grammar_point_id])
}

model User {
  id               Int                     @id @default(autoincrement())
  name             String
  email            String                  @unique
  password         String
  role             String                  @default("user")
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  chat_sessions    chat_sessions[]
  fsrs_review_logs fsrs_review_logs[]
  article_reads    user_article_reads[]
  learning_records user_learning_records[]
  user_settings    user_settings[]
}

model ai_prompts {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  type        String
  category    String
  title       String
  description String?
  content     String
  variables   String?
  is_active   Boolean  @default(true)
  version     String   @default("1.0")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
}

model user_learning_records {
  id                  Int                @id @default(autoincrement())
  user_id             Int
  article_id          Int?
  vocabulary_id       Int?
  grammar_point_id    Int?
  record_type         String
  status              String             @default("new")
  fsrs_due            DateTime?
  fsrs_stability      Float?
  fsrs_difficulty     Float?
  fsrs_elapsed_days   Int?
  fsrs_scheduled_days Int?
  fsrs_learning_steps Int?
  fsrs_reps           Int?               @default(0)
  fsrs_lapses         Int?               @default(0)
  fsrs_state          String?            @default("New")
  fsrs_last_review    DateTime?
  created_at          DateTime           @default(now())
  updated_at          DateTime           @updatedAt
  fsrs_review_logs    fsrs_review_logs[]
  grammar_point       grammar_points?    @relation(fields: [grammar_point_id], references: [id])
  vocabulary          vocabulary?        @relation(fields: [vocabulary_id], references: [id])
  article             articles?          @relation(fields: [article_id], references: [id])
  user                User               @relation(fields: [user_id], references: [id])

  @@unique([user_id, record_type, article_id, vocabulary_id, grammar_point_id], map: "user_learning_records_unique_learning_record_key")
}

model scraping_logs {
  id            Int          @id @default(autoincrement())
  rss_source_id Int?
  session_id    String?
  log_level     String?      @default("INFO")
  message       String
  url           String?
  created_at    DateTime     @default(now())
  rss_source    rss_sources? @relation(fields: [rss_source_id], references: [id])
}

model ai_processing_queue {
  id              Int       @id @default(autoincrement())
  article_id      Int
  processing_type String
  priority        Int?      @default(0)
  status          String    @default("pending")
  retry_count     Int?      @default(0)
  error_message   String?
  scheduled_at    DateTime? @default(now())
  started_at      DateTime?
  completed_at    DateTime?
  created_at      DateTime  @default(now())
  article         articles  @relation(fields: [article_id], references: [id], onDelete: Cascade)
}

model system_settings {
  id    Int    @id @default(autoincrement())
  key   String @unique
  value String
}

model user_settings {
  id         Int      @id @default(autoincrement())
  user_id    Int
  key        String
  value      String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, key])
}

model ai_models {
  id           Int      @id @default(autoincrement())
  model_id     String   @unique
  display_name String?
  description  String?
  model_type   String?
  is_enabled   Boolean  @default(true)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
}

model api_keys {
  id                           Int       @id @default(autoincrement())
  name                         String
  api_key                      String    @unique
  provider                     String    @default("google_gemini")
  priority                     Int
  is_active                    Boolean   @default(true)
  last_used_at                 DateTime?
  last_error                   String?
  quota_reset_interval_minutes Int       @default(60)
  min_usage_interval_seconds   Int       @default(1)
  created_at                   DateTime  @default(now())
}

model chat_sessions {
  id              Int             @id @default(autoincrement())
  user_id         Int
  session_type    String          @default("qa")
  title           String?
  scenario_config String?
  created_at      DateTime        @default(now())
  updated_at      DateTime        @updatedAt
  chat_messages   chat_messages[]
  user            User            @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model chat_messages {
  id         Int           @id @default(autoincrement())
  session_id Int
  role       String
  content    String
  metadata   String?
  created_at DateTime      @default(now())
  session    chat_sessions @relation(fields: [session_id], references: [id], onDelete: Cascade)
}

model media_files {
  id           Int       @id @default(autoincrement())
  file_path    String    @unique
  file_name    String
  file_size    Int
  file_type    String
  mime_type    String?
  article_id   Int?
  download_url String?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  article      articles? @relation(fields: [article_id], references: [id])

  @@index([file_type])
  @@index([article_id])
  @@index([created_at])
}

model user_article_reads {
  id         Int      @id @default(autoincrement())
  user_id    Int
  article_id Int
  read_at    DateTime @default(now())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  article    articles @relation(fields: [article_id], references: [id])
  user       User     @relation(fields: [user_id], references: [id])

  @@unique([user_id, article_id])
  @@index([user_id])
  @@index([article_id])
  @@index([read_at])
}

model fsrs_review_logs {
  id                    Int                   @id @default(autoincrement())
  user_id               Int
  learning_record_id    Int
  rating                Int
  state                 String
  due                   DateTime
  stability             Float
  difficulty            Float
  elapsed_days          Int
  last_elapsed_days     Int
  scheduled_days        Int
  learning_steps        Int
  review_time           DateTime
  created_at            DateTime              @default(now())
  user_learning_records user_learning_records @relation(fields: [learning_record_id], references: [id])
  User                  User                  @relation(fields: [user_id], references: [id])
}
