import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { dbManager } from '@/lib/server/database';

export async function GET() {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const settings = await dbManager.getUserSettings(session.user.id.toString());
    
    return NextResponse.json(settings);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
