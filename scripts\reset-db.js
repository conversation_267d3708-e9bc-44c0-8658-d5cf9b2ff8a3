/**
 * 数据库重置和种子数据填充脚本
 * 此脚本执行以下步骤：
 * 1. 重置数据库（删除所有数据）但跳过自动种子填充
 * 2. 根据 Schema 创建所有表
 * 3. 手动执行种子脚本填充初始数据
 */

const { execSync } = require('child_process');

console.log('开始数据库重置流程...');

try {
  // 步骤 1: 重置数据库但跳过种子填充
  console.log('\n步骤 1: 重置数据库（删除所有数据）...');
  execSync('npx prisma migrate reset --skip-seed --force', { stdio: 'inherit' });
  
  // 步骤 2: 根据 Schema 创建所有表
  console.log('\n步骤 2: 根据 Schema 创建所有表...');
  execSync('npx prisma db push', { stdio: 'inherit' });
  
  // 步骤 3: 手动执行种子脚本
  console.log('\n步骤 3: 执行种子脚本填充初始数据...');
  execSync('npx prisma db seed', { stdio: 'inherit' });
  
  console.log('\n数据库重置和初始化完成！');
} catch (error) {
  console.error('数据库重置过程中发生错误:', error.message);
  process.exit(1);
}