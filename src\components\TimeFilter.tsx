'use client';
import React from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { Calendar, Clock, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

interface TimeFilterProps {
  className?: string;
}

const TimeFilter: React.FC<TimeFilterProps> = ({ className = '' }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const currentTimeRange = searchParams.get('timeRange') || '';
  const currentSortBy = searchParams.get('sortBy') || '';
  const currentSortOrder = searchParams.get('sortOrder') || 'desc';

  const timeRangeOptions = [
    { value: '', label: '全部时间' },
    { value: 'today', label: '今天' },
    { value: '2days', label: '近两天' },
    { value: '3days', label: '近三天' },
    { value: '1week', label: '一周内' },
    { value: '1month', label: '一个月内' },
  ];

  const handleTimeRangeChange = (timeRange: string) => {
    const params = new URLSearchParams(searchParams);

    if (timeRange) {
      params.set('timeRange', timeRange);
    } else {
      params.delete('timeRange');
    }

    // Reset to first page when changing time range
    params.set('page', '1');

    router.push(`${pathname}?${params.toString()}`);
  };

  const handleSortChange = () => {
    const params = new URLSearchParams(searchParams);

    if (currentSortBy === 'publish_time') {
      // 如果当前是按发布时间排序，切换排序顺序
      const newOrder = currentSortOrder === 'desc' ? 'asc' : 'desc';
      params.set('sortOrder', newOrder);
    } else {
      // 如果当前不是按发布时间排序，设置为按发布时间降序排序
      params.set('sortBy', 'publish_time');
      params.set('sortOrder', 'desc');
    }

    // Reset to first page when changing sort
    params.set('page', '1');

    router.push(`${pathname}?${params.toString()}`);
  };

  const getSortIcon = () => {
    if (currentSortBy !== 'publish_time') {
      return <ArrowUpDown className="h-3 w-3" />;
    }
    return currentSortOrder === 'desc' ? <ArrowDown className="h-3 w-3" /> : <ArrowUp className="h-3 w-3" />;
  };

  const getSortLabel = () => {
    if (currentSortBy !== 'publish_time') {
      return '发布时间';
    }
    return currentSortOrder === 'desc' ? '最新优先' : '最早优先';
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="flex items-center space-x-1.5">
        <Calendar className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700 whitespace-nowrap">时间筛选</span>
      </div>

      <div className="flex flex-wrap gap-1.5">
        {timeRangeOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => handleTimeRangeChange(option.value)}
            className={`px-2.5 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
              currentTimeRange === option.value
                ? 'bg-indigo-600 text-white border-indigo-600'
                : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>

      {/* 排序按钮 */}
      <div className="flex items-center space-x-1.5">
        <Clock className="h-4 w-4 text-gray-500" />
        <button
          onClick={handleSortChange}
          className={`flex items-center space-x-1 px-2.5 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
            currentSortBy === 'publish_time'
              ? 'bg-indigo-600 text-white border-indigo-600'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          {getSortIcon()}
          <span>{getSortLabel()}</span>
        </button>
      </div>
    </div>
  );
};

export default TimeFilter;
