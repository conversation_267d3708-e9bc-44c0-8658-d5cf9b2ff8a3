import { getUserSettingsAction } from '@/app/actions';

export interface UserSettingsData {
  news_page_size: number;
  vocabulary_page_size: number;
  grammar_page_size: number;
  study_cards_per_session: number; // 保留向后兼容
  study_vocabulary_per_session: number; // 新增：每次学习词汇数量
  study_grammar_per_session: number; // 新增：每次学习语法数量
  auto_play_audio: boolean;
  show_furigana: boolean;
  theme_preference: 'light' | 'dark' | 'auto';
}

export const DEFAULT_USER_SETTINGS: UserSettingsData = {
  news_page_size: 10,
  vocabulary_page_size: 20,
  grammar_page_size: 15,
  study_cards_per_session: 10, // 保留向后兼容
  study_vocabulary_per_session: 8, // 默认每次学习8个词汇
  study_grammar_per_session: 5, // 默认每次学习5个语法
  auto_play_audio: true,
  show_furigana: true,
  theme_preference: 'light'
};

/**
 * 获取用户设置，如果没有设置则返回默认值
 */
export async function getUserSettings(userId: string): Promise<UserSettingsData> {
  try {
    const result = await getUserSettingsAction(userId);
    if (!result.success) {
      return DEFAULT_USER_SETTINGS;
    }

    const rawSettings = result.data;

    if (!rawSettings) {
      return DEFAULT_USER_SETTINGS;
    }

    // 转换字符串值为适当的类型
    return {
      news_page_size: parseInt(rawSettings.news_page_size || '10'),
      vocabulary_page_size: parseInt(rawSettings.vocabulary_page_size || '20'),
      grammar_page_size: parseInt(rawSettings.grammar_page_size || '15'),
      study_cards_per_session: parseInt(rawSettings.study_cards_per_session || '10'),
      auto_play_audio: rawSettings.auto_play_audio === 'true',
      show_furigana: rawSettings.show_furigana === 'true',
      theme_preference: (rawSettings.theme_preference as 'light' | 'dark' | 'auto') || 'light'
    };
  } catch (error) {
    console.error('Failed to get user settings:', error);
    return DEFAULT_USER_SETTINGS;
  }
}

/**
 * 获取特定的用户设置值
 */
export async function getUserSetting<K extends keyof UserSettingsData>(
  userId: string, 
  key: K
): Promise<UserSettingsData[K]> {
  const settings = await getUserSettings(userId);
  return settings[key];
}

/**
 * 获取分页大小设置
 */
export async function getPageSize(userId: string, type: 'news' | 'vocabulary' | 'grammar'): Promise<number> {
  const settings = await getUserSettings(userId);
  switch (type) {
    case 'news':
      return settings.news_page_size;
    case 'vocabulary':
      return settings.vocabulary_page_size;
    case 'grammar':
      return settings.grammar_page_size;
    default:
      return 10;
  }
}

/**
 * 客户端使用的设置 Hook（需要在组件中使用）
 */
export function useUserSettings(userId: string) {
  // 这个可以在后续实现为一个 React Hook
  // 现在先提供基础的获取功能
  return {
    getSettings: () => getUserSettings(userId),
    getSetting: <K extends keyof UserSettingsData>(key: K) => getUserSetting(userId, key),
    getPageSize: (type: 'news' | 'vocabulary' | 'grammar') => getPageSize(userId, type)
  };
}
