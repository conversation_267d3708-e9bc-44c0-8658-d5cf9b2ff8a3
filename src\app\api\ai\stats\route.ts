import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

export async function GET() {
  try {
    const aiStats = await dbManager.getAIStats();
    const dbStats = await dbManager.getDatabaseStatsWithMedia();
    
    return NextResponse.json({
      success: true,
      data: {
        aiStats,
        aiProcessingQueue: dbStats.aiProcessingQueue
      }
    });
  } catch (error: any) {
    console.error('获取AI统计失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
