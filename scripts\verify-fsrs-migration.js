const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyFsrsMigration() {
  try {
    console.log('=== 步骤 3: 验证 FSRS 迁移结果 ===\n');

    // 1. 检查表结构
    console.log('1. 检查表结构...');
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(user_learning_records)`;
    const fields = tableInfo.map(field => field.name);
    
    // 检查 FSRS 字段
    const fsrsFields = ['fsrs_due', 'fsrs_stability', 'fsrs_difficulty', 'fsrs_reps', 'fsrs_state', 'fsrs_last_review'];
    const missingFsrsFields = fsrsFields.filter(field => !fields.includes(field));
    
    // 检查旧字段
    const oldFields = ['last_reviewed_at', 'next_review_at', 'ease_factor', 'interval', 'repetitions'];
    const missingOldFields = oldFields.filter(field => !fields.includes(field));
    
    console.log(`总字段数: ${fields.length}`);
    console.log(`FSRS 字段完整性: ${missingFsrsFields.length === 0 ? '✅ 完整' : '❌ 缺失: ' + missingFsrsFields.join(', ')}`);
    console.log(`旧字段保留性: ${missingOldFields.length === 0 ? '✅ 完整保留' : '❌ 缺失: ' + missingOldFields.join(', ')}`);

    // 2. 检查数据完整性
    console.log('\n2. 检查数据完整性...');
    
    const totalRecords = await prisma.user_learning_records.count();
    const recordsWithFsrsData = await prisma.user_learning_records.count({
      where: { fsrs_due: { not: null } }
    });
    const recordsWithoutFsrsData = await prisma.user_learning_records.count({
      where: { fsrs_due: null }
    });

    console.log(`总记录数: ${totalRecords}`);
    console.log(`有 FSRS 数据: ${recordsWithFsrsData}`);
    console.log(`无 FSRS 数据: ${recordsWithoutFsrsData}`);
    console.log(`转换完成率: ${((recordsWithFsrsData / totalRecords) * 100).toFixed(2)}%`);

    // 3. 检查状态分布
    console.log('\n3. 检查状态分布...');
    const statusStats = await prisma.user_learning_records.groupBy({
      by: ['status'],
      _count: { id: true }
    });
    
    statusStats.forEach(stat => {
      console.log(`  - ${stat.status}: ${stat._count.id} 条`);
    });

    // 4. 检查 FSRS 状态分布
    console.log('\n4. 检查 FSRS 状态分布...');
    const fsrsStateStats = await prisma.user_learning_records.groupBy({
      by: ['fsrs_state'],
      _count: { id: true },
      where: { fsrs_state: { not: null } }
    });
    
    fsrsStateStats.forEach(stat => {
      console.log(`  - ${stat.fsrs_state}: ${stat._count.id} 条`);
    });

    // 5. 抽样检查数据质量
    console.log('\n5. 抽样检查数据质量...');
    
    // 检查 new 状态记录
    const sampleNewRecord = await prisma.user_learning_records.findFirst({
      where: { status: 'new', fsrs_due: { not: null } }
    });
    
    if (sampleNewRecord) {
      console.log('✅ new 状态记录 FSRS 数据示例:');
      console.log(`  - fsrs_state: ${sampleNewRecord.fsrs_state}`);
      console.log(`  - fsrs_reps: ${sampleNewRecord.fsrs_reps}`);
      console.log(`  - fsrs_difficulty: ${sampleNewRecord.fsrs_difficulty}`);
    }

    // 检查 learning 状态记录
    const sampleLearningRecord = await prisma.user_learning_records.findFirst({
      where: { status: 'learning', fsrs_due: { not: null } }
    });
    
    if (sampleLearningRecord) {
      console.log('✅ learning 状态记录 FSRS 数据示例:');
      console.log(`  - fsrs_state: ${sampleLearningRecord.fsrs_state}`);
      console.log(`  - fsrs_reps: ${sampleLearningRecord.fsrs_reps}`);
      console.log(`  - fsrs_difficulty: ${sampleLearningRecord.fsrs_difficulty}`);
      console.log(`  - 旧 repetitions: ${sampleLearningRecord.repetitions}`);
      console.log(`  - 旧 ease_factor: ${sampleLearningRecord.ease_factor}`);
    }

    // 6. 检查其他重要数据
    console.log('\n6. 检查其他重要数据...');
    const articleCount = await prisma.articles.count();
    const vocabularyCount = await prisma.vocabulary.count();
    const grammarCount = await prisma.grammar_points.count();
    
    console.log(`文章数量: ${articleCount} (应该是 859)`);
    console.log(`词汇数量: ${vocabularyCount} (应该是 2668)`);
    console.log(`语法点数量: ${grammarCount} (应该是 804)`);

    // 7. 生成验证报告
    console.log('\n=== 验证报告 ===');
    
    const allChecks = [
      { name: 'FSRS 字段完整', passed: missingFsrsFields.length === 0 },
      { name: '旧字段保留', passed: missingOldFields.length === 0 },
      { name: '数据转换完整', passed: recordsWithoutFsrsData === 0 },
      { name: '文章数据完整', passed: articleCount === 859 },
      { name: '词汇数据完整', passed: vocabularyCount === 2668 },
      { name: '语法数据完整', passed: grammarCount === 804 }
    ];

    const passedChecks = allChecks.filter(check => check.passed).length;
    const totalChecks = allChecks.length;

    console.log(`验证通过: ${passedChecks}/${totalChecks}`);
    allChecks.forEach(check => {
      console.log(`${check.passed ? '✅' : '❌'} ${check.name}`);
    });

    if (passedChecks === totalChecks) {
      console.log('\n🎉 所有验证通过！迁移成功！');
      console.log('✅ 可以安全进行下一步：删除旧字段');
    } else {
      console.log('\n⚠️  部分验证失败，请检查问题后再继续');
    }

    console.log('\n=== 步骤 3 完成：验证完成 ===');

  } catch (error) {
    console.error('验证迁移结果失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

verifyFsrsMigration();
