{"name": "nhk-japanese-learning", "private": true, "version": "2.0.0", "scripts": {"dev": "npx update-browserslist-db@latest && next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-new-db": "npx prisma migrate dev --name init", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev --name user-system", "db:user-migration": "npx ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/migrations/user-migration.ts", "db:reset": "node scripts/reset-db.js", "db:reset-complete": "node scripts/reset-database-complete.js", "postinstall": "npx update-browserslist-db@latest && npx prisma generate"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@genkit-ai/firebase": "^1.2.1", "@genkit-ai/googleai": "^1.2.1", "@google/genai": "^1.7.0", "@prisma/client": "^5.17.0", "@types/archiver": "^6.0.3", "@types/bcryptjs": "^2.4.6", "archiver": "^7.0.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "genkit": "^1.2.1", "hls-parser": "^0.13.6", "hls.js": "^1.6.6", "hlsdownloader": "^3.3.3", "https-proxy-agent": "^7.0.2", "jsdom": "^24.1.1", "lucide-react": "0.408.0", "m3u8-downloader": "^0.0.4", "m3u8-parser": "^7.1.0", "mux.js": "^6.3.0", "next": "15.3.3", "next-auth": "^4.24.11", "node-hls-downloader": "^2.2.0", "react": "^19.0.0-rc.0", "react-dom": "^19.0.0-rc.0", "ts-fsrs": "^5.2.1", "zod": "^3.23.8"}, "devDependencies": {"@types/jsdom": "^21.1.7", "@types/m3u8-parser": "^7.2.2", "@types/node": "^20.11.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/xmldom": "^0.1.34", "autoprefixer": "^10.4.18", "cheerio": "^1.0.0-rc.12", "eslint": "^8.57.0", "eslint-config-next": "15.3.3", "node-fetch": "^3.3.2", "postcss": "^8.4.35", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3", "xmldom": "^0.6.0", "xpath": "^0.0.32"}}