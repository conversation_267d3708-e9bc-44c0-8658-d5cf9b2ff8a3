'use client';

import React from 'react';
import { Volume2, Pause } from 'lucide-react';
import { renderRubyText } from '@/utils/ruby-utils';

interface GrammarCardProps {
  grammar: any;
  onGrammarSpeak?: (grammar: any) => void;
  onContentSpeak?: (text: string, contentId: string, language?: 'ja-JP' | 'en-US') => void;
  playingGrammarId?: string | null;
  playingContentId?: string | null;
  isPlaying?: boolean;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

const GrammarCard: React.FC<GrammarCardProps> = ({
  grammar,
  onGrammarSpeak,
  onContentSpeak,
  playingGrammarId,
  playingContentId,
  isPlaying = false,
  showActions = true,
  compact = false,
  className = ""
}) => {
  const grammarId = grammar.pattern;

  return (
    <div className={`${compact ? 'p-3' : 'p-6'} border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* 基本信息 */}
          <div className={`flex items-center space-x-2 ${compact ? 'mb-2' : 'mb-3'}`}>
            <div className={`font-medium text-indigo-600 ${compact ? 'text-lg' : 'text-xl'}`}>{grammar.pattern}</div>
            {grammar.reading && (
              <div className="text-sm text-gray-500">({grammar.reading})</div>
            )}
            {(grammar.level || grammar.jlpt_level) && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                {grammar.level || grammar.jlpt_level}
              </span>
            )}
            {grammar.difficulty && (
              <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
                难度: {grammar.difficulty}
              </span>
            )}
          </div>

          {/* 详细内容 */}
          <div className="space-y-3">
            {/* 中文解释 */}
            {grammar.explanation && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">中文解释</h5>
                <div className="text-gray-700 bg-gray-50 p-2 rounded-md border border-gray-100 whitespace-pre-wrap">
                  {grammar.explanation}
                </div>
              </div>
            )}

            {/* 日语解释 */}
            {grammar.explanationJa && (
              <div className="text-sm">
                <div className="flex items-center gap-2 mb-1">
                  <h5 className="text-xs font-semibold text-gray-700">日语解释</h5>
                  {onContentSpeak && (
                    <button
                      onClick={() => onContentSpeak(grammar.explanationJa, `grammar-ja-${grammar.id}`, 'ja-JP')}
                      className={`p-1 rounded transition-all ${
                        playingContentId === `grammar-ja-${grammar.id}` && isPlaying
                          ? 'text-blue-600 bg-blue-50'
                          : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
                      }`}
                      title="发音日语解释"
                    >
                      <Volume2 className="h-3 w-3" />
                    </button>
                  )}
                </div>
                <div className="text-gray-700 bg-blue-50 p-2 rounded-md border border-blue-100">
                  {renderRubyText(grammar.explanationJa)}
                </div>
              </div>
            )}

            {/* 常用搭配 */}
            {grammar.commonCollocations && grammar.commonCollocations.length > 0 && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">常用搭配</h5>
                <div className="flex flex-wrap gap-1">
                  {grammar.commonCollocations.map((collocation: any, index: number) => (
                    <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                      {typeof collocation === 'string' ? collocation : collocation.collocation || collocation.pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* 相似语法 */}
            {grammar.similarPatterns && grammar.similarPatterns.length > 0 && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">相似语法</h5>
                <div className="space-y-2">
                  {grammar.similarPatterns.map((pattern: any, index: number) => (
                    <div key={index} className="p-2 bg-orange-50 rounded-md border border-orange-100">
                      <div className="font-medium text-orange-800">{pattern.pattern}</div>
                      <div className="text-orange-600 text-xs">{pattern.meaning}</div>
                      {pattern.difference && (
                        <div className="text-gray-600 text-xs mt-1">区别: {pattern.difference}</div>
                      )}
                      {pattern.examples && pattern.examples.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {pattern.examples.map((example: any, exIndex: number) => {
                            // 处理不同的例句格式
                            let japaneseText = '';
                            let chineseText = '';

                            if (typeof example === 'string') {
                              // 格式: "日语例句 - 中文翻译"
                              const parts = example.split(' - ');
                              japaneseText = parts[0] || example;
                              chineseText = parts[1] || '';
                            } else if (typeof example === 'object') {
                              // 对象格式: {japanese: "...", chinese: "..."}
                              japaneseText = example.japanese || '';
                              chineseText = example.chinese || '';
                            }

                            return (
                              <div key={exIndex} className="text-xs text-gray-600 bg-white p-1 rounded border">
                                <div>{renderRubyText(japaneseText)}</div>
                                {chineseText && (
                                  <div className="text-gray-500">{chineseText}</div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 例句 */}
            {grammar.examples && grammar.examples.length > 0 && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">例句</h5>
                <div className="space-y-1">
                  {grammar.examples.map((example: string, index: number) => (
                    <div key={index} className="text-xs text-gray-600 bg-gray-50 p-2 rounded-md border border-gray-100">
                      {renderRubyText(example)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* 操作按钮 */}
        {showActions && (
          <div className="flex flex-col items-center space-y-2 ml-4">
            <div className="flex items-center space-x-2">
              {onGrammarSpeak && (
                <button
                  onClick={() => onGrammarSpeak(grammar)}
                  className={`p-2 rounded-lg transition-all ${
                    playingGrammarId === grammarId && isPlaying
                      ? 'text-indigo-600 bg-indigo-50'
                      : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                  }`}
                  title="发音"
                >
                  {playingGrammarId === grammarId && isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Volume2 className="h-4 w-4" />
                  )}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GrammarCard;
