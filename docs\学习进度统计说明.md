# 学习进度统计说明

## 概述

学习统计功能提供了用户在日语学习过程中的详细进度跟踪，包括词汇学习进度、语法学习进度和文章阅读进度。

## 词汇学习进度统计

### 统计逻辑

词汇学习进度通过以下方式计算：

1. **总词汇数**：系统中所有词汇的总数量（来自 `vocabulary` 表）
2. **已掌握词汇**：用户学习记录中状态为 `mastered` 的词汇数量
3. **已学习词汇**：用户学习记录中状态不为 `new` 的词汇数量（包括 `learning` 和 `mastered`）
4. **掌握百分比**：已掌握词汇数 / 总词汇数 × 100%

### "N/A 词汇" 的含义

在之前的按等级分组统计中，"N/A 词汇" 指的是：

- **定义**：`jlpt_level` 字段为 `null` 或空值的词汇
- **来源**：
  - AI 分析提取但未明确分级的词汇
  - 手动添加但未设置 JLPT 等级的词汇
  - 系统导入时缺少等级信息的词汇

### 当前实现

目前系统已简化为整体统计，不再按 JLPT 等级分组，因此不会再显示 "N/A 词汇"。

## 语法学习进度统计

### 统计逻辑

语法学习进度的计算方式与词汇类似：

1. **总语法数**：系统中所有语法点的总数量（来自 `grammar_points` 表）
2. **已掌握语法**：用户学习记录中状态为 `mastered` 的语法数量
3. **已学习语法**：用户学习记录中状态不为 `new` 的语法数量
4. **掌握百分比**：已掌握语法数 / 总语法数 × 100%

### 数据来源

- **语法点表**：`grammar_points` - 存储所有语法点信息
- **学习记录表**：`user_learning_records` - 记录用户的学习状态
- **记录类型**：`record_type = 'grammar'` 的记录

## 学习状态定义

系统中的学习状态包括：

- **`new`**：新项目，尚未开始学习
- **`learning`**：学习中，已开始但未完全掌握
- **`mastered`**：已掌握，完成学习目标

## 学习状态更新机制

### 什么时候状态会被更新为 `mastered`？

目前系统中，学习记录状态更新为 `mastered` 的方式有以下几种：

#### 1. 用户手动设置（主要方式）

**生词本页面 (`/vocabulary`)**：
- 用户可以在生词本中查看所有词汇
- 通过状态筛选器可以管理不同状态的词汇
- 用户可以手动将词汇状态设置为"已掌握"

**语法本页面 (`/grammar`)**：
- 用户可以在语法本中查看所有语法点
- 通过状态筛选器可以管理不同状态的语法
- 用户可以手动将语法状态设置为"已掌握"

**文章阅读页面**：
- 在文章的重点词汇和语法解析区域
- 用户可以为每个词汇或语法点设置学习状态
- 包括"陌生"、"学习中"、"已掌握"、"困难"等选项

#### 2. SRS复习系统（当前未自动设置为mastered）

**当前实现**：
- SRS复习系统中，无论用户回答"轻松"、"良好"、"困难"还是"重来"
- 状态都只会设置为 `'learning'`，不会自动升级为 `'mastered'`
- 这意味着用户需要手动将熟练掌握的内容标记为"已掌握"

**设计考虑**：
- 避免系统过早地将内容标记为"已掌握"
- 让用户自主决定何时认为已经完全掌握某个知识点
- 保持学习记录的准确性和用户的主观判断

#### 3. 状态更新的技术实现

**数据库层面**：
```typescript
// updateLearningProgress 函数
async updateLearningProgress(data: any, userId: string) {
    // 使用 UPSERT 逻辑更新或插入学习记录
    return prisma.user_learning_records.upsert({
        where: uniqueIdentifier,
        update: { status: data.status, updated_at: new Date() },
        create: { /* 创建新记录 */ }
    });
}
```

**前端调用**：
```typescript
// 通过 updateLearningProgressAction 调用
await updateLearningProgressAction({
    record_type: 'vocabulary', // 或 'grammar'
    status: 'mastered',
    vocabulary_id: vocabularyId
}, userId);
```

## 数据库实现

### 核心查询逻辑

```typescript
// 词汇学习进度
const [totalVocabulary, masteredVocabulary, learningVocabulary] = await prisma.$transaction([
    prisma.vocabulary.count(),
    prisma.user_learning_records.count({ 
        where: { user_id: userIdInt, record_type: 'vocabulary', status: 'mastered' } 
    }),
    prisma.user_learning_records.count({ 
        where: { user_id: userIdInt, record_type: 'vocabulary', status: { not: 'new' } } 
    })
]);

// 语法学习进度
const [totalGrammar, masteredGrammar, learningGrammar] = await prisma.$transaction([
    prisma.grammar_points.count(),
    prisma.user_learning_records.count({ 
        where: { user_id: userIdInt, record_type: 'grammar', status: 'mastered' } 
    }),
    prisma.user_learning_records.count({ 
        where: { user_id: userIdInt, record_type: 'grammar', status: { not: 'new' } } 
    })
]);
```

### 相关数据表

1. **vocabulary** - 词汇表
2. **grammar_points** - 语法点表
3. **user_learning_records** - 用户学习记录表

## 界面显示

### 进度条说明

- **深色进度条**：表示已掌握的内容
  - 词汇：靛蓝色 (`bg-indigo-500`)
  - 语法：翠绿色 (`bg-emerald-500`)
- **浅色进度条**：表示已学习但未掌握的内容
  - 词汇：浅蓝色 (`bg-blue-400`)
  - 语法：浅绿色 (`bg-green-400`)

### 数据更新

学习进度数据会在以下情况下更新：

1. 用户完成学习会话
2. 用户复习词汇或语法
3. 系统导入新的词汇或语法点
4. 用户手动标记学习状态

## 性能优化

- 使用数据库事务确保数据一致性
- 缓存统计结果减少重复查询
- 异步加载避免阻塞界面渲染

## 注意事项

### 当前系统的限制

1. **SRS系统不会自动设置为mastered**：
   - 即使用户在复习中多次回答"轻松"，系统也不会自动将状态升级为"已掌握"
   - 用户需要手动在生词本或语法本中将熟练掌握的内容标记为"已掌握"

2. **状态更新需要用户主动操作**：
   - 系统依赖用户的主观判断来确定是否已掌握某个知识点
   - 这确保了学习记录的准确性，但需要用户养成定期整理的习惯

3. **统计数据的准确性**：
   - "已掌握"的统计完全基于用户的手动标记
   - 如果用户不主动标记，可能会低估实际的学习成果

### 建议的使用方式

1. **定期整理学习状态**：
   - 建议用户定期访问生词本和语法本
   - 将确实已经熟练掌握的内容标记为"已掌握"

2. **结合SRS复习使用**：
   - 通过SRS复习系统巩固记忆
   - 当某个词汇或语法在多次复习中都能轻松回答时，考虑标记为"已掌握"

3. **关注学习进度统计**：
   - 定期查看学习统计页面了解整体进度
   - 根据统计数据调整学习计划和重点

## 未来扩展

可能的功能扩展包括：

1. **智能状态升级**：
   - 基于SRS复习表现自动建议升级为"已掌握"
   - 设置阈值条件（如连续N次"轻松"回答）

2. **按时间段的学习进度趋势**：
   - 显示每周/每月的掌握进度变化
   - 学习效率和记忆曲线分析

3. **个性化学习建议**：
   - 基于学习数据提供针对性建议
   - 推荐复习重点和学习计划

4. **批量状态管理**：
   - 支持批量标记多个项目的状态
   - 智能筛选和批量操作功能
