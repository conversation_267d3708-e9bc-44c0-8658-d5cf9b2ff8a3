import { NextRequest, NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';
import fs from 'fs';

// 下载原始数据库文件
export async function GET(request: NextRequest) {
  try {
    const dbPath = dbManager.getDatabaseFilePath();
    
    if (!dbPath) {
      return NextResponse.json({
        success: false,
        error: '数据库文件不存在'
      }, { status: 404 });
    }
    
    const fileBuffer = fs.readFileSync(dbPath);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
    const filename = `nhk_news_new_${timestamp}.db`;
    
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (error: any) {
    console.error('下载数据库文件失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
