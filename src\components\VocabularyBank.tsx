
'use client';
import React, { useState, useEffect, useTransition } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  BookOpen,
  Search,
  Star,
  Trash2,
  Volume2,
  Plus,
  Calendar,
  Loader,
  RefreshCw,
  Save,
  X
} from 'lucide-react';
import { addVocabularyAction } from '@/app/actions';
import VocabularyCard from './VocabularyCard';
import { useTTS } from '@/hooks/useTTS';

interface VocabularyBankProps {
  initialVocabulary: any | null; // 现在包含分页信息的对象
  initialStats: any | null;
}

const VocabularyBank: React.FC<VocabularyBankProps> = ({ initialVocabulary, initialStats }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedLevel, setSelectedLevel] = useState(searchParams.get('level') || 'all');

  const [vocabularyData, setVocabularyData] = useState(initialVocabulary);
  const [stats, setStats] = useState(initialStats);
  const [isPending, startTransition] = useTransition();

  // 从分页数据中提取词汇列表
  const vocabulary = vocabularyData?.vocabularies || [];
  const totalCount = vocabularyData?.totalCount || 0;
  const currentPage = vocabularyData?.currentPage || 1;
  const totalPages = vocabularyData?.totalPages || 1;

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newWord, setNewWord] = useState({ word: '', reading: '', meaning_zh: '', jlpt_level: 'N5' });

  const { isPlaying, speak, cancel } = useTTS();
  const [playingVocabId, setPlayingVocabId] = useState<string | null>(null);
  const [playingContentId, setPlayingContentId] = useState<string | null>(null);


  useEffect(() => {
    // Data is now primarily driven by props from the server component
    setVocabularyData(initialVocabulary);
    setStats(initialStats);
  }, [initialVocabulary, initialStats]);

  const handleFilterChange = () => {
    const params = new URLSearchParams(window.location.search);
    if (searchTerm) params.set('search', searchTerm);
    else params.delete('search');
    if (selectedLevel !== 'all') params.set('level', selectedLevel);
    else params.delete('level');
    router.push(`/vocabulary?${params.toString()}`);
  };

  useEffect(() => {
    const handler = setTimeout(() => {
        handleFilterChange();
    }, 500); // Debounce search input
    return () => clearTimeout(handler);
  }, [searchTerm, selectedLevel]);




  const handleAddWord = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newWord.word || !newWord.reading || !newWord.meaning_zh) return;

    startTransition(async () => {
      const result = await addVocabularyAction(newWord);
      if (result.success) {
        setIsAddModalOpen(false);
        setNewWord({ word: '', reading: '', meaning_zh: '', jlpt_level: 'N5' });
        router.refresh();
      } else {
        alert(`添加失败: ${result.error}`);
      }
    });
  };



  const handleVocabSpeak = (vocab: any) => {
    const vocabId = `${vocab.word}-${vocab.reading}`;

    if (playingVocabId === vocabId && isPlaying) {
      // 如果当前正在播放这个词汇，则停止
      cancel();
      setPlayingVocabId(null);
    } else {
      // 播放词汇发音
      const textToSpeak = vocab.reading || vocab.word;
      setPlayingVocabId(vocabId);
      speak(textToSpeak, 'ja-JP');

      // 设置播放结束后的清理
      setTimeout(() => {
        setPlayingVocabId(null);
      }, 3000); // 3秒后自动清理状态
    }
  };

  // 处理语义网络中单词的发音
  const handleContentSpeak = (text: string, contentId: string, language: 'ja-JP' | 'en-US' = 'ja-JP') => {
    if (playingContentId === contentId && isPlaying) {
      // 如果当前正在播放这个内容，则停止
      cancel();
      setPlayingContentId(null);
    } else {
      // 播放发音
      setPlayingContentId(contentId);
      speak(text, language);

      // 设置播放结束后的清理
      setTimeout(() => {
        setPlayingContentId(null);
      }, 3000); // 3秒后自动清理状态
    }
  };



  if (!vocabulary || !stats) {
    // This can be shown if the page is still client-side rendering or data failed to load
    return (
        <div className="flex h-full items-center justify-center py-12">
            <div className="text-center">
                <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
                <p className="text-gray-600">正在准备词汇库...</p>
            </div>
        </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">我的生词本</h2>
            <p className="text-gray-600 mt-1">管理你收藏的词汇，持续复习强化记忆</p>
          </div>
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => router.refresh()}
              disabled={isPending}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all"
            >
              {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              <span>刷新</span>
            </button>
            <button 
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>添加词汇</span>
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-gray-900">{stats.total || 0}</div>
            <div className="text-sm text-gray-600">总词汇</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-blue-600">{stats.learning || 0}</div>
            <div className="text-sm text-gray-600">学习中</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-orange-600">{stats.pending || 0}</div>
            <div className="text-sm text-gray-600">待学习</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="搜索词汇..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <select 
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
            >
              <option value="all">全部级别</option>
              <option value="N5">N5</option>
              <option value="N4">N4</option>
              <option value="N3">N3</option>
              <option value="N2">N2</option>
              <option value="N1">N1</option>
            </select>
            

          </div>
        </div>

        {/* Vocabulary List */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="divide-y divide-gray-200">
            {vocabulary.map((vocab: any) => (
              <div key={vocab.id} className="relative">
                <VocabularyCard
                  vocab={vocab}
                  onVocabSpeak={handleVocabSpeak}
                  onContentSpeak={handleContentSpeak}
                  playingVocabId={playingVocabId}
                  playingContentId={playingContentId}
                  isPlaying={isPlaying}
                  showActions={false}
                  compact={false}
                  className=""
                />

                {/* 额外的单词本特有信息 */}
                <div className="px-6 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>添加于 {vocab.addedDate}</span>
                      </span>
                      {vocab.review_count > 0 && (
                        <span>复习 {vocab.review_count} 次</span>
                      )}
                      {vocab.article_count > 0 && (
                        <span>出现在 {vocab.article_count} 篇文章中</span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleVocabSpeak(vocab)}
                        className={`p-2 rounded-lg transition-all ${
                          playingVocabId === `${vocab.word}-${vocab.reading}` && isPlaying
                            ? 'text-indigo-600 bg-indigo-50'
                            : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                        }`}
                        title="发音"
                      >
                        {playingVocabId === `${vocab.word}-${vocab.reading}` && isPlaying ? (
                          <Volume2 className="h-4 w-4 animate-pulse" />
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                      </button>
                      <button className="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-all">
                        <Star className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {vocabulary.length === 0 && !isPending && (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到词汇</h3>
            <p className="text-gray-600">
              尝试调整搜索条件或添加新的词汇
            </p>
          </div>
        )}

        {/* 分页导航 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between bg-white px-6 py-4 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-700">
              显示第 {((currentPage - 1) * Math.ceil(totalCount / totalPages)) + 1} - {Math.min(currentPage * Math.ceil(totalCount / totalPages), totalCount)} 项，共 {totalCount} 项
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', (currentPage - 1).toString());
                  router.push(`/vocabulary?${params.toString()}`);
                }}
                disabled={currentPage <= 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => {
                        const params = new URLSearchParams(window.location.search);
                        params.set('page', pageNum.toString());
                        router.push(`/vocabulary?${params.toString()}`);
                      }}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        pageNum === currentPage
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', (currentPage + 1).toString());
                  router.push(`/vocabulary?${params.toString()}`);
                }}
                disabled={currentPage >= totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Add Word Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <form onSubmit={handleAddWord} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">添加新词汇</h3>
                <button type="button" onClick={() => setIsAddModalOpen(false)} className="p-2 text-gray-400 hover:text-gray-600"><X className="h-5 w-5" /></button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">单词 (日语)</label>
                  <input type="text" value={newWord.word} onChange={e => setNewWord({...newWord, word: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">读音 (平假名)</label>
                  <input type="text" value={newWord.reading} onChange={e => setNewWord({...newWord, reading: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">中文释义</label>
                  <input type="text" value={newWord.meaning_zh} onChange={e => setNewWord({...newWord, meaning_zh: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">JLPT 等级</label>
                  <select value={newWord.jlpt_level} onChange={e => setNewWord({...newWord, jlpt_level: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    <option value="N5">N5</option>
                    <option value="N4">N4</option>
                    <option value="N3">N3</option>
                    <option value="N2">N2</option>
                    <option value="N1">N1</option>
                    <option value="N/A">未分级</option>
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" onClick={() => setIsAddModalOpen(false)} className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                <button type="submit" disabled={isPending} className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400">
                  {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}<span>保存</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default VocabularyBank;
