'use client';

import { useSession, signIn } from 'next-auth/react';
import { useEffect } from 'react';

export default function DevAutoLogin({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();

  useEffect(() => {
    // 只在开发模式下且启用自动登录时执行
    if (
      process.env.NODE_ENV === 'development' && 
      process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' &&
      status === 'unauthenticated'
    ) {
      // 自动登录
      signIn('credentials', {
        email: '<EMAIL>',
        password: 'dev',
        redirect: false
      });
    }
  }, [status]);

  // 在开发模式下，如果启用了自动登录但还没有session，显示加载状态
  if (
    process.env.NODE_ENV === 'development' && 
    process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' &&
    status === 'loading'
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">开发模式自动登录中...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
