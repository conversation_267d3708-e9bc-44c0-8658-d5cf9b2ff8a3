-- CreateTable
CREATE TABLE "rss_sources" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "language" TEXT DEFAULT 'ja',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "max_articles" INTEGER DEFAULT 10,
    "enable_ai_processing" BOOLEAN NOT NULL DEFAULT false,
    "content_selector" TEXT,
    "last_fetch_time" DATETIME,
    "last_fetch_count" INTEGER DEFAULT 0,
    "total_fetched" INTEGER DEFAULT 0,
    "success_rate" REAL DEFAULT 0.0,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "rss_field_mappings" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "rss_source_id" INTEGER NOT NULL,
    "field_name" TEXT NOT NULL,
    "xpath_selector" TEXT,
    "css_selector" TEXT,
    "attribute_name" TEXT,
    "default_value" TEXT,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "transform_rule" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "rss_field_mappings_rss_source_id_fkey" FOREIGN KEY ("rss_source_id") REFERENCES "rss_sources" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "articles" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "rss_source_id" INTEGER,
    "guid" TEXT,
    "title" TEXT NOT NULL,
    "subtitle" TEXT,
    "content" TEXT,
    "content_html" TEXT,
    "url" TEXT NOT NULL,
    "publish_time" DATETIME,
    "fetch_time" DATETIME DEFAULT CURRENT_TIMESTAMP,
    "featured_image_url" TEXT,
    "featured_image_path" TEXT,
    "video_url" TEXT,
    "video_path" TEXT,
    "video_metadata_json" TEXT,
    "video_m3u8_content" TEXT,
    "video_download_status" TEXT DEFAULT 'disabled',
    "use_local_video" BOOLEAN DEFAULT false,
    "audio_url" TEXT,
    "audio_path" TEXT,
    "ai_processing_status" TEXT NOT NULL DEFAULT 'pending',
    "ai_processed_at" DATETIME,
    "ai_processing_error" TEXT,
    "processing_status" TEXT NOT NULL DEFAULT 'pending',
    "title_furigana_html" TEXT,
    "content_furigana_html" TEXT,
    "subtitle_furigana_html" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "articles_rss_source_id_fkey" FOREIGN KEY ("rss_source_id") REFERENCES "rss_sources" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "article_translations" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "article_id" INTEGER NOT NULL,
    "language_code" TEXT NOT NULL,
    "translated_title" TEXT,
    "translated_subtitle" TEXT,
    "translated_content" TEXT,
    "translation_method" TEXT DEFAULT 'ai',
    "quality_score" REAL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "article_translations_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "vocabulary" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "word" TEXT NOT NULL,
    "reading" TEXT,
    "meaning_zh" TEXT,
    "meaning_en" TEXT,
    "part_of_speech" TEXT,
    "jlpt_level" TEXT,
    "frequency_rank" INTEGER,
    "difficulty_score" REAL,
    "extraction_method" TEXT DEFAULT 'ai',
    "related_words_json" TEXT,
    "verb_info_json" TEXT,
    "common_collocations_json" TEXT,
    "explanation_ja" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "article_vocabulary" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "article_id" INTEGER NOT NULL,
    "vocabulary_id" INTEGER NOT NULL,
    "position_start" INTEGER,
    "position_end" INTEGER,
    "context" TEXT,
    "is_key_vocabulary" BOOLEAN DEFAULT false,
    "extraction_confidence" REAL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "article_vocabulary_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "article_vocabulary_vocabulary_id_fkey" FOREIGN KEY ("vocabulary_id") REFERENCES "vocabulary" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "grammar_points" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "pattern" TEXT NOT NULL,
    "name" TEXT,
    "reading" TEXT,
    "meaning_zh" TEXT,
    "meaning_en" TEXT,
    "explanation" TEXT,
    "explanation_ja" TEXT,
    "common_collocations_json" TEXT,
    "jlpt_level" TEXT,
    "difficulty_score" REAL,
    "usage_frequency" INTEGER DEFAULT 0,
    "extraction_method" TEXT DEFAULT 'ai',
    "similar_grammar_json" TEXT,
    "examples_json" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "article_grammar_points" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "article_id" INTEGER NOT NULL,
    "grammar_point_id" INTEGER NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "article_grammar_points_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "article_grammar_points_grammar_point_id_fkey" FOREIGN KEY ("grammar_point_id") REFERENCES "grammar_points" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'user',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "ai_prompts" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "content" TEXT NOT NULL,
    "variables" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "version" TEXT NOT NULL DEFAULT '1.0',
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "user_learning_records" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "article_id" INTEGER,
    "vocabulary_id" INTEGER,
    "grammar_point_id" INTEGER,
    "record_type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'new',
    "fsrs_due" DATETIME,
    "fsrs_stability" REAL,
    "fsrs_difficulty" REAL,
    "fsrs_elapsed_days" INTEGER,
    "fsrs_scheduled_days" INTEGER,
    "fsrs_learning_steps" INTEGER,
    "fsrs_reps" INTEGER DEFAULT 0,
    "fsrs_lapses" INTEGER DEFAULT 0,
    "fsrs_state" TEXT DEFAULT 'New',
    "fsrs_last_review" DATETIME,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "user_learning_records_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "user_learning_records_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "user_learning_records_vocabulary_id_fkey" FOREIGN KEY ("vocabulary_id") REFERENCES "vocabulary" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "user_learning_records_grammar_point_id_fkey" FOREIGN KEY ("grammar_point_id") REFERENCES "grammar_points" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "fsrs_review_logs" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "learning_record_id" INTEGER NOT NULL,
    "rating" INTEGER NOT NULL,
    "state" TEXT NOT NULL,
    "due" DATETIME NOT NULL,
    "stability" REAL NOT NULL,
    "difficulty" REAL NOT NULL,
    "elapsed_days" INTEGER NOT NULL,
    "last_elapsed_days" INTEGER NOT NULL,
    "scheduled_days" INTEGER NOT NULL,
    "learning_steps" INTEGER NOT NULL,
    "review_time" DATETIME NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fsrs_review_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "fsrs_review_logs_learning_record_id_fkey" FOREIGN KEY ("learning_record_id") REFERENCES "user_learning_records" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "scraping_logs" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "rss_source_id" INTEGER,
    "session_id" TEXT,
    "log_level" TEXT DEFAULT 'INFO',
    "message" TEXT NOT NULL,
    "url" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "scraping_logs_rss_source_id_fkey" FOREIGN KEY ("rss_source_id") REFERENCES "rss_sources" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ai_processing_queue" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "article_id" INTEGER NOT NULL,
    "processing_type" TEXT NOT NULL,
    "priority" INTEGER DEFAULT 0,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "retry_count" INTEGER DEFAULT 0,
    "error_message" TEXT,
    "scheduled_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
    "started_at" DATETIME,
    "completed_at" DATETIME,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ai_processing_queue_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "system_settings" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "user_settings" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "user_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ai_models" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "model_id" TEXT NOT NULL,
    "display_name" TEXT,
    "description" TEXT,
    "model_type" TEXT,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "api_keys" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "name" TEXT NOT NULL,
    "api_key" TEXT NOT NULL,
    "provider" TEXT NOT NULL DEFAULT 'google_gemini',
    "priority" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_used_at" DATETIME,
    "last_error" TEXT,
    "quota_reset_interval_minutes" INTEGER NOT NULL DEFAULT 60,
    "min_usage_interval_seconds" INTEGER NOT NULL DEFAULT 1,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "chat_sessions" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "session_type" TEXT NOT NULL DEFAULT 'qa',
    "title" TEXT,
    "scenario_config" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "chat_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "chat_messages" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "session_id" INTEGER NOT NULL,
    "role" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "chat_messages_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "chat_sessions" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "media_files" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "file_path" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "file_type" TEXT NOT NULL,
    "mime_type" TEXT,
    "article_id" INTEGER,
    "download_url" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "media_files_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "user_article_reads" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "article_id" INTEGER NOT NULL,
    "read_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "user_article_reads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "user_article_reads_article_id_fkey" FOREIGN KEY ("article_id") REFERENCES "articles" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "rss_sources_url_key" ON "rss_sources"("url");

-- CreateIndex
CREATE UNIQUE INDEX "articles_guid_key" ON "articles"("guid");

-- CreateIndex
CREATE UNIQUE INDEX "articles_url_key" ON "articles"("url");

-- CreateIndex
CREATE UNIQUE INDEX "article_translations_article_id_language_code_key" ON "article_translations"("article_id", "language_code");

-- CreateIndex
CREATE UNIQUE INDEX "vocabulary_word_reading_key" ON "vocabulary"("word", "reading");

-- CreateIndex
CREATE UNIQUE INDEX "article_vocabulary_article_id_vocabulary_id_position_start_key" ON "article_vocabulary"("article_id", "vocabulary_id", "position_start");

-- CreateIndex
CREATE UNIQUE INDEX "grammar_points_pattern_key" ON "grammar_points"("pattern");

-- CreateIndex
CREATE UNIQUE INDEX "article_grammar_points_article_id_grammar_point_id_key" ON "article_grammar_points"("article_id", "grammar_point_id");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "ai_prompts_name_key" ON "ai_prompts"("name");

-- CreateIndex
CREATE UNIQUE INDEX "user_learning_records_user_id_record_type_article_id_vocabulary_id_grammar_point_id_key" ON "user_learning_records"("user_id", "record_type", "article_id", "vocabulary_id", "grammar_point_id");

-- CreateIndex
CREATE UNIQUE INDEX "system_settings_key_key" ON "system_settings"("key");

-- CreateIndex
CREATE UNIQUE INDEX "user_settings_user_id_key_key" ON "user_settings"("user_id", "key");

-- CreateIndex
CREATE UNIQUE INDEX "ai_models_model_id_key" ON "ai_models"("model_id");

-- CreateIndex
CREATE UNIQUE INDEX "api_keys_api_key_key" ON "api_keys"("api_key");

-- CreateIndex
CREATE UNIQUE INDEX "media_files_file_path_key" ON "media_files"("file_path");

-- CreateIndex
CREATE INDEX "media_files_file_type_idx" ON "media_files"("file_type");

-- CreateIndex
CREATE INDEX "media_files_article_id_idx" ON "media_files"("article_id");

-- CreateIndex
CREATE INDEX "media_files_created_at_idx" ON "media_files"("created_at");

-- CreateIndex
CREATE INDEX "user_article_reads_user_id_idx" ON "user_article_reads"("user_id");

-- CreateIndex
CREATE INDEX "user_article_reads_article_id_idx" ON "user_article_reads"("article_id");

-- CreateIndex
CREATE INDEX "user_article_reads_read_at_idx" ON "user_article_reads"("read_at");

-- CreateIndex
CREATE UNIQUE INDEX "user_article_reads_user_id_article_id_key" ON "user_article_reads"("user_id", "article_id");
