
'use client';

import EnhancedDashboard from '@/components/EnhancedDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { Loader } from 'lucide-react';

function LoadingFallback() {
  return (
    <div className="flex h-full items-center justify-center py-12">
      <div className="text-center">
        <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
        <p className="text-gray-600">正在加载学习统计...</p>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  const { session, status } = useAuth();

  if (status === 'loading') {
    return <LoadingFallback />;
  }

  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <p className="text-gray-600">请先登录以查看学习统计</p>
        </div>
      </div>
    );
  }

  const userId = session.user.id.toString();

  return (
    <div className="container mx-auto px-4 py-6">
      <EnhancedDashboard userId={userId} />
    </div>
  );
}