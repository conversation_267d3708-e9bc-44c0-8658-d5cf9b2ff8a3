const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentPrompts() {
  try {
    console.log('=== 当前AI提示词内容 ===\n');

    // 查看文章分析系统提示词
    const systemPrompt = await prisma.ai_prompts.findUnique({
      where: { name: 'article_analysis_system' }
    });

    if (systemPrompt) {
      console.log('📋 文章分析系统提示词:');
      console.log(`版本: ${systemPrompt.version}`);
      console.log(`更新时间: ${systemPrompt.updated_at}`);
      console.log('内容:');
      console.log(systemPrompt.content);
      console.log('\n' + '='.repeat(80) + '\n');
    }

    // 查看文章分析主提示词
    const mainPrompt = await prisma.ai_prompts.findUnique({
      where: { name: 'article_analysis_prompt' }
    });

    if (mainPrompt) {
      console.log('📋 文章分析主提示词:');
      console.log(`版本: ${mainPrompt.version}`);
      console.log(`更新时间: ${mainPrompt.updated_at}`);
      console.log('内容:');
      console.log(mainPrompt.content);
      console.log('\n' + '='.repeat(80) + '\n');
    }

    // 查看所有AI提示词列表
    const allPrompts = await prisma.ai_prompts.findMany({
      select: {
        name: true,
        type: true,
        category: true,
        title: true,
        version: true,
        is_active: true,
        updated_at: true
      },
      orderBy: { name: 'asc' }
    });

    console.log('📋 所有AI提示词列表:');
    allPrompts.forEach(prompt => {
      console.log(`- ${prompt.name} (${prompt.type}/${prompt.category}) v${prompt.version} ${prompt.is_active ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentPrompts();
