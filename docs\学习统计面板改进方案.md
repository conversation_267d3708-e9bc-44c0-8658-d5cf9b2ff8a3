# 学习统计面板改进方案

## 1. 概述

本文档基于当前学习统计面板的实现情况，提出了全面的改进方案，旨在为用户提供更丰富、更有价值的学习数据分析和可视化展示。

## 2. 当前状态分析

### 2.1 现有功能
- 30天内活跃天数统计
- 已学词汇数量
- 已掌握词汇数量  
- 完成文章数量
- 词汇学习进度（按JLPT等级分布）
- 文章阅读进度

### 2.2 存在问题
- 统计指标相对简单，缺乏深度分析
- 缺少学习效率和质量评估
- 没有学习时间统计
- 缺少智能学习建议
- 没有成就激励系统

## 3. 改进方案

### 3.1 复习任务指标扩展

#### 3.1.1 新增指标
- **今日复习任务**：当天需要复习的卡片数量
- **明日复习任务**：明天需要复习的卡片数量
- **本周复习任务**：本周内需要复习的卡片数量
- **本月复习任务**：本月内需要复习的卡片数量
- **逾期复习任务**：已过期但未完成的复习任务

#### 3.1.2 技术实现
```typescript
interface ReviewTaskStats {
  today: number;        // 今日复习任务
  tomorrow: number;     // 明日复习任务  
  thisWeek: number;     // 本周复习任务
  thisMonth: number;    // 本月复习任务
  overdue: number;      // 逾期未复习
}

async getReviewTaskStats(userId: string) {
    const userIdInt = parseInt(userId);
    const now = new Date();
    
    // 计算各个时间点
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const weekEnd = new Date(now);
    weekEnd.setDate(weekEnd.getDate() + (7 - weekEnd.getDay()));
    
    const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const [todayTasks, tomorrowTasks, weekTasks, monthTasks, overdueTasks] = await prisma.$transaction([
        // 今日复习任务
        prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: 'learning',
                next_review_at: {
                    gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
                    lt: tomorrow
                }
            }
        }),
        // 明日复习任务
        prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: 'learning',
                next_review_at: {
                    gte: tomorrow,
                    lt: new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)
                }
            }
        }),
        // 本周复习任务
        prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: 'learning',
                next_review_at: {
                    gte: now,
                    lte: weekEnd
                }
            }
        }),
        // 本月复习任务
        prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: 'learning',
                next_review_at: {
                    gte: now,
                    lte: monthEnd
                }
            }
        }),
        // 逾期任务
        prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: 'learning',
                next_review_at: { lt: now }
            }
        })
    ]);

    return {
        today: todayTasks,
        tomorrow: tomorrowTasks,
        thisWeek: weekTasks,
        thisMonth: monthTasks,
        overdue: overdueTasks
    };
}
```

### 3.2 学习效率统计

#### 3.2.1 效率指标定义
- **复习成功率**：基于SRS反馈质量计算的正确率
- **平均重复次数**：词汇达到掌握状态的平均复习次数
- **学习进度效率**：单位时间内新学会的词汇数量
- **记忆保持率**：成功复习的比例

#### 3.2.2 技术实现
```typescript
async getLearningEfficiencyStats(userId: string) {
    const userIdInt = parseInt(userId);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // 1. 复习正确率（基于SRS反馈质量）
    const reviewStats = await prisma.$queryRaw`
        SELECT 
            COUNT(*) as total_reviews,
            AVG(CASE WHEN last_quality >= 3 THEN 1.0 ELSE 0.0 END) as success_rate,
            AVG(repetitions) as avg_repetitions
        FROM user_learning_records 
        WHERE user_id = ${userIdInt} 
        AND last_reviewed_at >= ${thirtyDaysAgo}
        AND status = 'learning'
    `;

    // 2. 学习进度效率（新学会的词汇/时间）
    const progressEfficiency = await prisma.user_learning_records.count({
        where: {
            user_id: userIdInt,
            status: { in: ['learning', 'mastered'] },
            updated_at: { gte: thirtyDaysAgo }
        }
    });

    // 3. 记忆保持率（成功复习的比例）
    const retentionRate = await prisma.$queryRaw`
        SELECT 
            COUNT(CASE WHEN next_review_at > NOW() THEN 1 END) as on_schedule,
            COUNT(*) as total
        FROM user_learning_records 
        WHERE user_id = ${userIdInt} 
        AND status = 'learning'
    `;

    return {
        reviewSuccessRate: reviewStats[0]?.success_rate || 0,
        averageRepetitions: reviewStats[0]?.avg_repetitions || 0,
        progressEfficiency: progressEfficiency / 30, // 每日新学词汇
        retentionRate: retentionRate[0]?.on_schedule / retentionRate[0]?.total || 0
    };
}
```

### 3.3 学习时间统计

#### 3.3.1 实现方案

**方案一：基于用户行为推算（推荐）**
- 根据学习活动记录推算学习时间
- 每个词汇学习估算2分钟
- 每个语法点学习估算3分钟
- 每次复习估算1分钟

**方案二：实际时间追踪（更精确但复杂）**
- 前端实现学习会话时间追踪
- 记录用户实际学习时长
- 需要用户配合，但数据更准确

#### 3.3.2 推荐实现（方案一）
```typescript
async getEstimatedStudyTime(userId: string) {
    const userIdInt = parseInt(userId);
    
    // 基于学习活动推算时间
    const dailyActivities = await prisma.$queryRaw`
        SELECT 
            DATE(created_at) as study_date,
            COUNT(*) as activities,
            COUNT(DISTINCT vocabulary_id) as vocab_count,
            COUNT(DISTINCT grammar_point_id) as grammar_count
        FROM user_learning_records 
        WHERE user_id = ${userIdInt}
        AND created_at >= DATE('now', '-30 days')
        GROUP BY DATE(created_at)
    `;

    // 估算时间：每个词汇2分钟，每个语法点3分钟，每次复习1分钟
    const estimatedTimes = dailyActivities.map(day => ({
        date: day.study_date,
        estimatedMinutes: (day.vocab_count * 2) + (day.grammar_count * 3) + (day.activities * 1)
    }));

    return estimatedTimes;
}
```

## 4. 新增功能模块

### 4.1 学习活动热力图
- 类似GitHub贡献图的学习活跃度展示
- 显示过去一年的学习活动（365天）
- 颜色深度表示当日学习强度（5个等级）
- 鼠标悬停显示具体日期和学习项目数量
- 底部显示强度图例和时间范围

#### 技术实现
```typescript
// 热力图数据结构
interface HeatmapData {
  date: string;        // YYYY-MM-DD 格式
  intensity: number;   // 0-4 强度等级
  count: number;       // 学习项目数量
}

// 生成热力图数据
const generateHeatmapData = () => {
  const data = [];
  const today = new Date();
  for (let i = 364; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const intensity = calculateIntensity(date); // 基于实际学习记录计算
    data.push({
      date: date.toISOString().split('T')[0],
      intensity,
      count: getStudyCountForDate(date)
    });
  }
  return data;
};
```

### 4.2 学习/复习趋势柱状图
- 支持三种时间维度：一周、一月、一年
- 过去和未来数据的视觉区分
- 交互式时间段切换
- 详细的数据标签和提示

#### 功能特性
1. **时间维度切换**
   - 一周：显示过去7天 + 未来7天的每日数据
   - 一月：显示过去30天 + 未来15天的每日数据
   - 一年：显示过去52周 + 未来12周的每周合计数据

2. **视觉区分**
   - 已完成：实心蓝色柱状图
   - 计划中：虚线边框的浅蓝色柱状图
   - 图例说明清晰标识

3. **交互体验**
   - 鼠标悬停显示具体数值
   - 时间标签倾斜显示避免重叠
   - 响应式设计适配不同屏幕

#### 技术实现
```typescript
// 柱状图数据结构
interface ChartData {
  label: string;       // 显示标签（日期或周）
  date: string;        // 完整日期
  studied: number;     // 已学习/复习数量
  scheduled: number;   // 计划复习数量
  isPast: boolean;     // 是否为过去的数据
}

// 生成不同时间维度的数据
const generateChartData = (period: 'week' | 'month' | 'year') => {
  // 根据时间维度生成相应的数据点
  // 过去数据从学习记录中获取
  // 未来数据从复习计划中计算
};
```

### 4.3 智能学习建议
- 基于用户数据生成个性化建议
- 复习正确率低时建议增加复习频率
- 连续学习天数少时建议设置学习提醒

### 4.4 成就系统
- 学习里程碑徽章（如"掌握100个词汇"）
- 连续学习成就（如"连续学习7天"）
- 等级突破成就（如"N4征服者"）

### 4.5 学习目标设定
- 每日新词汇目标
- 每周复习目标
- 月度学习目标
- 目标完成进度追踪

### 4.6 快速操作面板
- 开始复习按钮
- 生词本快速访问
- 语法本快速访问
- AI助教快速访问

## 5. 实施优先级

### 5.1 高优先级
1. 复习任务指标扩展
2. 学习效率基础统计
3. 学习/复习趋势柱状图
4. 快速操作面板

### 5.2 中优先级
1. 学习活动热力图
2. 智能学习建议
3. 基础成就系统
4. 学习时间估算统计

### 5.3 低优先级
1. 学习目标设定
2. 高级图表和可视化
3. 学习报告导出
4. 实际时间追踪



## 6. 技术要求

### 6.1 后端开发
- 在 `src/lib/server/database.ts` 中添加新的统计方法
- 在 `src/app/actions.ts` 中创建对应的 Server Actions
- 确保数据查询性能优化

### 6.2 前端开发
- 修改 `src/components/Dashboard.tsx` 组件
- 添加新的图表和可视化组件
- 实现响应式布局
- 添加加载状态和错误处理

### 6.3 数据库优化
- 为复习任务查询添加适当的索引
- 优化统计查询的性能
- 考虑数据缓存策略

## 7. 预期效果

### 7.1 用户体验提升
- 更全面的学习数据展示
- 个性化的学习建议
- 激励性的成就系统
- 便捷的快速操作

### 7.2 学习效果改善
- 帮助用户更好地规划复习时间
- 提供学习效率反馈
- 激励持续学习习惯
- 优化学习策略

## 8. 后续扩展

### 8.1 高级功能
- 学习报告生成和导出
- 学习数据对比分析
- 社交学习功能
- 学习计划智能推荐

### 8.2 技术优化
- 实时数据更新
- 更精确的时间追踪
- 机器学习优化建议
- 数据可视化增强
