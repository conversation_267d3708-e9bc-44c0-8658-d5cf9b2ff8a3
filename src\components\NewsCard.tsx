'use client';
import React from 'react';
import {
  Volume2,
  Bookmark,
  Book,
  Calendar,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Video,
  Type,
  CheckCircle
} from 'lucide-react';
import { renderRubyText } from '@/utils/ruby-utils';
import { getMediaSrc } from '@/utils/media-utils';

interface NewsCardProps {
  article: any;
  onRead: () => void;
  showContent: boolean;
  showFurigana: boolean;
}

const NewsCard: React.FC<NewsCardProps> = ({ article, onRead, showContent, showFurigana }) => {
  const [showFullSummary, setShowFullSummary] = React.useState(false);
  


  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '科技': 'bg-purple-100 text-purple-800',
      '文化': 'bg-pink-100 text-pink-800',
      '环境': 'bg-emerald-100 text-emerald-800',
      '经济': 'bg-blue-100 text-blue-800',
      '社会': 'bg-orange-100 text-orange-800',
      '政治': 'bg-red-100 text-red-800',
      '国际': 'bg-indigo-100 text-indigo-800',
      '综合新闻': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '未知时间';
    
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays <= 1) return '今天';
      if (diffDays === 2) return '昨天';
      if (diffDays <= 7) return `${diffDays}天前`;
      
      return date.toLocaleDateString('zh-CN');
    } catch (error) {
      return '未知时间';
    }
  };

  const getProcessingStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'completed': 'bg-green-100 text-green-800',
      'processing': 'bg-blue-100 text-blue-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'failed': 'bg-red-100 text-red-800',
      'disabled': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getProcessingStatusLabel = (status: string) => {
    const labels: { [key: string]: string } = {
      'completed': '已完成',
      'processing': '处理中',
      'pending': '待处理',
      'failed': '失败',
      'disabled': '未启用'
    };
    return labels[status] || '未知';
  };

  const toggleSummary = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowFullSummary(!showFullSummary);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 group overflow-hidden">
      <div className="p-4 md:p-6 flex items-start space-x-4">
        {(article.featured_image_path || article.featured_image_url) && (
          <div className="flex-shrink-0 w-24 h-24 md:w-32 md:h-32">
            <img
              src={getMediaSrc(article.featured_image_path, article.featured_image_url) || ''}
              alt={article.title}
              className="w-full h-full object-cover rounded-lg"
              onError={(e) => {
                // If the primary src fails (could be local or remote), hide the element
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        )}
        <div className="flex-1 min-w-0">
          <div className="flex items-center flex-wrap gap-2 mb-3">
            {(article.video_url || article.video_path) && (
              <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <Video className="h-3 w-3" />
                <span>视频</span>
              </span>
            )}

            {/* Read Status */}
            {article.isRead && (
              <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3" />
                <span>已阅读</span>
              </span>
            )}

            {(article.rss_source?.category || article.category) && (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(article.rss_source?.category || article.category)}`}>
                {article.rss_source?.category || article.category}
              </span>
            )}

            {article.ai_processing_status && (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProcessingStatusColor(article.ai_processing_status)}`}>
                AI: {getProcessingStatusLabel(article.ai_processing_status)}
              </span>
            )}
            
            <span className="flex items-center space-x-1 text-xs text-gray-500">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(article.publishTime || article.publish_time || article.created_at)}</span>
            </span>

            <span className="flex items-center space-x-1 text-xs text-gray-500">
              <Book className="h-3 w-3" />
              <span>{article.vocabularyCount || 0}词汇</span>
            </span>

            <span className="flex items-center space-x-1 text-xs text-gray-500">
              <Type className="h-3 w-3" />
              <span>{article.grammarCount || 0}语法</span>
            </span>
            
            <div className="flex items-center space-x-2 ml-auto">
              <button 
                className="p-1 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-all"
                onClick={(e) => {
                  e.stopPropagation();
                  // 播放音频逻辑
                }}
              >
                <Volume2 className="h-4 w-4" />
              </button>
              <a 
                href={article.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all"
                onClick={(e) => e.stopPropagation()}
              >
                <ExternalLink className="h-4 w-4" />
              </a>
              <button 
                className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-all"
                onClick={(e) => {
                  e.stopPropagation();
                  // 收藏逻辑
                }}
              >
                <Bookmark className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <h3
            className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors line-clamp-2 cursor-pointer break-words"
            onClick={onRead}
          >
            {showFurigana && article.title_furigana_html ? (
              <span className="prose">
                {renderRubyText(article.title_furigana_html)}
              </span>
            ) : (
              article.title
            )}
          </h3>
          
          {article.subtitle && (
            <div className="mb-3">
              {showFurigana && article.subtitle_furigana_html ? (
                <div
                  className="text-sm text-gray-600 prose"
                  dangerouslySetInnerHTML={{ __html: article.subtitle_furigana_html }}
                />
              ) : (
                <>
                  <p className={`text-sm text-gray-600 break-words ${!showFullSummary ? 'line-clamp-2' : ''}`}>
                    {article.subtitle}
                  </p>
                  {article.subtitle.length > 100 && (
                    <button 
                      onClick={toggleSummary}
                      className="text-xs text-indigo-600 hover:text-indigo-800 mt-1 flex items-center"
                    >
                      {showFullSummary ? (
                        <>
                          <ChevronUp className="h-3 w-3 mr-1" />
                          收起概要
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-3 w-3 mr-1" />
                          展开完整概要
                        </>
                      )}
                    </button>
                  )}
                </>
              )}
            </div>
          )}
          
          {showContent && (
            <p className="text-gray-700 leading-relaxed mb-4 line-clamp-3 break-words">
              {article.content}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewsCard;
