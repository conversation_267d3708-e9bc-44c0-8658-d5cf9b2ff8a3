const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateGrammarPrompts() {
  try {
    console.log('=== 更新语法分析提示词以满足新需求 ===\n');

    // 更新语法分析主提示词
    await prisma.ai_prompts.update({
      where: { name: 'grammar_analysis_prompt' },
      data: {
        content: `提供された日本語テキストを分析し、重要な文法項目を特定してください。各文法項目について以下の情報を含むJSON配列を返してください：

**各文法項目に必須で含める情報：**

1. **pattern**: 文法パターン（例：「～について」「～に関して」）

2. **reading**: パターンに漢字が含まれる場合のひらがな読み（オプション）

3. **explanation**: 中国語での詳細な意味と用法の説明

4. **explanationJa**: 日本語での詳細な説明（すべての漢字にruby注音を付ける）【需求3】
   例：「「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。<ruby>何<rt>なに</rt></ruby>かの<ruby>内容<rt>ないよう</rt></ruby>や<ruby>事柄<rt>ことがら</rt></ruby>を<ruby>話<rt>はな</rt></ruby>したり<ruby>考<rt>かんが</rt></ruby>えたりする<ruby>時<rt>とき</rt></ruby>に<ruby>使<rt>つか</rt></ruby>います。」

5. **examples**: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]

6. **commonCollocations**: 2-3個の常見搭配【需求4】
   例：["～について話す", "～について考える", "～について書く"]

7. **similarGrammar**: 3-5個の類似文法パターン【需求5・6】
   各項目に以下を含む：
   - pattern: 類似文法パターン
   - difference: 主要文法との違い（中国語説明）
   - examples: 正確に2つの例文と中国語翻訳【需求6】
     例：["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]

8. **difficulty**: JLPT レベル（N1-N5）

分析対象のテキスト：
{{{text}}}

重要な注意事項：
- 日本語説明（explanationJa）は必須で、すべての漢字にruby注音を付けてください
- 例文は必ず指定された数を提供してください（主要文法：3つ、類似文法：各2つ）
- 常見搭配は2-3個提供してください
- 類似文法は3-5個提供し、各項目に正確に2つの例文を含めてください
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- 特定の文法項目が見つからない場合は、空の配列を返してください`,
        version: '3.0'
      }
    });

    console.log('✅ 语法分析主提示词已更新至v3.0');

    // 更新语法分析系统提示词
    await prisma.ai_prompts.update({
      where: { name: 'grammar_analysis_system' },
      data: {
        content: `あなたは日本語文法の専門家です。中国語話者の日本語学習者のために、提供された日本語テキストを分析し、重要な文法項目を特定することが任務です。

特に以下の4つの重要な要求を満たす必要があります：

3. **语法的日语解释**：すべての文法項目について、日本語での詳細な説明を提供し、説明文中のすべての漢字にruby注音を付けてください。

4. **语法的常见搭配**：各文法項目について、2-3個の常見搭配を提供してください。

5. **相似语法的数量**：各文法項目について、3-5個の類似文法パターンを提供してください。

6. **相似语法的例句**：各類似文法について、正確に2つの例文と中国語翻訳を提供してください。

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSON配列の前後に一切のテキストを追加しないでください
3. 各文法項目について、必ず以下をすべて提供してください：
   - pattern: 文法パターン（例：「～について」）
   - explanation: 中国語での詳細で正確な説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音付き）【必須】
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
   - commonCollocations: 2-3個の常見搭配【必須】
   - similarGrammar: 3-5個の類似文法（各項目に2つの例文）【必須】
   - difficulty: JLPT レベル（N1-N5）
4. 例文は必ず「日本語例文 - 中国語翻訳」の形式で提供してください
5. 特定の文法項目が見つからない場合は、空の配列を返してください
6. 出力は一貫性を保ち、完全で正確である必要があります
7. 日本語説明（explanationJa）は必須フィールドです
8. 類似文法は指定された数（3-5個）と例文数（各2つ）を厳守してください`,
        version: '3.0'
      }
    });

    console.log('✅ 语法分析系统提示词已更新至v3.0');

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateGrammarPrompts();
