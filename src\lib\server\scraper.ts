
import fetch from 'node-fetch';
import * as cheerio from 'cheerio';
import fs from 'fs';
import fsp from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { RSSFieldMappingParser, RSSFetcher } from './rss-parser';
import { dbManager } from './database';
import { antiCrawler } from './anti-crawler';
import { runInNewContext } from 'vm';
import { JSDOM } from 'jsdom';
import { Parser } from 'm3u8-parser';
import muxjs from 'mux.js';
import * as crypto from 'crypto';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..', '..', '..');

export class EnhancedNHKScraper {
  private mediaDir: string;
  private imagesDir: string;
  private videosDir: string;
  private audiosDir: string;
  private rssFetcher: RSSFetcher;
  public sessionId: string;
  private delayConfig: any;

  constructor() {
    this.mediaDir = join(projectRoot, 'public', 'media');
    this.imagesDir = join(this.mediaDir, 'images');
    this.videosDir = join(this.mediaDir, 'videos');
    this.audiosDir = join(this.mediaDir, 'audios');
    
    this.rssFetcher = new RSSFetcher();
    this.sessionId = this.generateSessionId();
    this.delayConfig = null;

    this.ensureDirectories();
  }

  private generateSessionId() {
    return `scrape_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 初始化延迟配置
  private async initDelayConfig() {
    if (!this.delayConfig) {
      this.delayConfig = await dbManager.getScrapingDelayConfig();
    }
    return this.delayConfig;
  }

  public resetSession() {
    this.sessionId = this.generateSessionId();
  }

  private ensureDirectories() {
    [this.mediaDir, this.imagesDir, this.videosDir, this.audiosDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  private async log(sessionId: string, level: string, message: string, rssSourceId: number | null = null, url: string | null = null) {
    try {
      await dbManager.addLog(sessionId, level, message, rssSourceId || undefined, url || undefined);
      const logMessage = `[${new Date().toLocaleTimeString()}] ${level}: ${message}`;
      if (level === 'ERROR') console.error(logMessage);
      else console.log(logMessage);
    } catch (error) {
      console.error('日志记录失败:', error);
    }
  }

  private async getRSSSourceById(id: number) {
    try {
      const source = await dbManager.getRSSSourceById(id);
      if (!source || !source.rss_field_mappings) {
          return null;
      }

      const fieldMappings: { [key: string]: any } = {};
      source.rss_field_mappings.forEach((mapping: any) => {
        fieldMappings[mapping.field_name] = {
          xpath_selector: mapping.xpath_selector,
          css_selector: mapping.css_selector,
          attribute_name: mapping.attribute_name,
          default_value: mapping.default_value,
          is_required: mapping.is_required,
          transform_rule: mapping.transform_rule
        };
      });

      return { ...source, field_mappings: fieldMappings };
    } catch (error) {
      console.error('获取RSS源失败:', error);
      throw error;
    }
  }

  public async scrapeAllActiveSources() {
    await this.log(this.sessionId, 'INFO', '开始抓取所有活跃的RSS源...');

    // 在开始抓取前检查是否需要清理日志
    await dbManager.performLogCleanupIfNeeded();

    // 初始化延迟配置
    await this.initDelayConfig();

    try {
      const activeSources = await dbManager.getActiveRSSSources();
      await this.log(this.sessionId, 'INFO', `找到 ${activeSources.length} 个活跃的RSS源`);

      const results = [];
      for (let i = 0; i < activeSources.length; i++) {
        const source = activeSources[i];
        try {
          await this.log(this.sessionId, 'INFO', `开始处理RSS源: ${source.name}`, source.id);
          const result = await this.scrapeRSSSource(source.id);
          results.push(result);

          // RSS源之间的延迟（最后一个源不需要延迟）
          if (i < activeSources.length - 1) {
            await this.delay(this.delayConfig.rssSourceDelay);
          }
        } catch (error: any) {
          await this.log(this.sessionId, 'ERROR', `处理RSS源 ${source.name} 失败: ${error.message}`, source.id);
          results.push({ rssSourceId: source.id, error: error.message, success: 0, failed: 0, skipped: 0 });
        }
      }
      return results;
    } catch (error: any) {
      await this.log(this.sessionId, 'ERROR', `抓取过程中发生严重错误: ${error.message}`);
    }
  }

  private async scrapeRSSSource(rssSourceId: number) {
    const startTime = Date.now();
    let successCount = 0, failCount = 0, skipCount = 0;

    const rssSource = await this.getRSSSourceById(rssSourceId);
    if (!rssSource) throw new Error(`RSS源 ${rssSourceId} 不存在`);

    const xmlContent = await this.rssFetcher.fetchRSSContent(rssSource.url);
    const parser = new RSSFieldMappingParser(rssSource.field_mappings);
    const { items: rssItems } = parser.parseRSSFeed(xmlContent);
    const limitedItems = rssItems.slice(0, rssSource.max_articles || 10);
    await this.log(this.sessionId, 'INFO', `找到 ${limitedItems.length} 篇文章进行处理`, rssSourceId);

    for (const [index, item] of limitedItems.entries()) {
        try {
            await this.log(this.sessionId, 'INFO', `[${index + 1}/${limitedItems.length}] 开始处理: ${item.title}`, rssSourceId, item.link);

            const articleResult = await this.processRSSItem(item, rssSource);
            let needsDelay = true; // 默认需要延迟

            if (articleResult) {
                if (articleResult.status === 'success') {
                    successCount++;
                } else if (articleResult.status === 'skipped') {
                    skipCount++;
                    needsDelay = false; // 跳过的文章不需要延迟
                }
            } else {
                failCount++;
            }

            // 只有在实际处理了文章内容时才延迟，且不是最后一篇文章
            if (needsDelay && index < limitedItems.length - 1) {
                await this.delay(this.delayConfig.articleDelay);
            }
        } catch (error: any) {
            failCount++;
            await this.log(this.sessionId, 'ERROR', `处理条目 "${item.title}" 失败: ${error.message}`, rssSourceId, item.link);
            // 错误情况下也需要延迟，避免连续错误请求
            if (index < limitedItems.length - 1) {
                await this.delay(this.delayConfig.articleDelay);
            }
        }
    }
    
    await dbManager.updateRSSSourceStats(rssSourceId, successCount, successCount);
    const processingTime = (Date.now() - startTime) / 1000;
    const summaryMessage = `RSS源抓取完成: ${successCount}篇成功, ${skipCount}篇跳过(已存在), ${failCount}篇失败.`;
    await this.log(this.sessionId, 'INFO', summaryMessage, rssSourceId);
    return { rssSourceId, success: successCount, failed: failCount, skipped: skipCount, processingTime };
  }
  
  private async processRSSItem(item: any, rssSource: any): Promise<{ status: 'success' | 'skipped', id: number, title: string } | null> {
    const existingArticle = await dbManager.getArticleByUrl(item.link);
    if (existingArticle) {
      await this.log(this.sessionId, 'INFO', `文章已存在, 跳过: ${item.title}`, rssSource.id, item.link);
      return { status: 'skipped', id: existingArticle.id, title: item.title };
    }

    const fullContent = await this.scrapeFullArticle(item.link, rssSource.content_selector, { pubDate: item.pub_date });
    
    if ((!fullContent || !fullContent.content) && !item.description) {
        await this.log(this.sessionId, 'ERROR', '未能提取到正文，且概要也为空，跳过文章。', rssSource.id, item.link);
        return null;
    }

    const articleContent = fullContent?.content || '';
    const articleHtml = fullContent?.html || '';

    if (!articleContent && item.description) {
        await this.log(this.sessionId, 'WARN', '未能提取到正文，但概要存在。将使用空正文保存文章。', rssSource.id, item.link);
    }

    const finalImageUrl = fullContent?.image_url || item.image_url;

    let videoMetadata: { metadata: any, m3u8Content: string } | null = null;
    const videoUrlFromContent = fullContent?.video_url;
    if (videoUrlFromContent && videoUrlFromContent.endsWith('.html')) {
        videoMetadata = await this.extractVideoMetadata(videoUrlFromContent);
    }

    // 先创建文章记录（不包含媒体文件路径）
    const articleData = {
      rss_source_id: rssSource.id,
      guid: item.guid || item.link,
      title: item.title,
      subtitle: item.description,
      content: articleContent,
      content_html: articleHtml,
      url: item.link,
      publish_time: item.pub_date,
      featured_image_url: finalImageUrl,
      featured_image_path: null, // 稍后更新
      audio_url: fullContent?.audio_url,
      audio_path: null, // 稍后更新
      ai_processing_status: rssSource.enable_ai_processing ? 'pending' : 'disabled',
      processing_status: 'completed',
      video_url: videoUrlFromContent,
      video_path: null,
      video_metadata_json: videoMetadata ? JSON.stringify(videoMetadata.metadata) : null,
      video_m3u8_content: videoMetadata ? videoMetadata.m3u8Content : null,
      video_download_status: videoUrlFromContent ? 'pending' : 'disabled',
      use_local_video: false,
    };
    const articleId = await dbManager.createArticle(articleData);

    // 下载媒体文件并关联到文章
    let localImagePath = null;
    let localAudioPath = null;

    if (finalImageUrl) {
      localImagePath = await this.downloadMedia(finalImageUrl, 'image', { pubDate: item.pub_date, articleId });
    }

    if (fullContent?.audio_url) {
      localAudioPath = await this.downloadMedia(fullContent.audio_url, 'audio', { pubDate: item.pub_date, articleId });
    }

    // 更新文章记录中的媒体文件路径
    if (localImagePath || localAudioPath) {
      await dbManager.updateArticleMediaPaths(articleId, {
        featured_image_path: localImagePath,
        audio_path: localAudioPath
      });
    }
    await this.log(this.sessionId, 'INFO', `文章处理成功: ${item.title}`, rssSource.id, item.link);
    
    if (rssSource.enable_ai_processing) {
        await dbManager.scheduleAIProcessing(articleId);
    }
    return { status: 'success', id: articleId, title: item.title };
  }

  public async extractVideoMetadata(playerPageUrl: string): Promise<{ metadata: any, m3u8Content: string } | null> {
    await this.log(this.sessionId, 'INFO', `发现内嵌视频播放器页面，正在提取M3U8元数据...`);
    try {
        const playerResponse = await antiCrawler.safeRequest(playerPageUrl);
        const playerHtml = await playerResponse.text();

        // 尝试新的nPlayer v2配置格式
        const newNPlayerMatch = playerHtml.match(/new nPlayer\.player\([^)]*'([^']+\.json)'\)/);
        if (newNPlayerMatch && newNPlayerMatch[1]) {
            const jsonFileName = newNPlayerMatch[1];
            const baseUrl = playerPageUrl.substring(0, playerPageUrl.lastIndexOf('/') + 1);
            const jsonUrl = baseUrl + jsonFileName;

            await this.log(this.sessionId, 'INFO', `找到nPlayer v2配置JSON: ${jsonUrl}`);

            const jsonResponse = await antiCrawler.safeRequest(jsonUrl);
            const videoJson = await jsonResponse.json() as any;

            const playlistUrl = videoJson?.mediaResource?.url;
            if (!playlistUrl) {
                throw new Error('在nPlayer v2配置中未找到M3U8播放列表URL');
            }

            const videoTitle = videoJson?.va?.adobe?.vodContentsID?.VInfo1 || path.basename(new URL(playerPageUrl).pathname, '.html');

            await this.log(this.sessionId, 'INFO', `找到M3U8播放列表URL: ${playlistUrl}`);

            const m3u8Response = await fetch(playlistUrl);
            const m3u8Content = await m3u8Response.text();

            return {
                metadata: { m3u8Url: playlistUrl, title: videoTitle },
                m3u8Content: m3u8Content
            };
        }

        // 回退到旧的nPlayer v1配置格式（向后兼容）
        const oldScriptContentMatch = playerHtml.match(/nPlayer\((\{[\s\S]*?\})\)/);
        if (oldScriptContentMatch && oldScriptContentMatch[1]) {
            const playerConfig = JSON.parse(oldScriptContentMatch[1]);
            const jsonUrl = playerConfig?.playlist?.find((p: any) => p.type === 'application/json')?.src;

            if (!jsonUrl) {
              throw new Error('在播放器配置中未找到视频JSON URL');
            }

            await this.log(this.sessionId, 'INFO', `找到视频JSON URL: ${jsonUrl}`);

            const jsonResponse = await antiCrawler.safeRequest(jsonUrl);
            const videoJson = await jsonResponse.json() as any;

            const playlistUrl = videoJson?.mediaResource?.url;
            if (!playlistUrl) {
                throw new Error('在视频JSON中未找到M3U8播放列表URL');
            }

            const videoTitle = videoJson?.va?.adobe?.vodContentsID?.VInfo1 || path.basename(new URL(playerPageUrl).pathname, '.html');

            const m3u8Response = await fetch(playlistUrl);
            const m3u8Content = await m3u8Response.text();

            return {
                metadata: { m3u8Url: playlistUrl, title: videoTitle },
                m3u8Content: m3u8Content
            };
        }

        throw new Error('在播放器页面中未找到 nPlayer 配置（v1或v2格式）');

    } catch (error: any) {
        await this.log(this.sessionId, 'ERROR', `从播放器页面提取视频元数据失败: ${error.message}`);
        return null;
    }
  }

  // AES-128解密相关方法
  private extractKeyInfo(playlistContent: string): { method: string; uri: string } | null {
    const keyMatch = playlistContent.match(/#EXT-X-KEY:METHOD=([^,]+),URI="([^"]+)"/);
    if (keyMatch) {
      return {
        method: keyMatch[1],
        uri: keyMatch[2]
      };
    }
    return null;
  }

  private async downloadKey(keyUri: string, hlsDir: string, sessionId: string): Promise<string | null> {
    try {
      await this.log(sessionId, 'INFO', `[AES] 下载解密密钥: ${keyUri}`);

      const response = await fetch(keyUri);
      if (!response.ok) {
        throw new Error(`密钥下载失败: ${response.statusText}`);
      }

      const keyData = await response.arrayBuffer();
      const keyPath = join(hlsDir, 'serve.key');
      fs.writeFileSync(keyPath, new Uint8Array(keyData));

      await this.log(sessionId, 'INFO', `[AES] 密钥已保存到: ${keyPath}`);
      return keyPath;
    } catch (error: any) {
      await this.log(sessionId, 'ERROR', `[AES] 密钥下载失败: ${error.message}`);
      return null;
    }
  }

  private calculateIV(segmentIndex: number): Buffer {
    // HLS标准: IV是16字节，前12字节为0，后4字节为segment序号（大端序）
    const iv = Buffer.alloc(16, 0);
    // 注意：HLS的segment序号通常从0开始，不是1
    iv.writeUInt32BE(segmentIndex, 12);
    return iv;
  }

  private decryptSegment(encryptedData: Buffer, key: Buffer, iv: Buffer): Buffer {
    try {
      // 确保输入数据长度是16字节的倍数（AES块大小）
      if (encryptedData.length % 16 !== 0) {
        throw new Error(`加密数据长度不是16的倍数: ${encryptedData.length}`);
      }

      const decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
      // 对于TS文件，通常不使用自动填充，因为TS包有固定的188字节结构
      decipher.setAutoPadding(false);

      const decrypted = Buffer.concat([
        decipher.update(encryptedData),
        decipher.final()
      ]);

      return decrypted;
    } catch (error) {
      throw new Error(`AES解密失败: ${error}`);
    }
  }

  public async downloadAndRemux(playlistBody: string, initialPlaylistUrl: string, title: string, sessionId: string, options: { pubDate?: string } = {}): Promise<string> {
    const safeTitle = title.replace(/[^a-z0-9\u4e00-\u9fa5\u3040-\u309f\u30a0-\u30ff-]/gi, '_').substring(0, 50);

    const date = options.pubDate ? new Date(options.pubDate) : new Date();
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    const yearDir = join(this.videosDir, year);
    const monthDir = join(yearDir, month);
    const dayDir = join(monthDir, day);

    if (!fs.existsSync(yearDir)) fs.mkdirSync(yearDir, { recursive: true });
    if (!fs.existsSync(monthDir)) fs.mkdirSync(monthDir, { recursive: true });
    if (!fs.existsSync(dayDir)) fs.mkdirSync(dayDir, { recursive: true });

    // 创建本地HLS文件夹
    const hlsDir = join(dayDir, safeTitle);
    if (!fs.existsSync(hlsDir)) fs.mkdirSync(hlsDir, { recursive: true });

    const playlistFilename = 'index.m3u8';
    const fullPlaylistPath = join(hlsDir, playlistFilename);
    const dbPath = `/api/media/videos/${year}/${month}/${day}/${safeTitle}/${playlistFilename}`;

    await this.log(sessionId, 'INFO', `[HLSDownloader] 初始化下载`);

    try {
      let playlistUrl = initialPlaylistUrl;
      let mediaPlaylistBody = playlistBody; // 保存播放列表内容用于密钥检测
      let isMasterPlaylist = false; // 标记是否为主播放列表
      let parser = new Parser();
      parser.push(playlistBody);
      parser.end();

      if (parser.manifest.playlists && parser.manifest.playlists.length > 0) {
        isMasterPlaylist = true;
        await this.log(sessionId, 'INFO', `[HLSDownloader] Master playlist detected. Selecting a variant stream.`);
        const mediaPlaylistUri = (parser.manifest.playlists[0] as any).uri;
        const mediaPlaylistUrl = new URL(mediaPlaylistUri, playlistUrl).href;

        await this.log(sessionId, 'INFO', `[HLSDownloader] Fetching media playlist`);
        playlistUrl = mediaPlaylistUrl;

        const mediaPlaylistResponse = await fetch(playlistUrl);
        mediaPlaylistBody = await mediaPlaylistResponse.text();

        parser = new Parser();
        parser.push(mediaPlaylistBody);
        parser.end();
      }

      const segments = parser.manifest.segments;
      if (!segments || segments.length === 0) {
        throw new Error('在M3U8播放列表中未找到任何视频片段。');
      }

      await this.log(sessionId, 'INFO', `[HLSDownloader] 发现 ${segments.length} 个片段。开始下载...`);

      // 检查是否需要AES解密
      let currentPlaylistContent = playlistBody;

      await this.log(sessionId, 'DEBUG', `[AES] 是否为主播放列表: ${isMasterPlaylist}`);
      await this.log(sessionId, 'DEBUG', `[AES] 初始播放列表长度: ${playlistBody.length}, 子播放列表长度: ${mediaPlaylistBody.length}`);

      // 如果是主播放列表，使用已经获取的子播放列表内容
      if (isMasterPlaylist) {
        currentPlaylistContent = mediaPlaylistBody;
        await this.log(sessionId, 'INFO', `[AES] 使用已获取的子播放列表进行密钥检测`);
        await this.log(sessionId, 'DEBUG', `[AES] 子播放列表前200字符: ${mediaPlaylistBody.substring(0, 200)}`);
      } else {
        await this.log(sessionId, 'INFO', `[AES] 使用主播放列表进行密钥检测`);
      }

      const keyInfo = this.extractKeyInfo(currentPlaylistContent);
      await this.log(sessionId, 'INFO', `[AES] 密钥检测结果: ${keyInfo ? `找到${keyInfo.method}密钥` : '未找到密钥'}`);

      // 添加调试信息
      if (!keyInfo) {
        await this.log(sessionId, 'DEBUG', `[AES] 播放列表内容预览: ${currentPlaylistContent.substring(0, 500)}...`);
        const hasKeyLine = currentPlaylistContent.includes('#EXT-X-KEY');
        await this.log(sessionId, 'DEBUG', `[AES] 播放列表包含EXT-X-KEY: ${hasKeyLine}`);
      }
      let decryptionKey: Buffer | null = null;

      if (keyInfo && keyInfo.method === 'AES-128') {
        await this.log(sessionId, 'INFO', `[AES] 检测到AES-128加密，下载解密密钥...`);
        const keyPath = await this.downloadKey(keyInfo.uri, hlsDir, sessionId);
        if (keyPath && fs.existsSync(keyPath)) {
          decryptionKey = fs.readFileSync(keyPath);
          await this.log(sessionId, 'INFO', `[AES] 解密密钥已准备就绪`);
        } else {
          await this.log(sessionId, 'WARN', `[AES] 密钥下载失败，将保存加密的片段`);
        }
      }

      // 下载所有TS片段
      const localSegmentNames: string[] = [];
      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const segmentUrl = new URL(segment.uri, playlistUrl).href;
        const segmentFilename = `segment_${i.toString().padStart(3, '0')}.ts`;
        const segmentPath = join(hlsDir, segmentFilename);

        if ((i + 1) % 10 === 0 || i === 0 || i === segments.length - 1) {
          await this.log(sessionId, 'INFO', `[HLSDownloader] 正在下载片段 ${i + 1}/${segments.length}`);
        }

        const response = await fetch(segmentUrl);
        if (!response.ok) {
            throw new Error(`下载片段失败 ${segmentUrl}: ${response.statusText}`);
        }

        const tsBuffer = await response.arrayBuffer();
        let tsData = new Uint8Array(tsBuffer);

        // AES解密处理
        if (decryptionKey) {
          try {
            const originalSize = tsData.length;
            const iv = this.calculateIV(i);

            if (i === 0) {
              await this.log(sessionId, 'DEBUG', `[AES] 开始解密 - 原始大小: ${originalSize}, IV: ${iv.toString('hex')}`);
            }

            const decryptedBuffer = this.decryptSegment(Buffer.from(tsData), decryptionKey, iv);
            tsData = new Uint8Array(decryptedBuffer);

            // 检查解密后的数据
            const firstBytes = Array.from(tsData.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ');

            if (i === 0) {
              await this.log(sessionId, 'INFO', `[AES] 片段解密成功 - 解密后大小: ${tsData.length}, 前8字节: ${firstBytes}`);
            } else if ((i + 1) % 5 === 0) {
              await this.log(sessionId, 'DEBUG', `[AES] 片段 ${i + 1} 解密完成 - 大小: ${tsData.length}`);
            }
          } catch (error: any) {
            await this.log(sessionId, 'ERROR', `[AES] 片段 ${i + 1} 解密失败: ${error.message}`);
            // 继续使用原始数据
          }
        } else {
          await this.log(sessionId, 'WARN', `[AES] 片段 ${i + 1} 跳过解密（无密钥）`);
        }

        // 验证TS文件格式，只在必要时进行修复
        let needsRepair = false;
        let syncPos = 0;

        // 首先检查文件是否以0x47开头
        if (tsData.length > 0 && tsData[0] === 0x47) {
          // 检查是否有连续的TS包结构
          let validPackets = 0;
          const totalPackets = Math.floor(tsData.length / 188);

          for (let k = 0; k < totalPackets && k * 188 < tsData.length; k++) {
            if (tsData[k * 188] === 0x47) {
              validPackets++;
            } else {
              break;
            }
          }

          // 如果大部分包都有效，认为文件格式正确
          if (validPackets >= Math.max(3, totalPackets * 0.9)) {
            await this.log(sessionId, 'DEBUG', `[HLSDownloader] 片段 ${i + 1} 格式正确 (${validPackets}/${totalPackets} 个有效TS包)`);
          } else {
            needsRepair = true;
            await this.log(sessionId, 'WARN', `[HLSDownloader] 片段 ${i + 1} TS包结构不完整，需要修复`);
          }
        } else {
          needsRepair = true;
          await this.log(sessionId, 'WARN', `[HLSDownloader] 片段 ${i + 1} 不以0x47开头，需要查找同步位置`);
        }

        // 只在真正需要时才进行修复
        if (needsRepair) {
          // 查找第一个有效的TS包序列
          let foundValidSequence = false;

          for (let j = 0; j < tsData.length - 188; j++) {
            if (tsData[j] === 0x47) {
              // 检查连续的TS包
              let consecutivePackets = 0;
              for (let k = j; k < tsData.length - 188; k += 188) {
                if (tsData[k] === 0x47) {
                  consecutivePackets++;
                } else {
                  break;
                }
              }

              if (consecutivePackets >= 3) {
                syncPos = j;
                foundValidSequence = true;
                await this.log(sessionId, 'INFO', `[HLSDownloader] 片段 ${i + 1} 找到有效序列，起始位置: ${j}, 连续包数: ${consecutivePackets}`);
                break;
              }
            }
          }

          if (!foundValidSequence) {
            await this.log(sessionId, 'ERROR', `[HLSDownloader] 片段 ${i + 1} 未找到有效的TS包序列，保存原始数据`);
            fs.writeFileSync(segmentPath, tsData);
            localSegmentNames.push(segmentFilename);
            continue;
          }
        }

        // 保存文件（修复或原始）
        if (needsRepair && syncPos > 0) {
          // 需要修复：移除前缀并对齐到188字节边界
          let repairedData = tsData.slice(syncPos);
          const packetCount = Math.floor(repairedData.length / 188);

          // 只有在会丢失大量数据时才警告
          const wouldLoseBytes = repairedData.length - (packetCount * 188);
          if (wouldLoseBytes > 16) { // 超过16字节才警告
            await this.log(sessionId, 'WARN', `[HLSDownloader] 片段 ${i + 1} 截断会丢失 ${wouldLoseBytes} 字节`);
          }

          repairedData = repairedData.slice(0, packetCount * 188);
          fs.writeFileSync(segmentPath, repairedData);

          await this.log(sessionId, 'INFO', `[HLSDownloader] 片段 ${i + 1} 已修复 (移除前 ${syncPos} 字节, 保留 ${packetCount} 个TS包)`);
        } else {
          // 文件格式正确，直接保存
          fs.writeFileSync(segmentPath, tsData);

          if (!needsRepair) {
            const packetCount = Math.floor(tsData.length / 188);
            await this.log(sessionId, 'DEBUG', `[HLSDownloader] 片段 ${i + 1} 无需修复 (${packetCount} 个TS包)`);
          }
        }
        localSegmentNames.push(segmentFilename);
      }

      await this.log(sessionId, 'INFO', `[HLSDownloader] 所有片段下载完成，生成本地播放列表...`);

      // 生成本地M3U8播放列表
      let localPlaylist = '#EXTM3U\n';
      localPlaylist += '#EXT-X-VERSION:3\n';
      localPlaylist += '#EXT-X-TARGETDURATION:10\n';
      localPlaylist += '#EXT-X-MEDIA-SEQUENCE:0\n';
      localPlaylist += '#EXT-X-PLAYLIST-TYPE:VOD\n';

      // 添加兼容性标记
      localPlaylist += '#EXT-X-INDEPENDENT-SEGMENTS\n';

      // 注意：由于我们在下载时已经解密了片段，本地播放列表不需要密钥信息

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const duration = segment.duration || 10.0;
        localPlaylist += `#EXTINF:${duration.toFixed(6)},\n`;
        localPlaylist += `${localSegmentNames[i]}\n`;
      }

      localPlaylist += '#EXT-X-ENDLIST\n';

      // 保存本地播放列表
      fs.writeFileSync(fullPlaylistPath, localPlaylist, 'utf8');

      await this.log(sessionId, 'INFO', `[HLSDownloader] 本地HLS流成功创建: ${fullPlaylistPath}`);

      // 检查文件
      const playlistStats = fs.statSync(fullPlaylistPath);
      const totalSize = localSegmentNames.reduce((sum, name) => {
        const segmentPath = join(hlsDir, name);
        return sum + fs.statSync(segmentPath).size;
      }, 0);

      await this.log(sessionId, 'INFO', `[HLSDownloader] 播放列表大小: ${playlistStats.size} bytes, 总视频大小: ${totalSize} bytes`);

      if (totalSize === 0) {
        throw new Error('视频片段总大小为0，下载可能失败');
      }

      return dbPath;
    } catch (error: any) {
        await this.log(sessionId, 'ERROR', `[HLSDownloader] 视频下载过程失败 for ${safeTitle}: ${error.message}`);
        // 清理失败的文件
        if (fs.existsSync(hlsDir)) {
            try {
              fs.rmSync(hlsDir, { recursive: true, force: true });
            } catch (e) {
              console.error(`Failed to cleanup HLS directory: ${e}`);
            }
        }
        throw error;
    }
  }

  private async scrapeFullArticle(url: string, contentSelector: string | undefined, options: { pubDate?: string } = {}): Promise<{ content: string; html: string; video_url: string | null; audio_url: string | null; image_url: string | null; } | null> {
    try {
        await this.log(this.sessionId, 'INFO', `开始抓取完整文章: ${url}`, undefined, url);
        const response = await antiCrawler.safeRequest(url);
        const html = await response.text();
        const $ = cheerio.load(html);

        let finalHtmlContent = '';
        let extractionMethod = 'N/A';
        let imageUrl: string | null = null;
        let videoUrl: string | null = null;

        const scripts = $('script');
        let detailPropScriptContent = '';
        for (let i = 0; i < scripts.length; i++) {
            const scriptContent = $(scripts[i]).html(); 
            if (scriptContent && scriptContent.includes('var __DetailProp__')) {
                detailPropScriptContent = scriptContent;
                break;
            }
        }

        if (detailPropScriptContent) {
            const match = detailPropScriptContent.match(/var\s+__DetailProp__\s*=\s*(\{[\s\S]*?\});/s);
            if (match && match[1]) {
                const detailPropString = match[1];
                try {
                    const detailProp = runInNewContext(`(${detailPropString})`, {});
                    
                    const imagePath = detailProp.largeImg || detailProp.img;
                    if (imagePath && typeof imagePath === 'string') {
                       imageUrl = new URL(imagePath.replace(/\\/g, '/'), "https://www3.nhk.or.jp/news/").href;
                    }

                    if (detailProp.video && typeof detailProp.video === 'string' && detailProp.video.trim() !== '') {
                        const rawVideoPath = detailProp.video;
                        if (rawVideoPath.startsWith('http')) {
                            videoUrl = rawVideoPath;
                        } else {
                            videoUrl = new URL(rawVideoPath.replace(/\\/g, '/'), "https://www3.nhk.or.jp/news/").href;
                        }
                    }
                    
                    const directContentHtml = detailProp.more_body || detailProp.more || detailProp.detail_more;
                    if (directContentHtml && typeof directContentHtml === 'string' && directContentHtml.trim().length > 0) {
                        finalHtmlContent += directContentHtml;
                        extractionMethod = '__DetailProp__ (Direct)';
                    }
                    
                    if (detailProp.body && Array.isArray(detailProp.body)) {
                        let bodyArrayContent = '';
                        for (const item of detailProp.body) {
                            if (item.title && typeof item.title === 'string') {
                                bodyArrayContent += `<h3>${item.title}</h3>`;
                            }
                            if (item.img && typeof item.img === 'string') {
                                const bodyImageUrl = new URL(item.img.replace(/\\/g, '/'), "https://www3.nhk.or.jp/news/").href;
                                // 下载正文图片到本地（暂不关联文章ID）
                                const localImagePath = await this.downloadMedia(bodyImageUrl, 'image', { pubDate: options.pubDate });
                                // 优先使用本地路径，如果下载失败则使用原始URL
                                const imageSrc = localImagePath || bodyImageUrl;
                                bodyArrayContent += `<img src="${imageSrc}" alt="${item.title || 'article image'}" style="max-width: 100%; height: auto; margin: 1rem 0; border-radius: 8px;" />`;
                            }
                            if (item.text && typeof item.text === 'string') {
                                bodyArrayContent += `<div>${item.text}</div>`;
                            }
                        }
                        
                        if (bodyArrayContent) {
                            finalHtmlContent += bodyArrayContent;
                            extractionMethod = extractionMethod.includes('Direct') ? '__DetailProp__ (Direct + Body Array)' : '__DetailProp__ (Body Array)';
                        }
                    }

                } catch (e: any) {
                    await this.log(this.sessionId, 'WARN', `使用 vm 解析 __DetailProp__ 失败: ${e.message}`, undefined, url);
                }
            }
        }

        if (!finalHtmlContent.trim()) {
            await this.log(this.sessionId, 'INFO', `__DetailProp__ 解析失败或无内容, 回退到CSS选择器抓取`, undefined, url);
            const selectors = (contentSelector || '.article-body, .content-body, article, .main-content, #news_textbody, .news_textbody').split(',').map(s => s.trim());
            for (const selector of selectors) {
                const potentialContent = $(selector);
                if (potentialContent.length > 0) {
                    const contentHtml = potentialContent.html() || '';
                    if (contentHtml.includes('var __DetailProp__')) {
                        await this.log(this.sessionId, 'WARN', `CSS选择器抓取到JS代码，忽略结果。`, undefined, url);
                    } else {
                        finalHtmlContent = contentHtml;
                        extractionMethod = `CSS Selector (${selector})`;
                        break;
                    }
                }
            }
        }
  
        const plainTextContent = finalHtmlContent ? cheerio.load(finalHtmlContent).text().trim() : '';

        await this.log(this.sessionId, 'INFO', `提取成功 (${extractionMethod}): ${url}`, undefined, url);
  
        return {
            content: plainTextContent,
            html: finalHtmlContent,
            video_url: videoUrl,
            audio_url: $('audio source').attr('src') || null,
            image_url: imageUrl
        };
    } catch (error: any) {
        await this.log(this.sessionId, 'ERROR', `抓取完整文章失败: ${url}: ${error.message}`, undefined, url);
        return null;
    }
  }

  private async downloadMedia(url: string | null, type: 'image' | 'video' | 'audio', options: { pubDate?: string, articleId?: number } = {}) {
    if (!url) return null;

    try {
        if (url.includes('.html')) {
            await this.log(this.sessionId, 'INFO', `URL指向HTML页面，跳过媒体下载`);
            return null;
        }

        const response = await fetch(url);

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('text/html')) {
            await this.log(this.sessionId, 'WARN', `尝试下载HTML页面作为媒体文件，已跳过`);
            return null;
        }

        const buffer = await response.buffer();

        // 1. Get original filename from URL
        const urlObj = new URL(url);
        const originalFilename = path.basename(urlObj.pathname);

        // 2. Determine year, month and day from pubDate, fallback to current date
        const date = options.pubDate ? new Date(options.pubDate) : new Date();
        const year = date.getFullYear().toString();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');

        // 3. Construct directory path
        const baseDir = type === 'image' ? this.imagesDir : type === 'video' ? this.videosDir : this.audiosDir;
        const yearDir = join(baseDir, year);
        const monthDir = join(yearDir, month);
        const dayDir = join(monthDir, day);

        // 4. Ensure directories exist (synchronously, as it's a prerequisite for writing)
        if (!fs.existsSync(yearDir)) fs.mkdirSync(yearDir, { recursive: true });
        if (!fs.existsSync(monthDir)) fs.mkdirSync(monthDir, { recursive: true });
        if (!fs.existsSync(dayDir)) fs.mkdirSync(dayDir, { recursive: true });

        // 5. Construct full file path and the relative path for the database
        const fullLocalPath = join(dayDir, originalFilename);
        const dbPath = `/api/media/${type}s/${year}/${month}/${day}/${originalFilename}`;

        // 6. Write file to disk
        await fsp.writeFile(fullLocalPath, buffer);

        // 7. Record file information in database
        try {
            await prisma.media_files.create({
                data: {
                    file_path: dbPath,
                    file_name: originalFilename,
                    file_size: buffer.length,
                    file_type: type,
                    mime_type: contentType || this.getMimeType(originalFilename, type),
                    article_id: options.articleId || null,
                    download_url: url
                }
            });
            await this.log(this.sessionId, 'INFO', `${type} 下载并记录成功: ${dbPath} (${buffer.length} bytes)`);
        } catch (dbError: any) {
            // 如果数据库记录失败，记录警告但不影响文件下载
            await this.log(this.sessionId, 'WARN', `文件下载成功但数据库记录失败: ${dbPath}: ${dbError.message}`);
        }

        return dbPath;
    } catch (error: any) {
        await this.log(this.sessionId, 'ERROR', `媒体文件下载失败: ${url}: ${error.message}`);
        return null;
    }
}
  




  private delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 根据文件扩展名和类型推断MIME类型
  private getMimeType(filename: string, type: 'image' | 'video' | 'audio'): string {
    const ext = path.extname(filename).toLowerCase();

    switch (type) {
      case 'image':
        switch (ext) {
          case '.jpg':
          case '.jpeg': return 'image/jpeg';
          case '.png': return 'image/png';
          case '.gif': return 'image/gif';
          case '.webp': return 'image/webp';
          case '.svg': return 'image/svg+xml';
          default: return 'image/jpeg';
        }
      case 'video':
        switch (ext) {
          case '.mp4': return 'video/mp4';
          case '.webm': return 'video/webm';
          case '.avi': return 'video/avi';
          case '.mov': return 'video/quicktime';
          case '.m3u8': return 'application/vnd.apple.mpegurl';
          case '.ts': return 'video/mp2t';
          default: return 'video/mp4';
        }
      case 'audio':
        switch (ext) {
          case '.mp3': return 'audio/mpeg';
          case '.wav': return 'audio/wav';
          case '.ogg': return 'audio/ogg';
          case '.aac': return 'audio/aac';
          case '.m4a': return 'audio/mp4';
          default: return 'audio/mpeg';
        }
      default:
        return 'application/octet-stream';
    }
  }
}
