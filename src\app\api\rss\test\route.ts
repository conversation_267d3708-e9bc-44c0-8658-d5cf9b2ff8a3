import { NextResponse } from 'next/server';
import { RSSFetcher } from '@/lib/server/rss-parser';

export async function POST(request: Request) {
  try {
    const { url, fieldMappings } = await request.json();
    
    if (!url) {
      return NextResponse.json({ success: false, error: 'URL参数缺失' }, { status: 400 });
    }

    const rssFetcher = new RSSFetcher();
    const result = await rssFetcher.testRSSSource(url, fieldMappings);
    
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
