const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addFsrsFields() {
  try {
    console.log('=== 步骤 1: 添加 FSRS 字段（保留所有旧字段）===\n');

    // 检查当前表结构
    console.log('1. 检查当前表结构...');
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(user_learning_records)`;
    const existingFields = tableInfo.map(field => field.name);
    console.log(`当前字段数量: ${existingFields.length}`);

    // 定义要添加的 FSRS 字段
    const fsrsFields = [
      { name: 'fsrs_due', type: 'DATETIME', description: '下次复习时间' },
      { name: 'fsrs_stability', type: 'REAL', description: '记忆稳定性' },
      { name: 'fsrs_difficulty', type: 'REAL', description: '内容难度' },
      { name: 'fsrs_elapsed_days', type: 'INTEGER', description: '已过天数' },
      { name: 'fsrs_scheduled_days', type: 'INTEGER', description: '计划间隔天数' },
      { name: 'fsrs_learning_steps', type: 'INTEGER', description: '学习步骤' },
      { name: 'fsrs_reps', type: 'INTEGER', default: '0', description: '复习次数' },
      { name: 'fsrs_lapses', type: 'INTEGER', default: '0', description: '遗忘次数' },
      { name: 'fsrs_state', type: 'TEXT', default: "'New'", description: '卡片状态' },
      { name: 'fsrs_last_review', type: 'DATETIME', description: '最后复习时间' }
    ];

    console.log('\n2. 检查需要添加的字段...');
    const fieldsToAdd = fsrsFields.filter(field => !existingFields.includes(field.name));
    
    if (fieldsToAdd.length === 0) {
      console.log('✅ 所有 FSRS 字段已存在，无需添加');
      return;
    }

    console.log(`需要添加 ${fieldsToAdd.length} 个字段:`);
    fieldsToAdd.forEach(field => {
      console.log(`  - ${field.name}: ${field.type} (${field.description})`);
    });

    console.log('\n3. 开始添加字段...');
    
    for (const field of fieldsToAdd) {
      try {
        const sql = field.default 
          ? `ALTER TABLE user_learning_records ADD COLUMN ${field.name} ${field.type} DEFAULT ${field.default}`
          : `ALTER TABLE user_learning_records ADD COLUMN ${field.name} ${field.type}`;
        
        console.log(`添加字段: ${field.name}...`);
        await prisma.$executeRawUnsafe(sql);
        console.log(`✅ ${field.name} 添加成功`);
      } catch (error) {
        console.error(`❌ 添加字段 ${field.name} 失败:`, error.message);
        throw error;
      }
    }

    console.log('\n4. 验证字段添加结果...');
    const newTableInfo = await prisma.$queryRaw`PRAGMA table_info(user_learning_records)`;
    const newFields = newTableInfo.map(field => field.name);
    
    console.log(`更新后字段数量: ${newFields.length}`);
    
    // 检查所有 FSRS 字段是否都存在
    const missingFields = fsrsFields.filter(field => !newFields.includes(field.name));
    if (missingFields.length === 0) {
      console.log('✅ 所有 FSRS 字段添加成功');
    } else {
      console.log('❌ 以下字段添加失败:');
      missingFields.forEach(field => console.log(`  - ${field.name}`));
      throw new Error('字段添加不完整');
    }

    console.log('\n=== 步骤 1 完成：FSRS 字段添加成功 ===');
    console.log('✅ 所有旧字段保持不变');
    console.log('✅ 所有现有数据完整保留');

  } catch (error) {
    console.error('添加 FSRS 字段失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

addFsrsFields();
