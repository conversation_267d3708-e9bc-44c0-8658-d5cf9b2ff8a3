# 抓取管理系统功能说明

## 1. 功能概述

### 1.1 主要功能
抓取管理系统是数据采集和处理的控制中心，负责管理新闻抓取任务、监控系统状态、控制AI处理队列，并提供数据统计和管理功能。

### 1.2 核心特性
- **抓取任务控制**: 启动、停止、监控抓取任务
- **实时进度监控**: 显示抓取进度和详细日志
- **AI处理队列管理**: 控制AI内容分析任务
- **数据库统计**: 实时显示数据量和存储状态
- **历史记录管理**: 查看抓取会话历史和统计
- **系统监控**: 服务状态和性能指标监控
- **数据管理**: 导出、清理、备份数据

### 1.3 适用场景
- 定期执行新闻数据抓取
- 监控系统运行状态
- 管理AI处理任务队列
- 分析数据采集效果
- 维护系统数据完整性

## 2. 使用指南

### 2.1 启动抓取任务
1. 进入抓取管理页面
2. 检查RSS源配置状态
3. 点击"开始抓取"按钮
4. 监控抓取进度和日志
5. 等待任务完成或手动停止

### 2.2 监控抓取进度
1. 查看实时进度条和统计数据
2. 观察日志输出了解详细过程
3. 检查错误信息和警告
4. 分析抓取效率和成功率

### 2.3 管理AI处理队列
1. 查看AI处理队列状态
2. 启动或停止AI处理器
3. 监控处理进度和速度
4. 清理失败的处理任务

### 2.4 数据库管理
1. 查看数据库统计信息
2. 导出数据到文件
3. 清理过期或无效数据
4. 备份重要数据

## 3. 界面详解

### 3.1 抓取管理主页面 (`/scraper`)

#### 页面布局
- **顶部**: 控制按钮和状态指示器
- **左侧**: 数据库统计和系统状态
- **中部**: 抓取进度和日志显示
- **右侧**: AI处理队列和历史记录

#### 控制按钮区域
| 按钮名称 | 功能 | 状态依赖 | 说明 |
|----------|------|----------|------|
| 开始抓取 | 启动新的抓取任务 | 未运行时可用 | 创建新的抓取会话 |
| 停止抓取 | 终止当前抓取任务 | 运行中时可用 | 优雅停止当前任务 |
| 强制停止 | 立即终止抓取任务 | 运行中时可用 | 强制结束所有进程 |
| 刷新状态 | 更新页面数据 | 始终可用 | 手动刷新统计信息 |

#### 系统状态指示器
| 指示器 | 状态值 | 颜色 | 说明 |
|--------|--------|------|------|
| 抓取状态 | 运行中/已停止 | 绿色/灰色 | 当前抓取任务状态 |
| AI处理状态 | 处理中/空闲 | 蓝色/灰色 | AI队列处理状态 |
| 数据库状态 | 正常/异常 | 绿色/红色 | 数据库连接状态 |
| 系统负载 | 低/中/高 | 绿色/黄色/红色 | 系统资源使用情况 |

### 3.2 数据库统计面板

#### 基础统计信息
| 统计项 | 数据源 | 计算方式 | 说明 |
|--------|--------|----------|------|
| 文章总数 | articles表 | COUNT(*) | 数据库中的文章总数 |
| 词汇总数 | vocabulary表 | COUNT(*) | 提取的词汇总数 |
| 语法点数量 | grammar_points表 | COUNT(*) | 识别的语法点数量 |
| 用户数量 | users表 | COUNT(*) | 注册用户总数 |
| 存储使用 | 文件系统 | 磁盘空间统计 | 媒体文件占用空间 |

#### 媒体文件统计
| 媒体类型 | 统计字段 | 显示格式 | 说明 |
|----------|----------|----------|------|
| 图片文件 | featured_image_path | 数量 + 大小 | 新闻配图统计 |
| 音频文件 | audio_path | 数量 + 大小 | 音频新闻统计 |
| 视频文件 | video_path | 数量 + 大小 | 视频新闻统计 |
| 总计 | 所有媒体文件 | 总大小 | 存储空间使用 |

### 3.3 抓取进度监控

#### 进度显示区域
| 显示项 | 数据来源 | 更新频率 | 说明 |
|--------|----------|----------|------|
| 总体进度 | 抓取会话统计 | 实时 | 整体任务完成度 |
| 当前源 | 正在处理的RSS源 | 实时 | 当前抓取的数据源 |
| 已处理源 | 完成的RSS源数量 | 实时 | 已完成的源统计 |
| 成功文章 | 成功抓取的文章数 | 实时 | 有效文章数量 |
| 失败文章 | 抓取失败的文章数 | 实时 | 错误文章统计 |
| 预计剩余时间 | 基于当前速度计算 | 每分钟 | 任务完成预估 |

#### 实时日志显示
| 日志级别 | 颜色标识 | 数据库字段 | 说明 |
|----------|----------|------------|------|
| INFO | 蓝色 | scraping_logs.level | 一般信息 |
| SUCCESS | 绿色 | scraping_logs.level | 成功操作 |
| WARNING | 黄色 | scraping_logs.level | 警告信息 |
| ERROR | 红色 | scraping_logs.level | 错误信息 |

#### 日志内容格式
```
[时间戳] [级别] [RSS源] 消息内容
[2024-12-20 10:30:15] [INFO] [NHK科技] 开始抓取RSS源...
[2024-12-20 10:30:16] [SUCCESS] [NHK科技] 成功获取15篇文章
[2024-12-20 10:30:17] [WARNING] [NHK科技] 3篇文章缺少图片
[2024-12-20 10:30:18] [ERROR] [NHK科技] 文章URL无效: https://...
```

### 3.4 AI处理队列管理

#### 队列状态面板
| 状态项 | 数据源 | 说明 |
|--------|--------|------|
| 队列长度 | ai_processing_queue表 | 待处理任务数量 |
| 处理中任务 | status='processing' | 正在处理的任务 |
| 已完成任务 | status='completed' | 完成的任务数量 |
| 失败任务 | status='failed' | 处理失败的任务 |
| 处理速度 | 每分钟完成数 | 处理效率统计 |

#### AI处理控制
| 控制项 | 功能 | 说明 |
|--------|------|------|
| 启动处理器 | 开始AI任务处理 | 启动后台AI处理服务 |
| 停止处理器 | 停止AI任务处理 | 优雅停止处理服务 |
| 清空队列 | 清除所有待处理任务 | 重置处理队列 |
| 重试失败任务 | 重新处理失败的任务 | 错误恢复功能 |

### 3.5 历史记录查看

#### 抓取会话列表
| 显示列 | 数据库字段 | 格式 | 说明 |
|--------|------------|------|------|
| 会话ID | session_id | 短ID | 唯一标识符 |
| 开始时间 | start_time | 日期时间 | 任务开始时间 |
| 结束时间 | end_time | 日期时间 | 任务结束时间 |
| 持续时间 | 计算得出 | HH:MM:SS | 任务执行时长 |
| 处理源数 | 统计计算 | 数字 | 处理的RSS源数量 |
| 成功文章 | 统计计算 | 数字 | 成功抓取的文章 |
| 失败文章 | 统计计算 | 数字 | 抓取失败的文章 |
| 状态 | 根据时间判断 | 标签 | 完成/进行中/失败 |

#### 会话详情查看
- **点击会话**: 展开详细信息
- **日志查看**: 显示该会话的所有日志
- **统计图表**: 该会话的数据可视化
- **错误分析**: 失败原因和解决建议

## 4. 技术实现

### 4.1 核心代码文件

#### 抓取管理组件
- **文件**: `src/components/EnhancedScraperManager.tsx`
- **功能**: 抓取管理主界面
- **主要功能**:
  - 任务控制和状态监控
  - 实时数据更新
  - 日志显示和过滤

#### 抓取器核心
- **文件**: `src/lib/server/scraper.ts`
- **功能**: 新闻抓取引擎
- **主要功能**:
  - RSS源遍历和内容提取
  - 媒体文件下载
  - 错误处理和重试机制

#### 数据库管理服务
- **文件**: `src/lib/server/database.ts`
- **相关方法**:
  - `getDatabaseStatsWithMedia()`: 获取数据库统计
  - `getScrapingSessions()`: 获取抓取会话历史
  - `getScrapingProgress(sessionId)`: 获取抓取进度
  - `addLog()`: 添加日志记录

#### AI处理队列
- **文件**: `src/lib/server/ai-processor.ts`
- **功能**: AI任务队列管理
- **主要功能**:
  - 任务调度和执行
  - 并发控制
  - 错误恢复机制

### 4.2 数据库表结构

#### scraping_sessions表（抓取会话）
```sql
CREATE TABLE scraping_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT UNIQUE NOT NULL,
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  end_time DATETIME,
  status TEXT DEFAULT 'running', -- 'running', 'completed', 'failed', 'stopped'
  total_sources INTEGER DEFAULT 0,
  processed_sources INTEGER DEFAULT 0,
  total_articles INTEGER DEFAULT 0,
  successful_articles INTEGER DEFAULT 0,
  failed_articles INTEGER DEFAULT 0,
  error_message TEXT
);
```

#### scraping_logs表（抓取日志）
```sql
CREATE TABLE scraping_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT NOT NULL,
  level TEXT NOT NULL, -- 'INFO', 'SUCCESS', 'WARNING', 'ERROR'
  message TEXT NOT NULL,
  details TEXT, -- JSON格式的详细信息
  rss_source_id INTEGER,
  article_url TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources(id)
);
```

#### ai_processing_queue表（AI处理队列）
```sql
CREATE TABLE ai_processing_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  task_type TEXT NOT NULL, -- 'furigana', 'vocabulary', 'grammar', 'translation'
  status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  priority INTEGER DEFAULT 0,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  error_message TEXT,
  result_data TEXT, -- JSON格式的处理结果
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles(id)
);
```

### 4.3 抓取流程架构

#### 主抓取流程
```typescript
class EnhancedScraper {
  async scrapeAllActiveSources(): Promise<ScrapingResult> {
    const sessionId = this.generateSessionId();
    const activeSources = await this.getActiveSources();
    
    this.log(sessionId, 'INFO', `开始抓取任务，共${activeSources.length}个源`);
    
    const results = {
      totalSources: activeSources.length,
      processedSources: 0,
      totalArticles: 0,
      successfulArticles: 0,
      failedArticles: 0,
      aiScheduled: 0
    };
    
    for (const source of activeSources) {
      if (this.shouldStop) break;
      
      try {
        const sourceResult = await this.scrapeSource(source, sessionId);
        this.updateResults(results, sourceResult);
        results.processedSources++;
      } catch (error) {
        this.log(sessionId, 'ERROR', `源抓取失败: ${source.name}`, { error });
      }
    }
    
    this.log(sessionId, 'INFO', '抓取任务完成', results);
    return results;
  }
}
```

#### 单源抓取逻辑
```typescript
private async scrapeSource(source: RSSSource, sessionId: string): Promise<SourceResult> {
  this.log(sessionId, 'INFO', `开始抓取源: ${source.name}`);
  
  // 1. 获取RSS内容
  const rssItems = await this.fetchRSSItems(source);
  this.log(sessionId, 'SUCCESS', `获取到${rssItems.length}篇文章`);
  
  // 2. 限制文章数量
  const limitedItems = rssItems.slice(0, source.max_articles || 10);
  
  // 3. 处理每篇文章
  const results = { successful: 0, failed: 0, aiScheduled: 0 };
  
  for (const item of limitedItems) {
    try {
      const article = await this.processArticle(item, source, sessionId);
      if (article) {
        results.successful++;
        if (source.enable_ai_processing) {
          await this.scheduleAIProcessing(article.id);
          results.aiScheduled++;
        }
      }
    } catch (error) {
      results.failed++;
      this.log(sessionId, 'ERROR', `文章处理失败: ${item.title}`, { error });
    }
  }
  
  return results;
}
```

### 4.4 API接口

#### 启动抓取任务
- **路径**: `POST /api/scraping/start`
- **返回**: `{sessionId: string, message: string}`

#### 停止抓取任务
- **路径**: `POST /api/scraping/stop`
- **参数**: `{sessionId?: string}`
- **返回**: 停止结果

#### 获取抓取进度
- **路径**: `GET /api/scraping/progress/[sessionId]`
- **返回**: 详细的进度信息和统计

#### 获取数据库统计
- **路径**: `GET /api/database/stats`
- **返回**: 数据库统计信息

#### 获取抓取历史
- **路径**: `GET /api/scraping/sessions`
- **参数**: `{limit?, offset?}`
- **返回**: 历史会话列表

#### AI队列管理
- **路径**: `POST /api/ai/start` - 启动AI处理器
- **路径**: `POST /api/ai/stop` - 停止AI处理器
- **路径**: `DELETE /api/ai/queue/clear` - 清空队列

## 5. 配置说明

### 5.1 抓取配置

#### 并发控制
```javascript
const SCRAPING_CONFIG = {
  maxConcurrentSources: 3, // 最大并发源数量
  maxConcurrentArticles: 5, // 最大并发文章数量
  requestDelay: 1000, // 请求间隔（毫秒）
  retryAttempts: 3, // 重试次数
  timeout: 30000 // 请求超时（毫秒）
};
```

#### 媒体下载配置
```javascript
const MEDIA_CONFIG = {
  enableImageDownload: true,
  enableAudioDownload: true,
  enableVideoDownload: false, // 视频文件较大，默认禁用
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedImageTypes: ['jpg', 'jpeg', 'png', 'webp'],
  allowedAudioTypes: ['mp3', 'm4a', 'wav']
};
```

### 5.2 AI处理配置

#### 队列处理参数
```javascript
const AI_QUEUE_CONFIG = {
  batchSize: 5, // 批处理大小
  processingInterval: 5000, // 处理间隔（毫秒）
  maxRetries: 3, // 最大重试次数
  priorityLevels: ['high', 'normal', 'low']
};
```

## 6. 故障排除

### 6.1 常见问题

#### 抓取任务卡住不动
**症状**: 进度条停止更新，日志无新输出
**解决方案**:
1. 检查网络连接状态
2. 查看RSS源是否可访问
3. 重启抓取服务
4. 检查数据库连接

#### AI处理队列堆积
**症状**: 队列长度持续增长，处理速度缓慢
**解决方案**:
1. 检查AI API配额和限制
2. 调整批处理大小
3. 增加处理间隔时间
4. 清理失败任务

#### 媒体文件下载失败
**症状**: 图片或音频文件无法下载
**解决方案**:
1. 检查文件URL有效性
2. 确认存储目录权限
3. 检查文件大小限制
4. 验证网络访问权限

### 6.2 性能优化

#### 数据库优化
```sql
-- 创建索引提高查询性能
CREATE INDEX idx_scraping_logs_session_time 
ON scraping_logs(session_id, created_at);

CREATE INDEX idx_ai_queue_status_priority 
ON ai_processing_queue(status, priority, created_at);
```

#### 内存管理
```typescript
// 实现流式处理避免内存溢出
async function processLargeDataset(items: any[]) {
  const batchSize = 100;
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await processBatch(batch);
    
    // 强制垃圾回收
    if (global.gc) {
      global.gc();
    }
  }
}
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
