'use client';
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  BookO<PERSON>,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Zap,
  Award,
  Sparkles,
  BookText,
  Bot,
  AlertTriangle,
  CheckCircle,
  Star,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import ActivityHeatmap from '@/components/charts/ActivityHeatmap';
import StudyTrendChart from '@/components/charts/StudyTrendChart';
import {
  getLearningStatsAction,
  getUserProgressAction,
  getReviewTaskStatsAction,
  getLearningEfficiencyStatsAction,
  getStudyTrendDataAction,
  getActivityHeatmapDataAction
} from '@/app/actions';

interface EnhancedDashboardProps {
  userId: string;
}

interface LearningStats {
  activeDaysLast30: number;
  consecutiveDays: number;
  learnedVocabulary: number;
  masteredVocabulary: number;
  completedArticles: number;
  averageStudyTime: number;
}

interface ReviewTaskStats {
  today: number;
  tomorrow: number;
  thisWeek: number;
  thisMonth: number;
  overdue: number;
}

interface EfficiencyStats {
  reviewSuccessRate: number;
  averageRepetitions: number;
  progressEfficiency: number;
  retentionRate: number;
}

interface VocabularyProgress {
  total: number;
  mastered: number;
  learning: number;
  percentage: number;
}

interface GrammarProgress {
  total: number;
  mastered: number;
  learning: number;
  percentage: number;
}

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ userId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartPeriod, setChartPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [chartLoading, setChartLoading] = useState(false);
  
  // State for all dashboard data
  const [learningStats, setLearningStats] = useState<LearningStats | null>(null);
  const [reviewTasks, setReviewTasks] = useState<ReviewTaskStats | null>(null);
  const [efficiency, setEfficiency] = useState<EfficiencyStats | null>(null);
  const [vocabularyProgress, setVocabularyProgress] = useState<VocabularyProgress | null>(null);
  const [grammarProgress, setGrammarProgress] = useState<GrammarProgress | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);
  const [heatmapData, setHeatmapData] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, [userId]);

  useEffect(() => {
    if (userId) {
      loadChartData();
    }
  }, [chartPeriod, userId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResult, reviewResult, efficiencyResult, progressResult, heatmapResult] = await Promise.all([
        getLearningStatsAction(userId),
        getReviewTaskStatsAction(userId),
        getLearningEfficiencyStatsAction(userId),
        getUserProgressAction(userId),
        getActivityHeatmapDataAction(userId)
      ]);

      if (!statsResult.success) throw new Error(statsResult.error);
      if (!reviewResult.success) throw new Error(reviewResult.error);
      if (!efficiencyResult.success) throw new Error(efficiencyResult.error);
      if (!progressResult.success) throw new Error(progressResult.error);
      if (!heatmapResult.success) throw new Error(heatmapResult.error);

      setLearningStats(statsResult.data);
      setReviewTasks(reviewResult.data);
      setEfficiency(efficiencyResult.data);
      setVocabularyProgress(progressResult.data.vocabularyProgress || null);
      setGrammarProgress(progressResult.data.grammarProgress || null);
      setHeatmapData(heatmapResult.data);

    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadChartData = async () => {
    try {
      setChartLoading(true);
      const result = await getStudyTrendDataAction(userId, chartPeriod);
      if (result.success) {
        setChartData(result.data);
      }
    } catch (err) {
      console.error('Failed to load chart data:', err);
    } finally {
      setChartLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        <span className="ml-2 text-gray-600">加载学习统计中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertTriangle className="h-6 w-6 text-red-600 mr-2" />
          <h3 className="text-lg font-semibold text-red-800">加载失败</h3>
        </div>
        <p className="text-red-700 mt-2">{error}</p>
        <button
          onClick={loadDashboardData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          重试
        </button>
      </div>
    );
  }

  if (!learningStats || !reviewTasks || !efficiency) {
    return (
      <div className="text-center text-gray-500 py-8">
        暂无学习数据
      </div>
    );
  }

  const generateInsights = () => {
    const insights = [];
    
    if (reviewTasks.overdue > 0) {
      insights.push({
        type: 'warning' as const,
        message: `有${reviewTasks.overdue}个逾期复习任务，建议优先处理`,
        action: '去复习页面'
      });
    }
    
    if (efficiency.reviewSuccessRate >= 0.7) {
      insights.push({
        type: 'success' as const,
        message: `复习成功率${Math.round(efficiency.reviewSuccessRate * 100)}%，继续保持！`,
        action: '查看详情'
      });
    } else if (efficiency.reviewSuccessRate < 0.5) {
      insights.push({
        type: 'warning' as const,
        message: '复习成功率较低，建议增加复习频率',
        action: '调整学习计划'
      });
    }
    
    if (learningStats.consecutiveDays >= 3) {
      insights.push({
        type: 'success' as const,
        message: `连续学习${learningStats.consecutiveDays}天，再坚持${7 - learningStats.consecutiveDays}天可获得成就`,
        action: '设置提醒'
      });
    } else {
      insights.push({
        type: 'info' as const,
        message: '保持连续学习可以提高记忆效果',
        action: '设置学习提醒'
      });
    }

    return insights.slice(0, 3); // 最多显示3个建议
  };

  const mockAchievements = [
    { id: 1, name: '初学者', icon: '🌱', earned: learningStats.learnedVocabulary > 0 },
    { id: 2, name: '词汇达人', icon: '📚', earned: learningStats.learnedVocabulary >= 50 },
    { id: 3, name: '坚持不懈', icon: '🔥', earned: learningStats.consecutiveDays >= 7 },
    { id: 4, name: '词汇征服者', icon: '🏆', earned: (vocabularyProgress?.percentage || 0) >= 80 },
    { id: 5, name: '复习专家', icon: '⚡', earned: efficiency.reviewSuccessRate >= 0.8 },
    { id: 6, name: '学习狂人', icon: '🚀', earned: learningStats.activeDaysLast30 >= 20 }
  ];

  const insights = generateInsights();

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">学习统计</h2>
          <p className="text-gray-600 mt-1">追踪你的日语学习进度和效率</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={loadDashboardData}
            className="px-4 py-2 text-sm bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors"
          >
            刷新数据
          </button>
        </div>
      </div>

      {/* 核心统计指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">连续学习</p>
              <p className="text-2xl font-bold text-gray-900">{learningStats.consecutiveDays} 天</p>
            </div>
            <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Flame className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">今日复习任务</p>
              <p className="text-2xl font-bold text-gray-900">{reviewTasks.today}</p>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">复习成功率</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(efficiency.reviewSuccessRate * 100)}%</p>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">平均学习时长</p>
              <p className="text-2xl font-bold text-gray-900">{learningStats.averageStudyTime} 分钟</p>
            </div>
            <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 复习任务概览 */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">复习任务概览</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{reviewTasks.today}</div>
            <div className="text-sm text-gray-600">今日</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{reviewTasks.tomorrow}</div>
            <div className="text-sm text-gray-600">明日</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{reviewTasks.thisWeek}</div>
            <div className="text-sm text-gray-600">本周</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-600">{reviewTasks.thisMonth}</div>
            <div className="text-sm text-gray-600">本月</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{reviewTasks.overdue}</div>
            <div className="text-sm text-gray-600">逾期</div>
          </div>
        </div>
      </div>

      {/* 学习活动热力图 */}
      <ActivityHeatmap data={heatmapData} loading={loading} />

      {/* 学习/复习趋势图 */}
      <StudyTrendChart
        data={chartData}
        period={chartPeriod}
        onPeriodChange={setChartPeriod}
        loading={chartLoading}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 词汇学习进度 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6 pb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">词汇学习进度</h3>
            {vocabularyProgress ? (
              <div className="space-y-4">
                {/* 掌握进度 */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">已掌握词汇</span>
                    <span className="text-gray-900 font-medium">
                      {vocabularyProgress.mastered}/{vocabularyProgress.total} ({vocabularyProgress.percentage}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-indigo-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${vocabularyProgress.percentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* 学习进度 */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">已学习词汇</span>
                    <span className="text-gray-900 font-medium">
                      {vocabularyProgress.learning}/{vocabularyProgress.total} ({Math.round((vocabularyProgress.learning / vocabularyProgress.total) * 100)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-400 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${Math.round((vocabularyProgress.learning / vocabularyProgress.total) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">暂无数据</div>
            )}
          </div>
        </div>

        {/* 语法学习进度 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6 pb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">语法学习进度</h3>
            {grammarProgress ? (
              <div className="space-y-4">
                {/* 掌握进度 */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">已掌握语法</span>
                    <span className="text-gray-900 font-medium">
                      {grammarProgress.mastered}/{grammarProgress.total} ({grammarProgress.percentage}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-emerald-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${grammarProgress.percentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* 学习进度 */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">已学习语法</span>
                    <span className="text-gray-900 font-medium">
                      {grammarProgress.learning}/{grammarProgress.total} ({Math.round((grammarProgress.learning / grammarProgress.total) * 100)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-400 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${Math.round((grammarProgress.learning / grammarProgress.total) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">暂无数据</div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 学习效率分析 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">学习效率分析</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">复习成功率</span>
                <span className="text-lg font-semibold text-green-600">
                  {Math.round(efficiency.reviewSuccessRate * 100)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">平均重复次数</span>
                <span className="text-lg font-semibold text-blue-600">
                  {efficiency.averageRepetitions}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">每日新学词汇</span>
                <span className="text-lg font-semibold text-purple-600">
                  {efficiency.progressEfficiency}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">记忆保持率</span>
                <span className="text-lg font-semibold text-indigo-600">
                  {Math.round(efficiency.retentionRate * 100)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 智能学习建议 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">智能学习建议</h3>
          <div className="space-y-3">
            {insights.map((insight, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                  insight.type === 'warning' ? 'bg-yellow-100' :
                  insight.type === 'success' ? 'bg-green-100' : 'bg-blue-100'
                }`}>
                  {insight.type === 'warning' ? (
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  ) : insight.type === 'success' ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <BarChart3 className="w-4 h-4 text-blue-600" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-700">{insight.message}</p>
                  <button className="text-xs text-indigo-600 hover:text-indigo-800 mt-1">
                    {insight.action} →
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 成就系统 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">成就徽章</h3>
          <div className="grid grid-cols-3 gap-4">
            {mockAchievements.map(achievement => (
              <div key={achievement.id} className="text-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 text-xl ${
                  achievement.earned ? 'bg-yellow-100' : 'bg-gray-100'
                }`}>
                  {achievement.icon}
                </div>
                <p className={`text-xs ${achievement.earned ? 'text-gray-900' : 'text-gray-400'}`}>
                  {achievement.name}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 快速操作面板 */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Link href="/study" className="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <Sparkles className="h-8 w-8 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-blue-700">开始复习</span>
            <span className="text-xs text-blue-600">{reviewTasks.today} 个任务</span>
          </Link>
          <Link href="/vocabulary" className="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <BookOpen className="h-8 w-8 text-green-600 mb-2" />
            <span className="text-sm font-medium text-green-700">生词本</span>
            <span className="text-xs text-green-600">{learningStats.learnedVocabulary} 个词汇</span>
          </Link>
          <Link href="/grammar" className="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <BookText className="h-8 w-8 text-purple-600 mb-2" />
            <span className="text-sm font-medium text-purple-700">语法本</span>
            <span className="text-xs text-purple-600">语法点</span>
          </Link>
          <Link href="/ai-tutor" className="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
            <Bot className="h-8 w-8 text-orange-600 mb-2" />
            <span className="text-sm font-medium text-orange-700">AI助教</span>
            <span className="text-xs text-orange-600">智能辅导</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default EnhancedDashboard;
