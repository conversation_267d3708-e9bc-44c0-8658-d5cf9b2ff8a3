
'use client';
import React, { useState, useEffect, useTransition } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  BookText,
  Search,
  Star,
  Trash2,
  Volume2,
  Calendar,
  Loader,
  RefreshCw,
} from 'lucide-react';
import GrammarCard from './GrammarCard';
import { useTTS } from '@/hooks/useTTS';

interface GrammarBankProps {
  initialGrammarPoints: any | null; // 现在包含分页信息的对象
  initialStats: any | null;
}

const GrammarBank: React.FC<GrammarBankProps> = ({ initialGrammarPoints, initialStats }) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedLevel, setSelectedLevel] = useState(searchParams.get('level') || 'all');

  const [grammarData, setGrammarData] = useState(initialGrammarPoints);
  const [stats, setStats] = useState(initialStats);
  const [isPending, startTransition] = useTransition();

  // TTS 状态管理
  const { isPlaying, speak, cancel } = useTTS();
  const [playingGrammarId, setPlayingGrammarId] = useState<string | null>(null);
  const [playingContentId, setPlayingContentId] = useState<string | null>(null);

  // 从分页数据中提取语法点列表
  const grammarPoints = grammarData?.grammarPoints || [];
  const totalCount = grammarData?.totalCount || 0;
  const currentPage = grammarData?.currentPage || 1;
  const totalPages = grammarData?.totalPages || 1;

  // TTS 处理函数
  const handleGrammarSpeak = (grammar: any) => {
    const grammarId = grammar.pattern;

    if (playingGrammarId === grammarId && isPlaying) {
      cancel();
      setPlayingGrammarId(null);
    } else {
      const textToSpeak = grammar.pattern;
      setPlayingGrammarId(grammarId);
      speak(textToSpeak, 'ja-JP');

      setTimeout(() => {
        setPlayingGrammarId(null);
      }, 3000);
    }
  };

  const handleContentSpeak = (text: string, contentId: string, language: 'ja-JP' | 'en-US' = 'ja-JP') => {
    if (playingContentId === contentId && isPlaying) {
      cancel();
      setPlayingContentId(null);
    } else {
      setPlayingContentId(contentId);
      speak(text, language);

      setTimeout(() => {
        setPlayingContentId(null);
      }, 3000);
    }
  };

  useEffect(() => {
    setGrammarData(initialGrammarPoints);
    setStats(initialStats);
  }, [initialGrammarPoints, initialStats]);

  const handleFilterChange = () => {
    const params = new URLSearchParams(window.location.search);
    if (searchTerm) params.set('search', searchTerm);
    else params.delete('search');
    if (selectedLevel !== 'all') params.set('level', selectedLevel);
    else params.delete('level');
    router.push(`/grammar?${params.toString()}`);
  };

  useEffect(() => {
    const handler = setTimeout(() => {
        handleFilterChange();
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm, selectedLevel]);






  if (!grammarPoints || !stats) {
    return (
        <div className="flex h-full items-center justify-center py-12">
            <div className="text-center">
                <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
                <p className="text-gray-600">正在准备语法库...</p>
            </div>
        </div>
    );
  }

  return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">我的语法本</h2>
            <p className="text-gray-600 mt-1">管理AI提取的语法点，巩固语法知识</p>
          </div>
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => router.refresh()}
              disabled={isPending}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all"
            >
              {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              <span>刷新</span>
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-gray-900">{stats.total || 0}</div>
            <div className="text-sm text-gray-600">总语法</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-blue-600">{stats.learning || 0}</div>
            <div className="text-sm text-gray-600">学习中</div>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="text-2xl font-bold text-orange-600">{stats.pending || 0}</div>
            <div className="text-sm text-gray-600">待学习</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="搜索语法..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <select 
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
            >
              <option value="all">全部级别</option>
              <option value="N5">N5</option>
              <option value="N4">N4</option>
              <option value="N3">N3</option>
              <option value="N2">N2</option>
              <option value="N1">N1</option>
            </select>
            

          </div>
        </div>

        {/* Grammar List */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="divide-y divide-gray-200">
            {grammarPoints.map((gp: any) => {
              // 解析例句
              let examples: string[] = [];
              try {
                if (gp.examples) {
                  examples = JSON.parse(gp.examples);
                }
              } catch (e) {
                console.warn('Failed to parse examples:', e);
              }

              // 构造统一的语法对象
              const grammarData = {
                ...gp,
                examples: examples
              };

              return (
                <div key={gp.id} className="relative">
                  <GrammarCard
                    grammar={grammarData}
                    onGrammarSpeak={handleGrammarSpeak}
                    onContentSpeak={handleContentSpeak}
                    playingGrammarId={playingGrammarId}
                    playingContentId={playingContentId}
                    isPlaying={isPlaying}
                    showActions={false}
                    compact={false}
                    className=""
                  />

                  {/* 额外的语法本特有信息 */}
                  <div className="px-6 pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>添加于 {gp.addedDate}</span>
                        </span>
                        {gp.article_count > 0 && (
                          <span>出现在 {gp.article_count} 篇文章中</span>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleGrammarSpeak(gp)}
                          className={`p-2 rounded-lg transition-all ${
                            playingGrammarId === gp.pattern && isPlaying
                              ? 'text-indigo-600 bg-indigo-50'
                              : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                          }`}
                        >
                          <Volume2 className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-all">
                          <Star className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {grammarPoints.length === 0 && !isPending && (
          <div className="text-center py-12">
            <BookText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到语法点</h3>
            <p className="text-gray-600">
              尝试调整搜索条件或等待AI分析更多文章。
            </p>
          </div>
        )}

        {/* 分页导航 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between bg-white px-6 py-4 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-700">
              显示第 {((currentPage - 1) * Math.ceil(totalCount / totalPages)) + 1} - {Math.min(currentPage * Math.ceil(totalCount / totalPages), totalCount)} 项，共 {totalCount} 项
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', (currentPage - 1).toString());
                  router.push(`/grammar?${params.toString()}`);
                }}
                disabled={currentPage <= 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => {
                        const params = new URLSearchParams(window.location.search);
                        params.set('page', pageNum.toString());
                        router.push(`/grammar?${params.toString()}`);
                      }}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        pageNum === currentPage
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', (currentPage + 1).toString());
                  router.push(`/grammar?${params.toString()}`);
                }}
                disabled={currentPage >= totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
  );
};

export default GrammarBank;
