'use client';
import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  BookOpen, 
  RotateCcw, 
  Sparkles, 
  Calendar,
  TrendingUp,
  Clock,
  Target,
  ArrowRight,
  Loader2,
  Book,
  FileText
} from 'lucide-react';
import { getStudyStatsAction } from '@/app/actions';

interface StudyModeProps {
  onModeSelect: (mode: 'learn' | 'review', contentType: 'vocabulary' | 'grammar' | 'mixed') => void;
}

interface StudyStats {
  // 顶部统计卡片数据（总数）
  totalStats: {
    newVocabulary: number;
    newGrammar: number;
    reviewVocabulary: number;
    reviewGrammar: number;
  };
  // 每日学习量设置（学习区域显示）
  dailyTargets: {
    vocabularyTarget: number;
    grammarTarget: number;
    mixedTarget: number;
  };
  loading: boolean;
}

const EnhancedStudyModeSelector: React.FC<StudyModeProps> = ({ onModeSelect }) => {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<StudyStats>({
    totalStats: {
      newVocabulary: 0,
      newGrammar: 0,
      reviewVocabulary: 0,
      reviewGrammar: 0
    },
    dailyTargets: {
      vocabularyTarget: 0,
      grammarTarget: 0,
      mixedTarget: 0
    },
    loading: true
  });

  useEffect(() => {
    const fetchStats = async () => {
      // 在开发模式下，如果启用了自动登录，使用默认用户ID
      const userId = session?.user?.id ||
        (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' ? '1' : null);

      if (status === 'loading' || !userId) {
        setStats(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        // 使用专门的统计API，不受今天学习限制影响
        const result = await getStudyStatsAction(userId);

        if (result.success && result.data) {
          const { totalStats, dailyTargets } = result.data;

          setStats({
            totalStats,
            dailyTargets,
            loading: false
          });
        } else {
          setStats({
            totalStats: {
              newVocabulary: 0,
              newGrammar: 0,
              reviewVocabulary: 0,
              reviewGrammar: 0
            },
            dailyTargets: {
              vocabularyTarget: 0,
              grammarTarget: 0,
              mixedTarget: 0
            },
            loading: false
          });
        }
      } catch (error) {
        console.error('Failed to fetch study stats:', error);
        setStats({
          totalStats: {
            newVocabulary: 0,
            newGrammar: 0,
            reviewVocabulary: 0,
            reviewGrammar: 0
          },
          dailyTargets: {
            vocabularyTarget: 0,
            grammarTarget: 0,
            mixedTarget: 0
          },
          loading: false
        });
      }
    };

    fetchStats();
  }, [session, status]);

  if (stats.loading) {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载学习数据...</p>
        </div>
      </div>
    );
  }

  const totalNew = stats.totalStats.newVocabulary + stats.totalStats.newGrammar;
  const totalReview = stats.totalStats.reviewVocabulary + stats.totalStats.reviewGrammar;

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">选择学习模式</h1>
        <p className="text-gray-600">选择学习模式和内容类型，开始您的日语学习之旅</p>
      </div>

      {/* 学习统计概览 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Book className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-blue-600">新词汇</p>
              <p className="text-2xl font-bold text-blue-900">{stats.totalStats.newVocabulary}</p>
            </div>
          </div>
        </div>

        <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-indigo-600">新语法</p>
              <p className="text-2xl font-bold text-indigo-900">{stats.totalStats.newGrammar}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Book className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">复习词汇</p>
              <p className="text-2xl font-bold text-green-900">{stats.totalStats.reviewVocabulary}</p>
            </div>
          </div>
        </div>

        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-emerald-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-emerald-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-emerald-600">复习语法</p>
              <p className="text-2xl font-bold text-emerald-900">{stats.totalStats.reviewGrammar}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 学习模式选择 */}
      <div className="space-y-8">
        {/* 学习新内容 */}
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">学习新内容</h3>
                <p className="text-sm text-gray-500">学习从未接触过的词汇和语法</p>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 学习词汇 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <Book className="h-5 w-5 text-blue-600" />
                  <h4 className="font-medium text-gray-900">学习词汇</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {stats.dailyTargets.vocabularyTarget} 个新词汇</p>
                </div>
                <button
                  onClick={() => onModeSelect('learn', 'vocabulary')}
                  disabled={stats.dailyTargets.vocabularyTarget === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    stats.dailyTargets.vocabularyTarget > 0
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {stats.dailyTargets.vocabularyTarget > 0 ? '开始学习' : '暂无新词汇'}
                </button>
              </div>

              {/* 学习语法 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <FileText className="h-5 w-5 text-indigo-600" />
                  <h4 className="font-medium text-gray-900">学习语法</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {stats.dailyTargets.grammarTarget} 个新语法</p>
                </div>
                <button
                  onClick={() => onModeSelect('learn', 'grammar')}
                  disabled={stats.dailyTargets.grammarTarget === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    stats.dailyTargets.grammarTarget > 0
                      ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {stats.dailyTargets.grammarTarget > 0 ? '开始学习' : '暂无新语法'}
                </button>
              </div>

              {/* 混合学习 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  <h4 className="font-medium text-gray-900">混合学习</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {stats.dailyTargets.mixedTarget} 个新内容</p>
                </div>
                <button
                  onClick={() => onModeSelect('learn', 'mixed')}
                  disabled={stats.dailyTargets.mixedTarget === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    stats.dailyTargets.mixedTarget > 0
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {stats.dailyTargets.mixedTarget > 0 ? '开始学习' : '暂无新内容'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 复习已学内容 */}
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <RotateCcw className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">复习已学内容</h3>
                <p className="text-sm text-gray-500">巩固今天需要复习的内容</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 复习词汇 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <Book className="h-5 w-5 text-green-600" />
                  <h4 className="font-medium text-gray-900">复习词汇</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {stats.totalStats.reviewVocabulary} 个到期词汇</p>
                </div>
                <button
                  onClick={() => onModeSelect('review', 'vocabulary')}
                  disabled={stats.totalStats.reviewVocabulary === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    stats.totalStats.reviewVocabulary > 0
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {stats.totalStats.reviewVocabulary > 0 ? '开始复习' : '暂无复习任务'}
                </button>
              </div>

              {/* 复习语法 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-emerald-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <FileText className="h-5 w-5 text-emerald-600" />
                  <h4 className="font-medium text-gray-900">复习语法</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {stats.totalStats.reviewGrammar} 个到期语法</p>
                </div>
                <button
                  onClick={() => onModeSelect('review', 'grammar')}
                  disabled={stats.totalStats.reviewGrammar === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    stats.totalStats.reviewGrammar > 0
                      ? 'bg-emerald-600 text-white hover:bg-emerald-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {stats.totalStats.reviewGrammar > 0 ? '开始复习' : '暂无复习任务'}
                </button>
              </div>

              {/* 混合复习 */}
              <div className="border border-gray-200 rounded-lg p-4 hover:border-teal-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <Calendar className="h-5 w-5 text-teal-600" />
                  <h4 className="font-medium text-gray-900">混合复习</h4>
                </div>
                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">• {totalReview} 个到期内容</p>
                </div>
                <button
                  onClick={() => onModeSelect('review', 'mixed')}
                  disabled={totalReview === 0}
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                    totalReview > 0
                      ? 'bg-teal-600 text-white hover:bg-teal-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {totalReview > 0 ? '开始复习' : '暂无复习任务'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      {(totalNew > 0 || totalReview > 0) && (
        <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="h-6 w-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <Target className="h-4 w-4 text-amber-600" />
            </div>
            <div>
              <h4 className="font-medium text-amber-800 mb-1">学习建议</h4>
              <div className="text-sm text-amber-700 space-y-1">
                {totalReview > 0 && (
                  <p>• 建议优先完成复习任务，巩固已学内容</p>
                )}
                {stats.totalStats.newVocabulary > 0 && (
                  <p>• 词汇学习：建议每次学习5-10个新词汇</p>
                )}
                {stats.totalStats.newGrammar > 0 && (
                  <p>• 语法学习：建议每次学习2-5个新语法点</p>
                )}
                <p>• 学习过程中遇到困难的内容会自动安排更频繁的复习</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 空状态 */}
      {totalNew === 0 && totalReview === 0 && (
        <div className="text-center py-12">
          <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无学习任务</h3>
          <p className="text-gray-600 max-w-md mx-auto mb-6">
            您的学习队列是空的。请先去阅读新闻，并将遇到的重点词汇或语法点加入学习计划。
          </p>
          <button
            onClick={() => window.location.href = '/'}
            className="inline-flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            <BookOpen className="h-5 w-5" />
            <span>去新闻列表看看</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default EnhancedStudyModeSelector;
