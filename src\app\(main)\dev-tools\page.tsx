'use client';

import React from 'react';
import Link from 'next/link';
import {
  TestTube,
  MessageSquare,
  ArrowLeft
} from 'lucide-react';

interface DevToolItem {
  id: string;
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<any>;
  category: 'prototype' | 'testing' | 'debug';
  status: 'stable' | 'experimental' | 'deprecated';
}

const DevToolsPage: React.FC = () => {
  const devTools: DevToolItem[] = [
    {
      id: 'prompt-test',
      title: '提示词测试',
      description: 'AI提示词的测试和优化工具',
      href: '/admin/prompt-test',
      icon: MessageSquare,
      category: 'testing',
      status: 'stable'
    }
  ];

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'prototype': return '原型设计';
      case 'testing': return '功能测试';
      case 'debug': return '调试工具';
      default: return category;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'stable': return 'bg-green-100 text-green-800';
      case 'experimental': return 'bg-yellow-100 text-yellow-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'stable': return '稳定';
      case 'experimental': return '实验性';
      case 'deprecated': return '已废弃';
      default: return status;
    }
  };

  const groupedTools = devTools.reduce((acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  }, {} as Record<string, DevToolItem[]>);

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">开发工具</h1>
          <p className="text-gray-600 mt-1">
            AI提示词测试和优化工具
          </p>
        </div>
        <Link
          href="/"
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>返回主页</span>
        </Link>
      </div>

      {/* 工具分类展示 */}
      <div className="space-y-8">
        {Object.entries(groupedTools).map(([category, tools]) => (
          <div key={category} className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-2 w-2 bg-indigo-600 rounded-full"></div>
              <h2 className="text-lg font-semibold text-gray-900">
                {getCategoryName(category)}
              </h2>
              <span className="text-sm text-gray-500">({tools.length} 个工具)</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <Link
                    key={tool.id}
                    href={tool.href}
                    className="group block p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <Icon className="h-6 w-6 text-indigo-600 group-hover:text-indigo-700" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="text-sm font-medium text-gray-900 group-hover:text-indigo-700">
                            {tool.title}
                          </h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(tool.status)}`}>
                            {getStatusText(tool.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {tool.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <TestTube className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-blue-900 mb-2">使用说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>提示词测试</strong>：用于测试和验证AI分析功能的稳定性和一致性</li>
              <li>• <strong>批量测试</strong>：支持连续执行多次测试，检测输出的稳定性</li>
              <li>• <strong>结果分析</strong>：自动分析测试结果，识别格式和一致性问题</li>
              <li>• <strong>权限要求</strong>：仅管理员可访问此功能</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DevToolsPage;
