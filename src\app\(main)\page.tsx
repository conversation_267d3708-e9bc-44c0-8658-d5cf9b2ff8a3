import { Suspense } from 'react';
import NewsReader from '@/components/NewsReader';
import { Loader } from 'lucide-react';
import { dbManager } from '@/lib/server/database';
import { getPageSize } from '@/lib/userSettings';
import { getSession } from '@/lib/auth';

interface NewsReaderPageProps {
  searchParams: Promise<{
    search?: string;
    page?: string;
    timeRange?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: string;
    unreadOnly?: string;
  }>;
}

// This is the new Server Component to fetch data
async function NewsFeed({ searchParams }: NewsReaderPageProps) {
  const params = await searchParams;
  const search = params.search || '';
  const page = parseInt(params.page || '1', 10);
  const timeRange = params.timeRange || '';
  const category = params.category || '';
  const sortBy = params.sortBy || '';
  const sortOrder = params.sortOrder || '';
  const unreadOnly = params.unreadOnly === 'true';

  // Get user session and use user-specific page size
  const session = await getSession();
  let limit = 10; // default
  let userId: number | undefined;

  if (session?.user?.id) {
    userId = parseInt(session.user.id);
    try {
      limit = await getPageSize(session.user.id.toString(), 'news');
    } catch (error) {
      console.warn('Failed to get user page size, using default:', error);
      // Fallback to system setting if user setting fails
      const articlesPerPageSetting = await dbManager.getSystemSetting('articles_per_page');
      limit = articlesPerPageSetting ? parseInt(articlesPerPageSetting.value, 10) : 10;
    }
  } else {
    // For non-authenticated users, use system setting
    const articlesPerPageSetting = await dbManager.getSystemSetting('articles_per_page');
    limit = articlesPerPageSetting ? parseInt(articlesPerPageSetting.value, 10) : 10;
  }

  const { articles, totalCount } = await dbManager.getArticles({
    search,
    page,
    limit,
    timeRange,
    category,
    sortBy,
    sortOrder,
    userId,
    unreadOnly,
  });

  return (
    <NewsReader
      initialArticles={articles}
      totalCount={totalCount}
      currentPage={page}
      limit={limit}
    />
  );
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
        <p className="text-gray-600">正在加载新闻...</p>
      </div>
    </div>
  );
}

// The main page component now only passes the searchParams object down.
export default function NewsReaderPage({ searchParams }: NewsReaderPageProps) {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <NewsFeed searchParams={searchParams} />
    </Suspense>
  );
}
