import { NextRequest, NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// 获取WAL模式状态
export async function GET(request: NextRequest) {
  try {
    const walConfig = await dbManager.getWALConfiguration();
    
    return NextResponse.json({
      success: true,
      walConfig
    });
  } catch (error: any) {
    console.error('获取WAL状态失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// 设置WAL模式
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    let result;
    if (action === 'enable') {
      result = await dbManager.checkAndSetWALMode();
    } else if (action === 'optimize') {
      result = await dbManager.optimizeWALConfiguration();
    } else {
      return NextResponse.json({
        success: false,
        error: '无效的操作'
      }, { status: 400 });
    }
    
    if (result) {
      const walConfig = await dbManager.getWALConfiguration();
      return NextResponse.json({
        success: true,
        message: action === 'enable' ? 'WAL模式已启用' : 'WAL配置已优化',
        walConfig
      });
    } else {
      return NextResponse.json({
        success: false,
        error: '操作失败'
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('设置WAL模式失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
