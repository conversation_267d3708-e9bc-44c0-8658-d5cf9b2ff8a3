import EnhancedScraperManager from '@/components/EnhancedScraperManager';
import { dbManager } from '@/lib/server/database';
import { Suspense } from 'react';
import { Loader } from 'lucide-react';

// This is the new Server Component to fetch data
async function ScraperPageData() {
  // Fetch all necessary data on the server in parallel
  const [databaseStats, aiProcessingStats, scrapingSessions, defaultModelSetting] = await Promise.all([
    dbManager.getDatabaseStatsWithMedia(),
    dbManager.getAIStats(),
    dbManager.getScrapingSessions(),
    dbManager.getSystemSetting('default_text_model')
  ]);

  const defaultModelId = defaultModelSetting?.value || 'googleai/gemini-2.5-flash';

  // Pass the data as props to the client component
  return (
    <EnhancedScraperManager
      initialDbStats={databaseStats}
      initialAiStats={aiProcessingStats}
      initialScrapingSessions={scrapingSessions}
      defaultModelId={defaultModelId}
    />
  );
}

function LoadingFallback() {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载抓取管理器...</p>
        </div>
      </div>
    );
}

// The main page component now uses Suspense
export default function ScraperPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <ScraperPageData />
    </Suspense>
  );
}
