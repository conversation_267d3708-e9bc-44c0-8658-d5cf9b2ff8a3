
'use client';
import React, { useState, useEffect, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Bookmark,
  Settings,
  Play,
  Pause,
  Lightbulb,
  BookOpen,
  ChevronDown,
  ChevronUp,

  Loader,
  AlertCircle,
  ExternalLink,
  Type,
  ArrowUp,
  Volume2
} from 'lucide-react';
import { getArticleDetailsAction, updateLearningProgressAction, markArticleAsReadAction } from '@/app/actions';
import { useTTS } from '@/hooks/useTTS';
import authManager from '@/utils/auth';
import HLSVideoPlayer from './HLSVideoPlayer';
import { renderRubyText, renderExamples, renderCollocations, renderRelatedWords } from '@/utils/ruby-utils';
import VocabularyCard from './VocabularyCard';
import GrammarCard from './GrammarCard';
import { getMediaSrc, convertToApiPath } from '@/utils/media-utils';

interface ArticleReaderProps {
  article: any;
  onBack: () => void;
  standalone?: boolean; // 是否为独立页面模式
}


const ArticleReader: React.FC<ArticleReaderProps> = ({ article, onBack, standalone = false }) => {
  const router = useRouter();
  const [showTranslation, setShowTranslation] = useState(false);
  const [showFurigana, setShowFurigana] = useState(false);
  const [showVocabulary, setShowVocabulary] = useState(true);
  const [showGrammar, setShowGrammar] = useState(true);
  const [articleDetails, setArticleDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();
  const { isPlaying, speak, cancel } = useTTS();

  // 处理返回操作
  const handleBack = () => {
    if (standalone) {
      router.push('/');
    } else {
      onBack();
    }
  };
  const [playingVocabId, setPlayingVocabId] = useState<string | null>(null);
  const [playingContentId, setPlayingContentId] = useState<string | null>(null);


  useEffect(() => {
    loadArticleDetails();
    // 自动标记文章为已读
    markAsRead();
    // When component unmounts or article changes, stop any speech
    return () => {
      cancel();
    };
  }, [article.id]);

  const markAsRead = async () => {
    try {
      await markArticleAsReadAction(article.id);
    } catch (error) {
      console.error('Failed to mark article as read:', error);
    }
  };

  const loadArticleDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getArticleDetailsAction(article.id);
      if (result.success && result.data) {
        setArticleDetails(result.data);
      } else {
        throw new Error(result.error || 'Failed to load article details');
      }
    } catch (err: any) {
      setError('加载文章详情失败');
      console.error('加载文章详情错误:', err);
    } finally {
      setLoading(false);
    }
  };




  const handleReadAloud = () => {
    if (isPlaying) {
      cancel();
    } else if (articleDetails) {
      const textToSpeak = [
        articleDetails.title,
        articleDetails.subtitle,
        articleDetails.content,
      ]
        .filter(Boolean)
        .join('。 ');
      speak(textToSpeak, 'ja-JP');
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleVocabSpeak = (vocab: any) => {
    const vocabId = `${vocab.word}-${vocab.reading}`;

    if (playingVocabId === vocabId && isPlaying) {
      // 如果当前正在播放这个词汇，则停止
      cancel();
      setPlayingVocabId(null);
    } else {
      // 播放词汇发音
      const textToSpeak = vocab.reading || vocab.word;
      setPlayingVocabId(vocabId);
      speak(textToSpeak, 'ja-JP');

      // 设置播放结束后的清理
      setTimeout(() => {
        setPlayingVocabId(null);
      }, 3000); // 3秒后自动清理状态
    }
  };

  // 处理内容发音（英文解释、日语解释、搭配等）
  const handleContentSpeak = (text: string, contentId: string, language: 'ja-JP' | 'en-US' = 'ja-JP') => {
    if (playingContentId === contentId && isPlaying) {
      // 如果当前正在播放这个内容，则停止
      cancel();
      setPlayingContentId(null);
    } else {
      // 播放内容发音
      setPlayingContentId(contentId);
      speak(text, language);

      // 设置播放结束后的清理
      setTimeout(() => {
        setPlayingContentId(null);
      }, Math.max(3000, text.length * 100)); // 根据文本长度调整清理时间
    }
  };


  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载文章详情...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          <AlertCircle className="h-12 w-12 mx-auto mb-2" />
          <p>{error}</p>
        </div>
        <button
          onClick={handleBack}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          返回文章列表
        </button>
      </div>
    );
  }

  const details = articleDetails || article;
  const useLocalVideo = details.use_local_video && details.video_path;
  const imageSrc = getMediaSrc(details.featured_image_path, details.featured_image_url);

  const hasFurigana = details.title_furigana_html || details.content_furigana_html || details.subtitle_furigana_html;
  const hasTranslation = details.translation;

  return (
    <div>
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBack}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>返回新闻列表</span>
            </button>
            
            <div className="flex items-center space-x-2">
              {hasFurigana && (
                <button 
                  onClick={() => setShowFurigana(!showFurigana)}
                  className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <Type className="h-4 w-4" />
                  <span>{showFurigana ? '隐藏注音' : '显示注音'}</span>
                </button>
              )}
              {hasTranslation && (
                <button 
                  onClick={() => setShowTranslation(!showTranslation)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  {showTranslation ? '隐藏翻译' : '显示翻译'}
                </button>
              )}
              
              <a 
                href={details.url} 
                target="_blank" 
                rel="noopener noreferrer"
                title="查看原文"
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all"
              >
                <ExternalLink className="h-5 w-5" />
              </a>
              
              <button 
                onClick={handleReadAloud}
                title={isPlaying ? '停止朗读' : '朗读全文'}
                className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-all"
              >
                {isPlaying ? <Pause className="h-5 w-5 text-indigo-600" /> : <Play className="h-5 w-5" />}
              </button>
              
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all">
                <Bookmark className="h-5 w-5" />
              </button>
              
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-sm text-gray-600">{details.publishTime}</span>
            {details.rss_source_name && (
              <>
                <span className="text-gray-400">•</span>
                <span className="text-sm text-gray-600">{details.rss_source_name}</span>
              </>
            )}
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {showFurigana && details.title_furigana_html ? (
                <span className="prose prose-2xl">
                  {renderRubyText(details.title_furigana_html)}
                </span>
            ) : (
                details.title
            )}
          </h1>
          {details.subtitle && (
            showFurigana && details.subtitle_furigana_html ? (
                <div className="text-lg text-gray-600 prose">
                  {renderRubyText(details.subtitle_furigana_html)}
                </div>
            ) : (
                <p className="text-lg text-gray-600">{details.subtitle}</p>
            )
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            
            {useLocalVideo ? (
              details.video_path?.endsWith('.m3u8') ? (
                <div className="relative mb-6">
                  <HLSVideoPlayer src={convertToApiPath(details.video_path) || ''} className="w-full rounded-lg" />
                </div>
              ) : (
                <div className="relative mb-6">
                  <video src={convertToApiPath(details.video_path) || ''} controls className="w-full rounded-lg"></video>
                </div>
              )
            ) : details.video_url && details.video_url.includes('.html') ? (
              <div className="relative mb-6" style={{ paddingBottom: '56.25%' }}>
                <iframe
                  src={details.video_url}
                  title="NHK News Video"
                  className="absolute top-0 left-0 w-full h-full rounded-lg border-0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  在线播放
                </div>
              </div>
            ) : imageSrc ? (
              <img src={imageSrc} alt={details.title} className="w-full rounded-lg mb-6" />
            ) : null}

            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">正文</h2>
            </div>
            
            <div className="space-y-4">
              <div className="prose prose-lg max-w-none">
                {showFurigana && details.content_furigana_html ? (
                  renderRubyText(details.content_furigana_html)
                ) : (
                  <div dangerouslySetInnerHTML={{
                    __html: details.content_html || details.content
                  }} />
                )}
              </div>
              
              {showTranslation && details.translation && (
                <div className="text-gray-600 leading-relaxed bg-gray-50 p-4 rounded-lg mt-4">
                  <div
                    className="prose prose-gray max-w-none"
                    dangerouslySetInnerHTML={{
                      __html: details.translation.replace(/<img[^>]*>/gi, '<br>')
                    }}
                  />
                </div>
              )}

              {/* 底部操作按钮 */}
              <div className="flex items-center justify-between pt-8 mt-8 border-t border-gray-200">
                <button
                  onClick={handleBack}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>返回新闻列表</span>
                </button>

                <button
                  onClick={scrollToTop}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                >
                  <ArrowUp className="h-5 w-5" />
                  <span>回到顶部</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <button 
              onClick={() => setShowVocabulary(!showVocabulary)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-indigo-600" />
                <span className="font-medium text-gray-900">
                  重点词汇 ({details.vocabulary?.length || 0})
                </span>
              </div>
              {showVocabulary ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </button>
            
            {showVocabulary && details.vocabulary && (
              <div className="px-4 pb-4 space-y-2">
                {details.vocabulary.map((vocab: any, index: number) => (
                  <VocabularyCard
                    key={index}
                    vocab={vocab}
                    onVocabSpeak={handleVocabSpeak}
                    onContentSpeak={handleContentSpeak}
                    playingVocabId={playingVocabId}
                    playingContentId={playingContentId}
                    isPlaying={isPlaying}
                    compact={true}
                    showActions={true}
                    className=""
                  />
                ))}
                
                {(!details.vocabulary || details.vocabulary.length === 0) && (
                  <div className="text-center py-4 text-gray-500">
                    <BookOpen className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>暂无词汇数据</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Grammar Points */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <button 
              onClick={() => setShowGrammar(!showGrammar)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <Lightbulb className="h-5 w-5 text-orange-600" />
                <span className="font-medium text-gray-900">
                  语法解析 ({details.grammarPoints?.length || 0})
                </span>
              </div>
              {showGrammar ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </button>
            
            {showGrammar && details.grammarPoints && (
              <div className="px-4 pb-4 space-y-4">
                {details.grammarPoints.map((grammar: any, index: number) => {
                  // Parse JSON fields in frontend
                  let similarGrammar = null;
                  let examples: string[] = [];

                  try {
                    if (grammar.similar_grammar_json) {
                      similarGrammar = JSON.parse(grammar.similar_grammar_json);
                    }
                  } catch (e) {
                    console.warn('Failed to parse similar_grammar_json:', e);
                  }

                  try {
                    if (grammar.examples_json) {
                      examples = JSON.parse(grammar.examples_json);
                    }
                  } catch (e) {
                    console.warn('Failed to parse examples_json:', e);
                  }

                  // 构造统一的语法对象
                  const grammarData = {
                    ...grammar,
                    similarPatterns: similarGrammar,
                    examples: examples
                  };

                  return (
                    <GrammarCard
                      key={index}
                      grammar={grammarData}
                      onGrammarSpeak={(g) => handleContentSpeak(g.pattern, `grammar-${g.id}`, 'ja-JP')}
                      onContentSpeak={handleContentSpeak}
                      playingGrammarId={playingContentId?.startsWith('grammar-') ? playingContentId : null}
                      playingContentId={playingContentId}
                      isPlaying={isPlaying}
                      compact={true}
                      showActions={true}
                      className=""
                    />
                  );
                })}
                
                {(!details.grammarPoints || details.grammarPoints.length === 0) && (
                  <div className="text-center py-4 text-gray-500">
                    <Lightbulb className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>暂无语法点数据</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleReader;
