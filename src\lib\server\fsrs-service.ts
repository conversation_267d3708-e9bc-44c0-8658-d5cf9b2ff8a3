import {
  fsrs,
  createEmptyCard,
  Rating,
  State,
  Card,
  generatorParameters,
  FSRSParameters
} from 'ts-fsrs';
import { prisma } from './database';

// FSRS 参数配置
const FSRS_PARAMS: FSRSParameters = generatorParameters({
  enable_fuzz: true,
  enable_short_term: true,
  maximum_interval: 36500, // 100年
  request_retention: 0.9,  // 90% 记忆保持率
  w: [
    0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61
  ]
});

// 自动掌握的阈值配置
const MASTERY_THRESHOLDS = {
  MIN_STABILITY_DAYS: 30,    // 最小稳定性天数
  MIN_REVIEWS: 5,            // 最少复习次数
  MAX_DIFFICULTY: 5.0,       // 最大难度值
  MIN_SUCCESS_RATE: 0.8      // 最小成功率
};

export class FSRSService {
  private fsrsInstance = fsrs(FSRS_PARAMS);

  /**
   * 将数据库记录转换为 FSRS Card
   */
  private recordToCard(record: any): Card {
    if (!record.fsrs_due) {
      // 新卡片
      return createEmptyCard(new Date());
    }

    return {
      due: new Date(record.fsrs_due),
      stability: record.fsrs_stability || 0,
      difficulty: record.fsrs_difficulty || 0,
      elapsed_days: record.fsrs_elapsed_days || 0,
      scheduled_days: record.fsrs_scheduled_days || 0,
      learning_steps: record.fsrs_learning_steps || 0,
      reps: record.fsrs_reps || 0,
      lapses: record.fsrs_lapses || 0,
      state: this.stringToState(record.fsrs_state || 'New'),
      last_review: record.fsrs_last_review ? new Date(record.fsrs_last_review) : undefined
    };
  }

  /**
   * 将 FSRS Card 转换为数据库字段
   */
  private cardToRecord(card: Card) {
    return {
      fsrs_due: card.due,
      fsrs_stability: card.stability,
      fsrs_difficulty: card.difficulty,
      fsrs_elapsed_days: card.elapsed_days,
      fsrs_scheduled_days: card.scheduled_days,
      fsrs_learning_steps: card.learning_steps,
      fsrs_reps: card.reps,
      fsrs_lapses: card.lapses,
      fsrs_state: this.stateToString(card.state),
      fsrs_last_review: card.last_review
    };
  }

  /**
   * 字符串转 State 枚举
   */
  private stringToState(stateStr: string): State {
    switch (stateStr) {
      case 'New': return State.New;
      case 'Learning': return State.Learning;
      case 'Review': return State.Review;
      case 'Relearning': return State.Relearning;
      default: return State.New;
    }
  }

  /**
   * State 枚举转字符串
   */
  private stateToString(state: State): string {
    switch (state) {
      case State.New: return 'New';
      case State.Learning: return 'Learning';
      case State.Review: return 'Review';
      case State.Relearning: return 'Relearning';
      default: return 'New';
    }
  }

  /**
   * 数字评分转 Rating 枚举
   */
  private numberToRating(quality: number): Rating {
    switch (quality) {
      case 1: return Rating.Again;
      case 2: return Rating.Hard;
      case 3: return Rating.Good;
      case 4: return Rating.Easy;
      default: return Rating.Good;
    }
  }

  /**
   * 判断是否应该自动标记为已掌握
   */
  private shouldAutoMaster(card: Card, successfulReviews: number): boolean {
    return (
      card.stability >= MASTERY_THRESHOLDS.MIN_STABILITY_DAYS &&
      card.reps >= MASTERY_THRESHOLDS.MIN_REVIEWS &&
      card.difficulty <= MASTERY_THRESHOLDS.MAX_DIFFICULTY &&
      successfulReviews / Math.max(card.reps, 1) >= MASTERY_THRESHOLDS.MIN_SUCCESS_RATE &&
      card.state === State.Review
    );
  }

  /**
   * 提交复习结果
   */
  async submitReview(recordId: number, quality: number, userId: string) {
    const userIdInt = parseInt(userId);
    
    // 获取学习记录
    const record = await prisma.user_learning_records.findFirst({
      where: { id: recordId, user_id: userIdInt }
    });
    
    if (!record) {
      throw new Error("Record not found");
    }

    // 转换为 FSRS Card
    const card = this.recordToCard(record);
    const rating = this.numberToRating(quality);
    const now = new Date();

    // 使用 FSRS 算法计算下次复习
    const scheduling_cards = this.fsrsInstance.repeat(card, now);
    const result = (scheduling_cards as any)[rating];
    const newCard = result.card;
    const log = result.log;

    // 计算成功复习次数（用于自动掌握判断）
    const successfulReviews = await prisma.fsrs_review_logs.count({
      where: {
        learning_record_id: recordId,
        rating: { gte: 3 } // Good 或 Easy
      }
    });

    // 判断是否自动掌握
    const shouldMaster = this.shouldAutoMaster(newCard, successfulReviews);
    const newStatus = shouldMaster ? 'mastered' : 'learning';

    // 更新学习记录
    const updatedRecord = await prisma.user_learning_records.update({
      where: { id: recordId },
      data: {
        status: newStatus,
        ...this.cardToRecord(newCard),
        updated_at: new Date()
      }
    });

    // 记录复习日志
    await prisma.fsrs_review_logs.create({
      data: {
        user_id: userIdInt,
        learning_record_id: recordId,
        rating: rating,
        state: this.stateToString(log.state),
        due: log.due,
        stability: log.stability,
        difficulty: log.difficulty,
        elapsed_days: log.elapsed_days,
        last_elapsed_days: log.last_elapsed_days,
        scheduled_days: log.scheduled_days,
        learning_steps: log.learning_steps,
        review_time: log.review
      }
    });

    return {
      record: updatedRecord,
      autoMastered: shouldMaster,
      nextReview: newCard.due,
      stability: newCard.stability,
      difficulty: newCard.difficulty
    };
  }

  /**
   * 获取今日到期的复习项目（包括逾期项目）
   */
  async getTodayReviews(userId: string) {
    const userIdInt = parseInt(userId);
    const now = new Date();

    // 计算今天结束时间
    const todayEnd = new Date(now);
    todayEnd.setHours(23, 59, 59, 999);

    return prisma.user_learning_records.findMany({
      where: {
        user_id: userIdInt,
        status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
        fsrs_due: { lte: todayEnd } // 包括今天和之前逾期的所有复习项目
      },
      include: {
        vocabulary: true,
        grammar_point: true,
        article: true
      }
    });
  }

  /**
   * 初始化新的学习项目
   */
  async initializeNewCard(recordId: number) {
    const card = createEmptyCard(new Date());
    
    await prisma.user_learning_records.update({
      where: { id: recordId },
      data: {
        ...this.cardToRecord(card),
        status: 'learning'
      }
    });

    return card;
  }

  /**
   * 获取学习统计信息
   */
  async getLearningStats(userId: string) {
    const userIdInt = parseInt(userId);
    
    const [total, mastered, learning, newItems] = await Promise.all([
      prisma.user_learning_records.count({
        where: { user_id: userIdInt }
      }),
      prisma.user_learning_records.count({
        where: { user_id: userIdInt, status: 'mastered' }
      }),
      prisma.user_learning_records.count({
        where: { user_id: userIdInt, status: 'learning' }
      }),
      prisma.user_learning_records.count({
        where: { user_id: userIdInt, status: 'new' }
      })
    ]);

    return {
      total,
      mastered,
      learning,
      new: newItems,
      masteryRate: total > 0 ? (mastered / total) * 100 : 0
    };
  }
}

export const fsrsService = new FSRSService();
