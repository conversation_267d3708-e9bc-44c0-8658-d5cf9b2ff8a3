import React from 'react';
import { Volume2 } from 'lucide-react';

/**
 * 渲染包含Ruby标签的文本
 * @param text 包含<ruby>标签的文本
 * @returns React元素
 */
export function renderRubyText(text: string): React.ReactNode {
  if (!text || typeof text !== 'string') return null;

  // 首先修复不完整的ruby标签
  const fixedText = fixRubyTags(text);

  // 如果文本不包含ruby标签，直接返回
  if (!fixedText.includes('<ruby>') && !fixedText.includes('<rt>')) {
    return fixedText;
  }

  return (
    <span
      className="prose"
      dangerouslySetInnerHTML={{ __html: fixedText }}
    />
  );
}

/**
 * 修复不完整的Ruby标签格式（客户端版本）
 * 将孤立的<rt>标签转换为完整的<ruby><rt></rt></ruby>格式
 * @param text 包含可能不完整ruby标签的文本
 * @returns 修复后的文本
 */
export function fixRubyTags(text: string): string {
  // 确保输入是字符串类型
  if (!text || typeof text !== 'string') return '';

  // 正则表达式匹配模式：汉字+<rt>读音</rt>
  // 这个模式匹配没有被<ruby>包装的汉字和<rt>标签组合
  const incompleteRubyPattern = /([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>/g;

  // 将不完整的格式转换为完整的ruby格式
  let fixedText = text.replace(incompleteRubyPattern, '<ruby>$1<rt>$2</rt></ruby>');

  // 处理连续的汉字+<rt>组合，确保每个汉字都有自己的<ruby>标签
  // 例如：株式<rt>かぶしき</rt>保有<rt>ほゆう</rt> -> <ruby>株式<rt>かぶしき</rt></ruby><ruby>保有<rt>ほゆう</rt></ruby>
  const consecutivePattern = /([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>\s*([一-龯々〆〤ヶ]+)<rt>([^<]+)<\/rt>/g;

  // 多次应用修复，直到没有更多的连续模式
  let previousText = '';
  let iterations = 0;
  const maxIterations = 10; // 防止无限循环

  while (fixedText !== previousText && iterations < maxIterations) {
    previousText = fixedText;
    fixedText = fixedText.replace(consecutivePattern, '<ruby>$1<rt>$2</rt></ruby><ruby>$3<rt>$4</rt></ruby>');
    iterations++;
  }

  return fixedText;
}

/**
 * 清理Ruby标签，只保留主要文本
 * @param text 包含<ruby>标签的文本
 * @returns 清理后的纯文本
 */
export function stripRubyTags(text: string): string {
  if (!text) return '';

  // 移除ruby标签，保留主要文本
  return text
    .replace(/<ruby>(.*?)<rt>.*?<\/rt><\/ruby>/g, '$1')
    .replace(/<\/?[^>]+(>|$)/g, ''); // 移除其他HTML标签
}

/**
 * 检查文本是否包含Ruby标签
 * @param text 要检查的文本
 * @returns 是否包含Ruby标签
 */
export function hasRubyTags(text: string): boolean {
  return Boolean(text && text.includes('<ruby>'));
}

/**
 * 渲染例句列表，支持Ruby标签
 * @param examples 例句数组
 * @returns React元素
 */
export function renderExamples(examples: string[]): React.ReactNode {
  if (!examples || examples.length === 0) return null;
  
  return (
    <div className="space-y-2">
      {examples.map((example: string, index: number) => (
        <div key={index} className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
          {renderRubyText(example)}
        </div>
      ))}
    </div>
  );
}

/**
 * 渲染常用搭配，支持Ruby标签
 * @param collocations 搭配数组
 * @returns React元素
 */
export function renderCollocations(collocations: any[]): React.ReactNode {
  if (!collocations || collocations.length === 0) return null;

  return (
    <div className="space-y-2">
      {collocations.map((collocation: any, index: number) => (
        <div key={index} className="bg-blue-50 p-3 rounded-lg border border-blue-100">
          <div className="font-medium text-blue-800 flex items-center gap-2 flex-wrap">
            <span>{renderRubyText(collocation.collocation)}</span>
            <span className="text-blue-600 text-sm">- {renderRubyText(collocation.meaning)}</span>
          </div>
          {collocation.example && (
            <div className="text-gray-600 text-sm mt-1">
              {renderRubyText(collocation.example)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * 渲染关联词，支持Ruby标签和语义网络
 * @param relatedWords 关联词对象
 * @param onSpeak TTS发音函数，可选
 * @param playingContentId 当前播放的内容ID，可选
 * @param isPlaying 是否正在播放，可选
 * @returns React元素
 */
export function renderRelatedWords(
  relatedWords: any,
  onSpeak?: (text: string, contentId: string, language?: 'ja-JP' | 'en-US') => void,
  playingContentId?: string | null,
  isPlaying?: boolean
): React.ReactNode {
  if (!relatedWords || typeof relatedWords !== 'object') return null;

  const { synonyms, antonyms, hypernyms, hyponyms, wordFamily, relatedConcepts } = relatedWords;

  const renderWordGroup = (words: any[], label: string, bgColor: string, textColor: string, groupType: string) => {
    if (!words || words.length === 0) return null;

    return (
      <div>
        <span className="text-xs font-medium text-gray-600">{label}：</span>
        <div className="flex flex-wrap gap-1 mt-1">
          {words.map((wordItem: any, index: number) => {
            // 处理新的数据结构：{word: string, reading: string, meaning: string}
            let displayText: string;
            let readingText: string | null = null;
            let meaningText: string | null = null;

            if (typeof wordItem === 'string') {
              // 兼容旧的字符串格式
              displayText = wordItem;
            } else if (wordItem && typeof wordItem === 'object' && wordItem.word) {
              // 新的对象格式
              displayText = wordItem.word;
              readingText = wordItem.reading;
              meaningText = wordItem.meaning;
            } else {
              // 无效数据，跳过
              console.warn('Invalid word item:', wordItem);
              return null;
            }

            // 生成唯一的内容ID
            const contentId = `semantic-${groupType}-${index}-${displayText}`;

            return (
              <span key={index} className={`px-2 py-1 ${bgColor} ${textColor} rounded text-xs flex items-center gap-1`}>
                {/* TTS按钮 */}
                {onSpeak && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSpeak(displayText, contentId, 'ja-JP');
                    }}
                    className={`p-0.5 rounded transition-all ${
                      playingContentId === contentId && isPlaying
                        ? 'bg-white bg-opacity-50'
                        : 'hover:bg-white hover:bg-opacity-30'
                    }`}
                    title="发音"
                  >
                    <Volume2 className="h-3 w-3" />
                  </button>
                )}
                <span>{renderRubyText(displayText)}</span>
                {readingText && (
                  <span className="text-xs opacity-75">({readingText})</span>
                )}
                {meaningText && (
                  <span className="text-xs opacity-75 ml-1">[zh]{meaningText}</span>
                )}
              </span>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-2">
      {renderWordGroup(synonyms, '近义词', 'bg-green-100', 'text-green-800', 'synonyms')}
      {renderWordGroup(antonyms, '反义词', 'bg-red-100', 'text-red-800', 'antonyms')}
      {renderWordGroup(hypernyms, '上位词', 'bg-blue-100', 'text-blue-800', 'hypernyms')}
      {renderWordGroup(hyponyms, '下位词', 'bg-purple-100', 'text-purple-800', 'hyponyms')}
      {renderWordGroup(wordFamily, '词族', 'bg-orange-100', 'text-orange-800', 'wordFamily')}
      {renderWordGroup(relatedConcepts, '相关概念', 'bg-indigo-100', 'text-indigo-800', 'relatedConcepts')}
    </div>
  );
}
