
# 学习统计功能设计方案

## 1. 功能目标

本功能旨在取代现有的静态模拟数据，为用户提供一个真实、动态、可交互的学习统计仪表盘。其核心目标如下：

- **提供清晰反馈**: 让用户直观地看到自己的学习成果，例如掌握了多少词汇、完成了多少篇文章的阅读。
- **激励持续学习**: 通过可视化数据（如连续学习天数、周活动图）来激励用户保持学习习惯。
- **帮助自我评估**: 用户可以通过数据（如各等级词汇的掌握比例）了解自己在不同难度级别的强弱项，从而调整学习策略。
- **建立数据基础**: 为未来的游戏化功能（如成就、徽章、排行榜）奠定可靠的数据统计基础。

## 2. 核心统计指标定义与计算逻辑

为了确保数据的真实性和可实现性，我们定义以下核心指标，并明确其计算方法。

| 指标名称 (UI) | 英文标识 | 数据来源表 | 计算逻辑 |
| :--- | :--- | :--- | :--- |
| **连续学习天数** | `continuousDays` | `user_learning_records` | **[简化实现]** 查询过去30天内，用户有学习记录（`created_at`）的独立天数。 |
| **已学词汇总数** | `totalVocabulary` | `user_learning_records` | `SELECT COUNT(DISTINCT vocabulary_id) FROM user_learning_records WHERE user_id = ? AND record_type = 'vocabulary' AND status != 'new'` |
| **已掌握词汇** | `masteredItems` | `user_learning_records` | `SELECT COUNT(vocabulary_id) FROM user_learning_records WHERE user_id = ? AND record_type = 'vocabulary' AND status = 'mastered'` |
| **已读文章数** | `readArticles` | `user_learning_records` | `SELECT COUNT(DISTINCT article_id) FROM user_learning_records WHERE user_id = ? AND record_type = 'article' AND status = 'completed'` |
| **文章总数** | `totalArticles` | `articles` | `SELECT COUNT(id) FROM articles` |
| **本周活动** | `weeklyActivity` | `user_learning_records` | `SELECT strftime('%w', created_at) as day, COUNT(*) as count FROM user_learning_records WHERE user_id = ? AND created_at >= date('now', '-7 days') GROUP BY day` |
| **词汇掌握度分布** | `vocabularyProgress` | `vocabulary`, `user_learning_records` | 按JLPT等级分组，分别统计每个等级下的总词汇数和用户已掌握的词汇数，最后计算出百分比。 |
| **文章阅读进度** | `articleProgress` | `articles`, `user_learning_records` | 统计总文章数和用户已读文章数。 |

**注意**:
- “总学习时长”指标因无法在当前架构下准确追踪，建议从本次实现中移除，以确保所有数据的真实可靠。
- 所有统计都将与当前登录的 `user_id` 绑定。

## 3. 后端接口与数据库实现

### 3.1 数据库层 (`database.ts`)

需要实现两个新的数据库查询函数：

1.  **`getLearningStats(userId: string)`**:
    -   在一个函数内，通过多个并行的SQL查询来高效地获取上述**核心统计指标**（连续学习天数、已学词汇、已掌握、已读文章等）。
    -   将所有查询结果聚合成一个单一的、结构化的JSON对象并返回。

2.  **`getUserProgress(userId: string)`**:
    -   专门用于计算“词汇掌握度分布”和“文章阅读进度”。
    -   通过带有`GROUP BY`的JOIN查询，按N5-N1等级统计词汇的掌握情况。
    -   返回一个包含各级别进度详情的JSON对象。

### 3.2 后端服务层 (`actions.ts`)

需要创建两个新的 Server Action 来暴露数据库的统计功能：

1.  **`getLearningStatsAction(userId: string)`**:
    -   调用`dbManager.getLearningStats(userId)`。
    -   处理可能出现的数据库错误，并返回一个包含 `success` 标志和 `data` 或 `error` 信息的标准响应对象。

2.  **`getUserProgressAction(userId: string)`**:
    -   调用`dbManager.getUserProgress(userId)`。
    -   同样进行错误处理和标准化的响应封装。

## 4. 前端实现方案

### 4.1 页面组件 (`dashboard/page.tsx`)

-   该组件将保持为一个**客户端组件** (`'use client'`)，因为它需要根据用户交互和登录状态来获取数据。
-   它将负责调用 `getLearningStatsAction` 和 `getUserProgressAction`。

### 4.2 UI组件 (`Dashboard.tsx`)

-   移除所有现存的硬编码假数据。
-   从父组件接收 `initialStats` 和 `initialProgress` 作为props。
-   增加**加载状态 (Loading State)**：在等待数据返回时，显示一个加载指示器（例如，骨架屏或旋转图标），提升用户体验。
-   增加**错误状态 (Error State)**：如果数据获取失败，向用户显示一个友好的错误提示和“重试”按钮。
-   **数据绑定**: 将从props接收到的真实统计数据，精确地绑定到UI的各个部分（统计卡片、进度条、图表等）。

## 5. 实施步骤

1.  **后端开发 (数据库)**: 在 `src/lib/server/database.ts` 中实现 `getLearningStats` 和 `getUserProgress` 两个新的数据库方法，并编写好相应的SQL查询语句。
2.  **后端开发 (服务)**: 在 `src/app/actions.ts` 中创建 `getLearningStatsAction` 和 `getUserProgressAction` 两个Server Action。
3.  **前端重构 (页面)**:
    -   修改 `src/app/(main)/dashboard/page.tsx`。
    -   移除旧的 `useEffect` 数据获取逻辑。
    -   改为在组件加载时，调用新的Server Actions，并将获取到的数据通过props传递给 `Dashboard` 组件。
4.  **前端重构 (UI)**:
    -   修改 `src/components/Dashboard.tsx`。
    -   移除所有模拟数据，改造为接收props。
    -   实现并美化加载和错误状态的UI显示。
5.  **联调与测试**: 验证从数据库到前端的整个数据链路是否通畅，统计结果是否准确无误。

## 6. 未来扩展

- **成就系统**: 基于统计数据，设计一套成就和徽章系统 (例如，“掌握100个N5词汇”、“连续学习7天”等)。
- **学习报告**: 增加生成周报或月报的功能，并支持导出为PDF。
- **图表增强**: 引入更复杂的图表，例如使用`recharts`库来展示词汇掌握趋势、每日学习活动热力图等。
