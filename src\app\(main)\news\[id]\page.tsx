import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { Loader } from 'lucide-react';
import { dbManager } from '@/lib/server/database';
import StandaloneArticleReader from './StandaloneArticleReader';

interface NewsDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

// Server Component to fetch article data
async function NewsDetailContent({ params }: NewsDetailPageProps) {
  const resolvedParams = await params;
  const articleId = parseInt(resolvedParams.id, 10);
  
  if (isNaN(articleId)) {
    notFound();
  }

  try {
    const article = await dbManager.getArticleById(articleId);
    
    if (!article) {
      notFound();
    }

    return (
      <div className="min-h-screen bg-gray-50">
        <StandaloneArticleReader article={article} />
      </div>
    );
  } catch (error) {
    console.error('Failed to load article:', error);
    notFound();
  }
}

// Loading component
function LoadingComponent() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader className="h-8 w-8 animate-spin text-indigo-600 mx-auto mb-4" />
        <p className="text-gray-600">正在加载文章...</p>
      </div>
    </div>
  );
}

// Main page component
export default function NewsDetailPage({ params }: NewsDetailPageProps) {
  return (
    <Suspense fallback={<LoadingComponent />}>
      <NewsDetailContent params={params} />
    </Suspense>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: NewsDetailPageProps) {
  const resolvedParams = await params;
  const articleId = parseInt(resolvedParams.id, 10);
  
  if (isNaN(articleId)) {
    return {
      title: '文章不存在',
    };
  }

  try {
    const article = await dbManager.getArticleById(articleId);
    
    if (!article) {
      return {
        title: '文章不存在',
      };
    }

    return {
      title: article.title,
      description: article.subtitle || article.title,
    };
  } catch (error) {
    return {
      title: '加载失败',
    };
  }
}
