# 视频异步下载与手动切换 - 实施计划

本文档详细描述了将视频下载功能重构为异步后台任务的具体实施步骤，旨在提升系统性能和用户体验。

## 第一阶段：数据库升级与准备 (Database Migration)
-   **任务**: 为 `articles` 表添加新字段以支持视频下载状态管理。
-   **目标文件**: `scripts/init-new-db-optimized.js`
-   **具体操作**:
    1.  在 `CREATE TABLE articles` 语句中，添加以下字段：
        -   `video_metadata_json TEXT`: 存储视频下载所需的元数据（如M3U8 URL）。
        -   `video_m3u8_content TEXT`: 存储M3U8播放列表的完整内容，确保下载可靠性。
        -   `video_download_status TEXT`: 存储下载状态 (`pending`, `downloading`, `completed`, `failed`)。
        -   `use_local_video BOOLEAN`: 标记是否使用本地视频（`0`=远程，`1`=本地）。
    2.  为新字段设置合理的默认值，例如 `video_download_status` 默认为 `'disabled'`，`use_local_video` 默认为 `0`。

## 第二阶段：核心抓取逻辑重构 (Scraper Refactoring)
-   **任务**: 修改新闻抓取器，使其不再直接下载视频，而是提取并保存下载所需的元数据。
-   **目标文件**: `src/lib/server/scraper.ts`
-   **具体操作**:
    1.  在 `processRSSItem` 方法中，当检测到视频时，调用新的 `extractVideoMetadata` 内部函数。
    2.  `extractVideoMetadata` 函数将完整实现解析播放器页面、获取M3U8播放列表URL及其内容的逻辑。
    3.  将提取到的元数据和M3U8内容存入数据库对应的新字段中。
    4.  将视频的 `video_download_status` 状态设置为 `'pending'`。

## 第三阶段：后端服务与数据库接口开发 (Backend & DB Layer)
-   **任务**: 创建新的后端服务（Server Actions）和数据库接口，用于管理和执行视频下载任务。
-   **目标文件**: `src/app/actions.ts`, `src/lib/server/database.ts`
-   **具体操作**:
    1.  在 `database.ts` 中，添加用于查询待下载视频、更新下载状态、切换播放源（`use_local_video`）等操作的新函数。
    2.  在 `actions.ts` 中，创建以下核心服务：
        -   `startVideoDownloadAction`: 接收一个可选的文章ID数组。它会查询数据库中的待处理任务，并在后台逐一执行视频下载和转码。
        -   `getVideosForManagementAction`: 获取一个分页的、包含视频的文章列表，供前端UI展示。
        -   `setUseLocalVideoAction`: 用于在用户确认后，将指定文章的播放源切换为本地视频。
        -   `batchUpdateVideoSourceAction`: 用于批量切换播放源。

## 第四阶段：前端管理界面开发 (Frontend UI Implementation)
-   **任务**: 创建一个全新的“视频管理”界面，并更新文章阅读器以支持视频源切换。
-   **目标文件**: `src/components/EnhancedScraperManager.tsx`, `src/components/ArticleReader.tsx`
-   **具体操作**:
    1.  在 `EnhancedScraperManager.tsx` 中新增一个“视频管理”标签页。
    2.  该标签页将调用 `getVideosForManagementAction`，分页显示所有包含视频的文章。
    3.  列表中每一项都包含：多选框、文章标题、下载状态、以及一个用于切换播放源的开关。
    4.  页面顶部提供“下载选中项”和“批量切换播放源”等操作按钮。
    5.  修改 `ArticleReader.tsx`，使其能根据 `use_local_video` 字段的值，智能地决定是播放本地视频文件还是远程内嵌网页。同时，提供预览和确认切换的UI。
