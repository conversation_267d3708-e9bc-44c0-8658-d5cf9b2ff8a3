# 数据库切换方案分析 (SQLite to PostgreSQL)

## 1. 概述

本文档旨在深入分析将应用数据库从 SQLite 切换到 PostgreSQL 的几种可行方案，评估其技术可行性、实现难度和长期价值，为项目的架构演进提供决策依据。

## 2. 方案对比

| 方案 | 核心思想 | 实现难度 | 推荐指数 |
| :--- | :--- | :--- | :--- |
| **1. 直接切换到 PostgreSQL** | 一次性重构，完全放弃SQLite。 | 中等 🟠 | ⭐⭐⭐⭐ |
| **2. 手动实现数据库切换** | 自己编写抽象层，让代码能兼容两种数据库。 | **极高** 🔴 | ⭐ |
| **3. 引入Prisma ORM** | 使用专业的ORM框架来管理数据库连接和查询。 | 中高 🟡 | ⭐⭐⭐⭐⭐ |

---

### 方案 1: 直接切换到 PostgreSQL (一次性迁移)

*   **这是什么？**
    一个目标明确的重构任务。我们将彻底移除 `sqlite3` 相关的代码，并用 `pg` (PostgreSQL驱动) 的代码来替换它。应用将不再支持 SQLite。

*   **如何实现？**
    1.  **修改依赖**: 在 `package.json` 中，用 `pg` 替换 `sqlite3`。
    2.  **重写数据访问层**: 这是主要工作。需要重写 `src/lib/server/database.ts`，将所有 `new sqlite3.Database()` 和 `db.all/get/run()` 的调用，替换为使用 `pg` 的连接池和查询方法。
    3.  **修改初始化脚本**: 调整 `scripts/init-new-db-optimized.js` 中的 `CREATE TABLE` 语句，使其符合 PostgreSQL 的语法（例如，`AUTOINCREMENT` -> `SERIAL`）。
    4.  **环境配置**: 需要一个正在运行的 PostgreSQL 服务器，并通过环境变量（如 `DATABASE_URL`）来配置连接。

*   **优点**:
    *   最终代码干净、目标明确。
    *   完全发挥 PostgreSQL 的性能和特性。
    *   相比方案2，工作量和复杂度都小得多。

*   **缺点**:
    *   失去了 SQLite 带来的本地开发的简便性。
    *   是一条“单行道”，未来再想切换回 SQLite 会很困难。

*   **难度评估**: **中等**。工作量主要在于重写数据访问层的代码，虽然繁琐但逻辑清晰。

---

### 方案 2: 手动实现数据库切换 (自己造轮子)

*   **这是什么？**
    构建我们自己的“迷你ORM”或数据库抽象层。目标是让上层代码（如 Server Actions）在调用数据库时，不知道底层究竟是 SQLite 还是 PostgreSQL。

*   **如何实现？**
    1.  **创建抽象接口**: 定义一个通用的接口，例如 `IDatabaseAdapter`，包含 `query()`, `run()` 等方法。
    2.  **实现两个适配器**:
        *   `SqliteAdapter`: 实现 `IDatabaseAdapter` 接口，其内部使用 `sqlite3` 库。
        *   `PostgresAdapter`: 实现 `IDatabaseAdapter` 接口，其内部使用 `pg` 库。
    3.  **重构 `DatabaseManager`**: 让它依赖于 `IDatabaseAdapter` 接口，而不是任何具体的库。
    4.  **动态加载**: 在系统启动时，根据环境变量（如 `DB_TYPE`）来决定实例化哪个适配器。
    5.  **处理SQL方言**：这是最棘手的部分。初始化脚本需要包含两种数据库的建表语句，并根据环境执行对应的部分。在编写查询时，也可能需要处理两种方言的细微差别。

*   **优点**:
    *   理论上非常灵活。

*   **缺点**:
    *   **极其复杂**，工作量巨大。我们基本上是在重新发明一个简陋、脆弱的轮子。
    *   维护成本高，未来很容易出现只有一种数据库能正常工作的情况。
    *   处理两种SQL方言的差异会带来无尽的麻烦。

*   **难度评估**: **极高**。这是一个典型的“过度设计”陷阱，强烈不推荐。

---

### 方案 3: 引入 Prisma ORM (使用专业工具)

*   **这是什么？**
    引入一个专业的、类型安全的 Node.js ORM 框架。Prisma 会帮我们处理底层数据库的差异，让我们能更专注于业务逻辑。

*   **如何实现？**
    1.  **安装 Prisma**: `npm install prisma --save-dev` 和 `npm install @prisma/client`。
    2.  **定义 Schema**: 创建 `prisma/schema.prisma` 文件。这是**新的、唯一的真实数据源**。您需要在这个文件中用 Prisma 的语法重新定义所有的数据模型（`model Article`, `model Vocabulary`...）。
    3.  **配置多数据库**: 在 `schema.prisma` 的 `datasource` 部分，可以轻松定义 SQLite 和 PostgreSQL 两种数据源。
    4.  **生成和运行迁移**: 运行 `npx prisma migrate dev`。Prisma 会自动根据您的 `schema.prisma` 文件，生成**对应数据库方言**的 SQL 迁移文件，并应用到数据库。这完美解决了SQL方言问题。
    5.  **重构所有数据访问代码**: 这是主要工作。您需要用 Prisma Client 替换所有原生的 SQL 查询。
        *   例如，`dbManager.query('SELECT * FROM articles')` 会变成 `prisma.article.findMany()`。

*   **优点 (详细说明)**:

    *   **1. 未来的数据库维护会变得极其简单 (Simpler Maintenance)**
        *   **现状 (Raw SQL)**: 当我们需要为`vocabulary`表增加一个`difficulty_score`字段时，需要：
            1.  手动修改`scripts/init-new-db-optimized.js`中的`CREATE TABLE`语句。
            2.  在`src/lib/server/database.ts`中，手动找到所有相关的`INSERT`和`UPDATE`查询，并确保都加上了新字段。非常容易遗漏。
        *   **使用Prisma后**:
            1.  只需在`prisma/schema.prisma`的`vocabulary`模型中加一行`difficulty_score Float?`。这是**唯一的真实数据源**。
            2.  运行`npx prisma migrate dev --name "add-vocab-difficulty"`，Prisma会自动生成并执行安全的`ALTER TABLE`语句。
            3.  从此，所有`prisma.vocabulary.create(...)`这样的调用都会获得类型提示，告诉你`difficulty_score`是可用的。

    *   **2. 表结构变更更安全、更可追溯 (Safer Schema Changes)**
        *   **现状 (Raw SQL)**: 假设我们要将`rss_sources`表中的`name`字段重命名为`source_name`。在SQLite中，这是一个高风险操作，通常需要新建表、复制数据、删旧表、重命名新表这四步，极易出错。
        *   **使用Prisma后**:
            1.  只需在`prisma/schema.prisma`中，将`name String`改为`source_name String`。
            2.  运行`npx prisma migrate dev --name "rename-rss-name"`。Prisma的迁移引擎足够智能，它会为我们生成并执行上述所有复杂、安全的操作步骤。所有变更都记录在可读的迁移文件中，团队协作清晰明了。

    *   **3. 获得完整的代码类型安全 (Full Type Safety)**
        *   **现状 (Raw SQL)**: 在`database.ts`中，当我们写`db.query('SELECT id, title FROM articles')`时，返回结果的类型是`any[]`。如果我们不小心在代码中写了`article.content`，编译器无法发现错误，只有在程序运行时才会因访问`undefined`的属性而崩溃。
        *   **使用Prisma后**:
            1.  `prisma/schema.prisma`文件定义了数据的唯一形态。
            2.  当我们写`prisma.article.findMany({ select: { id: true, title: true } })`时，TypeScript**在编译时**就知道返回的数组类型是`{ id: number; title: string; }[]`。
            3.  此时如果我们的代码尝试访问`article.content`，会立刻收到一个**编译错误**，而不是一个隐藏的运行时bug。这使得代码重构和维护的安全性大大提升。

*   **缺点**:
    *   需要学习 Prisma 的基本用法。
    *   重构工作量大，因为所有的数据查询方式都变了。
    *   增加了一个重要的外部依赖。

*   **难度评估**: **中高**。难度不在于“发明”，而在于“学习和适应”。虽然初次重构工作量不小，但一旦完成，未来的开发和维护会变得非常轻松。

---

## 4. 结论与建议

*   **方案2 vs 方案3，哪个更容易实现？**
    毫无疑问，**方案3（引入Prisma）比方案2（手动实现）要容易得多，也正确得多**。方案2试图解决的问题，正是Prisma这样的专业工具的核心价值所在。手动实现几乎一定会引入大量bug和难以维护的代码。

*   **最终推荐**：
    1.  如果您的**最终目标只是切换到PostgreSQL，并且不打算再换回来**，那么**方案1（直接切换）** 是最快、最直接的路径。
    2.  如果您**希望获得在开发环境（SQLite）和生产环境（PostgreSQL）之间轻松切换的灵活性**，那么**方案3（引入Prisma）是唯一推荐的、专业的选择**。它的长期收益（类型安全、易维护、易迁移）远远超过初期的学习和重构成本。
    
**对于一个需要长期维护和发展的项目来说，方案3是技术上最先进、最稳妥的选择。**
