# NHK日语学习平台 - 系统概述

## 项目简介

NHK日语学习平台是一个基于人工智能的日语新闻学习系统，通过抓取NHK新闻内容，为用户提供智能化的日语学习体验。系统支持RSS源管理、AI内容处理、词汇标注、语法解析等功能，帮助用户通过真实的新闻内容提升日语水平。

## 核心特性

### 🎯 智能学习系统
- **AI驱动的内容分析**：自动翻译、词汇提取、语法分析
- **个性化学习路径**：根据用户水平推荐合适的学习内容
- **学习进度追踪**：详细记录用户的学习历程和成果

### 📰 新闻内容管理
- **RSS源管理**：灵活配置多个RSS新闻源
- **智能抓取系统**：自动抓取和处理新闻内容
- **媒体文件支持**：自动下载和管理图片、视频等媒体文件

### 🤖 AI助教功能
- **智能问答**：回答日语学习相关问题
- **情景对话练习**：模拟真实场景的对话训练
- **语法解析助手**：分析句子结构和语法点

### 📚 学习工具
- **生词本管理**：收藏和复习重点词汇
- **学习统计**：可视化展示学习进度和成果
- **多级别支持**：支持N5到N1各个级别的学习内容

## 技术优势

- **前后端分离架构**：React前端 + Node.js后端
- **本地化部署**：SQLite数据库，无需复杂的服务器配置
- **响应式设计**：完美适配桌面端和移动端
- **模块化设计**：易于扩展和维护
- **用户管理系统**：支持多用户独立学习进度管理

## 适用场景

- **个人日语学习**：自学者的智能学习助手
- **教育机构**：日语教学的辅助工具
- **企业培训**：员工日语能力提升
- **研究用途**：日语语言学习和教学研究

## 系统要求

- **运行环境**：Node.js 16+ 
- **浏览器支持**：Chrome、Firefox、Safari、Edge等现代浏览器
- **存储空间**：建议预留1GB以上空间用于媒体文件存储
- **网络要求**：需要互联网连接以抓取RSS内容

## 版本信息

- **当前版本**：2.0.0
- **最后更新**：2024年12月
- **开发状态**：活跃开发中
- **许可证**：MIT License