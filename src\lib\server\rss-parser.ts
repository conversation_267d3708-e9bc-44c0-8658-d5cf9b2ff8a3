
import fetch from 'node-fetch';
import { DOMParser } from 'xmldom';
import xpath from 'xpath';
import { antiCrawler } from './anti-crawler';

export class RSSFieldMappingParser {
  constructor(private mappingConfig: { [key: string]: any }) {}

  // The main parsing function, now fully based on XPath
  parseRSSFeed(xmlContent: string): { feedTitle: string; feedDescription: string; items: any[] } {
    try {
      const doc = new DOMParser().parseFromString(xmlContent, 'text/xml');

      const feedTitle = xpath.select("string(//*[local-name()='rss']/*[local-name()='channel']/*[local-name()='title'] | /*[local-name()='feed']/*[local-name()='title'])", doc, true);
      const feedDescription = xpath.select("string(//*[local-name()='rss']/*[local-name()='channel']/*[local-name()='description'] | /*[local-name()='feed']/*[local-name()='subtitle'])", doc, true);
      
      const itemNodes = xpath.select("//*[local-name()='item'] | //*[local-name()='entry']", doc);
      
      const items: any[] = [];
      itemNodes.forEach(node => {
        try {
          const item = this.parseRSSItemNode(node as Node);
          if (item) {
            items.push(item);
          }
        } catch (error) {
          console.error('解析RSS/Atom条目失败:', error);
        }
      });
      return { 
        feedTitle: feedTitle ? feedTitle.toString() : 'Unknown Title', 
        feedDescription: feedDescription ? feedDescription.toString() : 'No Description', 
        items 
      };
    } catch (e) {
      console.error("XML Parsing failed", e);
      return { feedTitle: 'Parse Error', feedDescription: '', items: [] };
    }
  }

  // Parses a single item node using XPath
  private parseRSSItemNode(itemNode: Node): any {
    const result: { [key: string]: any } = {};
    for (const [fieldName, config] of Object.entries(this.mappingConfig)) {
      try {
        let value = this.extractValueFromNode(itemNode, config);
        value = this.transformValue(value, config.transform_rule);

        if (!value && config.is_required) {
          throw new Error(`必需字段 ${fieldName} 缺失`);
        }
        result[fieldName] = value || config.default_value || null;
      } catch (error: any) {
        if (config.is_required) {
          throw error;
        }
        result[fieldName] = config.default_value || null;
      }
    }

    // Ensure all required fields were found before returning the item
    const hasRequiredFields = Object.entries(this.mappingConfig).every(([fieldName, config]) => {
      if (!config.is_required) return true;
      return !!result[fieldName];
    });

    return hasRequiredFields ? result : null;
  }

  // Extracts a single value from the item node based on a config
  private extractValueFromNode(itemNode: Node, config: any): string | null {
    if (!config.xpath_selector) return null;
    try {
      // Make the absolute XPath selector relative to the current node
      const relativeSelector = config.xpath_selector
        .replace(/^\/\/item\//, '')
        .replace(/^\/\/entry\//, '');
      
      // Use string() to directly get the text value. 
      // This is smart enough to get text from text nodes, attributes, and CDATA sections.
      const result = xpath.select(`string(${relativeSelector})`, itemNode, true);
      
      if (typeof result === 'string' && result.trim() !== '') {
        return result.trim();
      }
      
      return null;
    } catch (e) {
      console.error(`XPath error on selector ${config.xpath_selector}:`, e);
      return null;
    }
  }

  // The transform logic remains mostly the same
  private transformValue(value: string | null, transformRule: string | object | undefined): string | null {
    if (!value || !transformRule) return value;
    try {
      const rule = typeof transformRule === 'string' ? JSON.parse(transformRule) : transformRule;
      let result = value;
      switch (rule.type) {
        case 'text':
          if (rule.trim) result = result.trim();
          if (rule.maxLength && result.length > rule.maxLength) result = result.substring(0, rule.maxLength);
          return result;
        case 'url':
          result = result.trim();
          if (rule.makeAbsolute && !result.startsWith('http')) {
            result = 'https://www3.nhk.or.jp' + (result.startsWith('/') ? result : '/' + result);
          }
          return result;
        case 'html': // Simply strip tags
          result = result.replace(/<[^>]*>/g, '').trim();
          if (rule.maxLength && result.length > rule.maxLength) result = result.substring(0, rule.maxLength);
          return result;
        case 'datetime':
          try {
            return new Date(value).toISOString();
          } catch (dateError) {
            return null; // Invalid date
          }
        default:
          return value;
      }
    } catch (error) {
      // JSON parsing or other errors
      return value;
    }
  }
}


export class RSSFetcher {
  async fetchRSSContent(url: string): Promise<string> {
    const response = await antiCrawler.safeRequest(url, { headers: { 'Accept': 'application/rss+xml, application/xml, text/xml' } });
    const content = await response.text();
    // A more lenient check for XML content
    if (!content.trim().startsWith('<')) {
      throw new Error('返回的内容不是有效的XML格式');
    }
    return content;
  }

  async testRSSSource(url: string, fieldMappings?: any) {
    try {
      const xmlContent = await this.fetchRSSContent(url);
      // Create a default mapping if none is provided for testing
      const mappingsToUse = fieldMappings && Object.keys(fieldMappings).length > 0 ? fieldMappings : {
          title: { xpath_selector: "//item/title", is_required: true, transform_rule: '{"type":"text"}' },
          link: { xpath_selector: "//item/link", is_required: true, transform_rule: '{"type":"url"}' },
          description: { xpath_selector: "//item/description", is_required: false, transform_rule: '{"type":"html", "stripTags": true}' }
      };
      const parser = new RSSFieldMappingParser(mappingsToUse);
      const { feedTitle, feedDescription, items } = parser.parseRSSFeed(xmlContent);
      return {
        success: true,
        itemCount: items.length,
        sampleItems: items.slice(0, 3),
        feedTitle,
        feedDescription,
        message: `成功解析 ${items.length} 个RSS条目`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `RSS源测试失败: ${error.message}`,
      };
    }
  }
}
