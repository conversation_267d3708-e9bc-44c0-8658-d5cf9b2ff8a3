import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-config';
import { redirect } from 'next/navigation';

// 获取服务器端会话
export async function getSession() {
  return await getServerSession(authOptions);
}

// 获取当前用户
export async function getCurrentUser() {
  const session = await getSession();
  return session?.user;
}

// 检查用户是否已登录，如果未登录则重定向到登录页
export async function requireAuth() {
  const session = await getSession();
  if (!session) {
    redirect('/login');
  }
  return session.user;
}

// 检查用户是否具有特定角色，如果没有则重定向到首页
export async function requireRole(role: string | string[]) {
  const user = await requireAuth();
  const roles = Array.isArray(role) ? role : [role];
  
  if (!roles.includes(user.role)) {
    redirect('/');
  }
  
  return user;
}

// 检查用户是否为管理员
export async function requireAdmin() {
  return requireRole('admin');
}