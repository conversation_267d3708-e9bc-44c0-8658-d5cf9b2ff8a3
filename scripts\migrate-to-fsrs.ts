import { PrismaClient } from '@prisma/client';
import { createEmptyCard, fsrs, generatorParameters } from 'ts-fsrs';

const prisma = new PrismaClient();

// FSRS 参数配置
const FSRS_PARAMS = generatorParameters({
  enable_fuzz: true,
  enable_short_term: true,
  maximum_interval: 36500,
  request_retention: 0.9,
});

const fsrsInstance = fsrs(FSRS_PARAMS);

/**
 * 将旧的 SRS 数据迁移到 FSRS 格式
 */
async function migrateLearningRecords() {
  console.log('开始迁移学习记录到 FSRS 格式...');

  // 获取所有需要迁移的学习记录
  const records = await prisma.user_learning_records.findMany({
    where: {
      // 只迁移已经开始学习的记录
      status: { not: 'new' },
      // 还没有 FSRS 数据的记录
      fsrs_due: null
    }
  });

  console.log(`找到 ${records.length} 条需要迁移的记录`);

  let migratedCount = 0;
  let errorCount = 0;

  for (const record of records) {
    try {
      // 创建新的 FSRS 卡片
      let card = createEmptyCard(record.created_at);

      // 对于已有的学习记录，创建基本的 FSRS 卡片
      if (record.status !== 'new') {
        // 设置基本的 FSRS 参数
        card = {
          ...card,
          reps: 1,
          stability: 1,
          difficulty: 5,
          elapsed_days: 0,
          scheduled_days: 1,
          state: record.status === 'mastered' ? 3 : 2, // Review state
          last_review: record.updated_at,
          due: new Date(Date.now() + 24 * 60 * 60 * 1000) // 明天
        };
      }

      // 更新记录
      await prisma.user_learning_records.update({
        where: { id: record.id },
        data: {
          fsrs_due: card.due,
          fsrs_stability: card.stability,
          fsrs_difficulty: card.difficulty,
          fsrs_elapsed_days: card.elapsed_days,
          fsrs_scheduled_days: card.scheduled_days,
          fsrs_learning_steps: card.learning_steps,
          fsrs_reps: card.reps,
          fsrs_lapses: card.lapses,
          fsrs_state: getStateString(card.state),
          fsrs_last_review: card.last_review
        }
      });

      migratedCount++;
      
      if (migratedCount % 100 === 0) {
        console.log(`已迁移 ${migratedCount} 条记录...`);
      }
    } catch (error) {
      console.error(`迁移记录 ${record.id} 时出错:`, error);
      errorCount++;
    }
  }

  console.log(`迁移完成！成功: ${migratedCount}, 失败: ${errorCount}`);
}

/**
 * 将 FSRS State 枚举转换为字符串
 */
function getStateString(state: number): string {
  switch (state) {
    case 0: return 'New';
    case 1: return 'Learning';
    case 2: return 'Review';
    case 3: return 'Relearning';
    default: return 'New';
  }
}

/**
 * 清理和重置所有学习记录为新状态（可选）
 */
async function resetAllToNew() {
  console.log('重置所有学习记录为新状态...');
  
  const result = await prisma.user_learning_records.updateMany({
    data: {
      status: 'new',
      fsrs_due: null,
      fsrs_stability: null,
      fsrs_difficulty: null,
      fsrs_elapsed_days: null,
      fsrs_scheduled_days: null,
      fsrs_learning_steps: null,
      fsrs_reps: 0,
      fsrs_lapses: 0,
      fsrs_state: 'New',
      fsrs_last_review: null
    }
  });

  console.log(`重置了 ${result.count} 条记录`);
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
  console.log('验证迁移结果...');

  const [total, withFsrsData, withoutFsrsData] = await Promise.all([
    prisma.user_learning_records.count(),
    prisma.user_learning_records.count({
      where: { fsrs_due: { not: null } }
    }),
    prisma.user_learning_records.count({
      where: { 
        status: { not: 'new' },
        fsrs_due: null 
      }
    })
  ]);

  console.log(`总记录数: ${total}`);
  console.log(`已有 FSRS 数据: ${withFsrsData}`);
  console.log(`缺少 FSRS 数据的非新记录: ${withoutFsrsData}`);

  if (withoutFsrsData > 0) {
    console.warn(`警告: 还有 ${withoutFsrsData} 条记录缺少 FSRS 数据`);
  } else {
    console.log('✅ 迁移验证通过');
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const args = process.argv.slice(2);
    
    if (args.includes('--reset')) {
      await resetAllToNew();
    } else if (args.includes('--validate')) {
      await validateMigration();
    } else {
      await migrateLearningRecords();
      await validateMigration();
    }
  } catch (error) {
    console.error('迁移过程中出错:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

export { migrateLearningRecords, resetAllToNew, validateMigration };
