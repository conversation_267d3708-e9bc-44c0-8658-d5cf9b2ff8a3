# NHK新闻页面 `__DetailProp__` 变量结构与内容提取分析

## 1. 背景概述

在抓取NHK新闻页面时，我们发现其文章正文并非总是直接存在于HTML的固定标签（如`<p>`或`<div>`）中。为了应对动态加载和反爬虫机制，NHK采用了一种更复杂的策略：将新闻的绝大部分元数据和正文内容都嵌入到一个名为 `__DetailProp__` 的JavaScript变量中。

要准确、稳定地提取正文，我们必须深入理解这个变量的内部结构，并模拟页面脚本对其进行处理的方式。本文档旨在详细解析`__DetailProp__`变量的结构，并基于此制定最终的抓取策略。

## 2. `__DetailProp__` 变量分析

`__DetailProp__` 变量位于新闻HTML页面的一个 `<script>` 标签内，其形式为 `var __DetailProp__ = {...};`。它本质上是一个巨大的JavaScript对象字面量（并非严格的JSON），包含了该新闻页面的所有关键信息。

### 2.1 关键属性解析

以下是与我们内容抓取最相关的几个关键属性：

- **基本信息**: `title`, `news_prearranged_time`, `news_web_image_uri`, `news_web_video_uri` 等。

- **正文内容**: NHK主要通过以下两种模式来加载正文，我们的抓取器必须能够同时应对：

#### 模式一：直接内容加载

- **关键属性**: `more_body`, `more`, `detail_more`
- **结构分析**: 在部分（通常是较旧或较简单的）新闻页面中，完整的文章正文HTML会作为一个长字符串，直接存储在这些属性中。
- **页面行为**: 页面的JavaScript脚本会检查这些属性，如果发现有内容，就直接将其内容渲染到页面的正文区域。
- **抓取策略**: 这是最简单的情况。我们的抓取器只需从`__DetailProp__`的声明字符串中，用正则表达式提取这些属性的值，即可获得完整的正文HTML。

#### 模式二：间接内容加载（分片请求）

- **关键属性**: `body`
- **结构分析**: 在更多（特别是较新的）新闻页面中，`more_body`等属性为空。此时，正文内容被拆分成一个或多个片段，其信息存储在`body`数组中。
    ```javascript
    "body": [
        { "type": "1", "path": "body/k10014849241_..._01.json", "size": "small" },
        { "type": "1", "path": "body/k10014849241_..._02.json", "size": "small" }
    ]
    ```
    - `body`是一个对象数组，其本身是符合JSON格式的。
    - `type: "1"` 代表这是一个正文HTML片段。
    - `path` 提供了该HTML片段的**相对URL**，指向一个服务器上的`.json`文件。这个文件本身只包含一段HTML字符串。
- **页面行为**: 页面的JavaScript脚本会遍历`body`数组。对于每一个`type`为`"1"`的对象，它会根据`path`拼接出完整的URL并发起一个异步HTTP请求。在所有请求完成后，脚本会将返回的HTML片段按顺序拼接起来，形成完整的文章正文，并将其动态插入到页面中。
- **抓取策略**: 我们的抓取器必须**完整地模拟**这个过程：
    1.  用正则表达式从`__DetailProp__`声明中提取出`body`数组的字符串。
    2.  用`JSON.parse`安全地解析这个数组字符串。
    3.  遍历`body`数组，对每个`type`为`"1"`的元素，提取其`path`值。
    4.  将相对`path`与新闻页面的基础URL拼接，构成一个完整的JSON文件URL。
    5.  **并发请求**所有这些URL。
    6.  将返回的HTML字符串（即正文片段）按顺序拼接起来。
    7.  所有片段拼接完成后，我们便得到了完整的文章正文HTML。

## 3. 最终抓取策略

基于以上分析，为了确保抓取的健壮性和全面性，我们制定了如下的多层次智能抓取策略：

1.  **核心策略 - 智能解析 `__DetailProp__`**:
    -   抓取器会首先在页面中查找并获取包含 `var __DetailProp__` 的脚本内容。
    -   **优先尝试直接内容**：用正则表达式检查 `more_body`, `more`, `detail_more` 等直接内容属性。如果它们有值，则直接提取并使用其内容。
    -   **其次尝试间接内容**：如果直接内容为空，则用正则表达式检查 `body` 数组是否存在。如果存在，则模拟页面的分片加载行为，并发请求所有路径并合并成完整的HTML。

2.  **最终备选 - 带防护的CSS选择器**:
    -   **只有在以上所有基于 `__DetailProp__` 的智能解析方法都失败的情况下**，系统才会回退到最初始的方案：使用用户在“RSS源管理”中配置的CSS选择器来尝试从页面DOM中直接提取内容。
    -   **新增防护**：在回退到此方案时，系统会检查提取到的内容是否意外包含了JavaScript代码（如 `var __DetailProp__`），如果包含，则认为提取失败，以防止脏数据入库。

## 4. 结论

通过实施这种分层、智能、模拟浏览器行为的抓取策略，我们的系统将能够有效应对NHK新闻网站已知的所有内容加载方式，极大地提高了正文抓取的成功率和准确性，确保为用户提供高质量的学习素材。
