'use client';
import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  Bookmark,
  RefreshCw,
  FileText,
  Type
} from 'lucide-react';
import NewsCard from './NewsCard';
import ArticleReader from './ArticleReader';
import TimeFilter from './TimeFilter';
import CategoryFilter from './CategoryFilter';
import UnreadFilter from './UnreadFilter';
import PageJumper from './PageJumper';
import { useNews } from '@/contexts/NewsContext';

interface NewsReaderProps {
  initialArticles: any[];
  totalCount: number;
  currentPage: number;
  limit: number;
}

const NewsReader: React.FC<NewsReaderProps> = ({
  initialArticles,
  totalCount,
  currentPage,
  limit
}) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { selectedArticle, setSelectedArticle } = useNews();

  const [showContent, setShowContent] = useState(true);
  const [showFurigana, setShowFurigana] = useState(false);
  
  const searchQuery = searchParams.get('search') || '';
  const page = searchParams.get('page') || '1';

  const articles = initialArticles;
  const totalPages = Math.ceil(totalCount / limit);

  // 监听URL参数变化，当搜索参数改变时重置选中的文章
  // 使用ref来存储上一次的参数值，避免在用户选择文章时误重置
  const prevParamsRef = useRef({ searchQuery, page });

  useEffect(() => {
    const prevParams = prevParamsRef.current;
    const currentParams = { searchQuery, page };

    // 检查是否有参数发生了变化
    const hasParamsChanged =
      prevParams.searchQuery !== currentParams.searchQuery ||
      prevParams.page !== currentParams.page;

    if (hasParamsChanged && selectedArticle) {
      // 只有当URL参数确实发生变化且当前有选中文章时，才重置
      setSelectedArticle(null);
    }

    // 更新ref中的参数值
    prevParamsRef.current = currentParams;
  }, [searchQuery, page, selectedArticle, setSelectedArticle]);

  const handleRefresh = () => {
    router.refresh();
  };

  // 重置选中的文章，返回新闻一览
  const resetToNewsList = () => {
    setSelectedArticle(null);
  };

  const createPageURL = (pageNumber: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', pageNumber.toString());
    return `${pathname}?${params.toString()}`;
  };

  if (selectedArticle) {
    return (
      <ArticleReader 
        article={selectedArticle} 
        onBack={() => setSelectedArticle(null)} 
      />
    );
  }

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold text-gray-900 truncate">新闻一览</h2>
          <p className="text-gray-600 mt-1 truncate">
            {totalCount} 篇文章
            {searchQuery && ` • 搜索: "${searchQuery}"`}
          </p>
        </div>

        {/* Filters - moved to header row */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-shrink-0">
          <div className="flex items-center gap-4">
            <TimeFilter className="" />
            <CategoryFilter className="" />
            <UnreadFilter className="" />
          </div>

          <div className="flex items-center space-x-3">
            <div className="hidden md:flex items-center space-x-4">
              <label className="flex items-center space-x-2 text-sm text-gray-600 cursor-pointer">
                <FileText className="h-4 w-4" />
                <input
                  type="checkbox"
                  checked={showContent}
                  onChange={(e) => setShowContent(e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span>显示正文</span>
              </label>
              <label className="flex items-center space-x-2 text-sm text-gray-600 cursor-pointer">
                <Type className="h-4 w-4" />
                <input
                  type="checkbox"
                  checked={showFurigana}
                  onChange={(e) => setShowFurigana(e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span>显示注音</span>
              </label>
            </div>

            <button
              onClick={handleRefresh}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all"
            >
              <RefreshCw className="h-4 w-4" />
              <span className="hidden md:inline">刷新</span>
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 max-w-full">
        {articles.map((article) => (
          <NewsCard 
            key={article.id} 
            article={article} 
            onRead={() => setSelectedArticle(article)}
            showContent={showContent}
            showFurigana={showFurigana}
          />
        ))}
      </div>

      {articles.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Bookmark className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到相关新闻</h3>
          <p className="text-gray-600 mb-4 break-words">
            尝试调整搜索条件，或运行抓取器获取新内容。
          </p>
          <div className="space-x-2">
            <button 
              onClick={handleRefresh}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              刷新文章列表
            </button>
          </div>
        </div>
      )}

      {totalPages > 1 && (
        <div className="mt-8 space-y-4">
          {/* Traditional pagination */}
          <div className="flex justify-center items-center space-x-4">
            <Link
              href={createPageURL(currentPage - 1)}
              className={`px-4 py-2 border rounded-lg ${currentPage <= 1 ? 'pointer-events-none text-gray-400 bg-gray-100' : 'text-gray-700 bg-white hover:bg-gray-50'}`}
              aria-disabled={currentPage <= 1}
              tabIndex={currentPage <= 1 ? -1 : undefined}
            >
              上一页
            </Link>

            <span className="text-sm text-gray-700">
              第 {currentPage} 页 / 共 {totalPages} 页
            </span>

            <Link
              href={createPageURL(currentPage + 1)}
              className={`px-4 py-2 border rounded-lg ${currentPage >= totalPages ? 'pointer-events-none text-gray-400 bg-gray-100' : 'text-gray-700 bg-white hover:bg-gray-50'}`}
              aria-disabled={currentPage >= totalPages}
              tabIndex={currentPage >= totalPages ? -1 : undefined}
            >
              下一页
            </Link>
          </div>

          {/* Page jumper */}
          <div className="flex justify-center">
            <PageJumper
              currentPage={currentPage}
              totalPages={totalPages}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default NewsReader;
