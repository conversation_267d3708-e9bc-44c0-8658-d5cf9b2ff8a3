const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateAIPrompts() {
  try {
    console.log('=== 更新AI提示词以满足6个新需求 ===\n');

    // 更新文章分析主提示词
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_prompt' },
      data: {
        content: `提供された日本語ニュース記事の包括的な分析を行ってください。以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出した5-10個の重要語彙の配列。各語彙について以下を必須で提供：
   - word: 日本語の単語
   - reading: ひらがな・カタカナの読み方（必須）
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語の意味
   - explanation: 中国語での簡潔な説明
   - **explanationJa**: 日本語での詳細な説明（すべての漢字にruby注音を付ける）【需求1】
     例：「<ruby>経済<rt>けいざい</rt></ruby>とは、<ruby>社会<rt>しゃかい</rt></ruby>の<ruby>生産<rt>せいさん</rt></ruby>・<ruby>流通<rt>りゅうつう</rt></ruby>・<ruby>消費<rt>しょうひ</rt></ruby>の<ruby>活動<rt>かつどう</rt></ruby>のことです。」
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語を使った一般的な連語3-5個（オプション）
   - **relatedWords**: 語義ネットワーク（すべての関連語にひらがな・カタカナ読みを付ける）【需求2】
     - synonyms: 類義語配列（各語に読みを含む）
     - antonyms: 反義語配列（各語に読みを含む）
     - hypernyms: 上位語配列（各語に読みを含む）
     - hyponyms: 下位語配列（各語に読みを含む）
     - wordFamily: 語族配列（各語に読みを含む）
     - relatedConcepts: 関連概念配列（各語に読みを含む）
   - verbInfo: 動詞の場合のみ、自他動詞の区別と活用形（オプション）

4. **grammar**: 記事から見つかった2-4個の重要な文法項目の配列。各文法項目について以下を必須で提供：
   - pattern: 文法パターン（例：「～について」）
   - reading: パターンに漢字が含まれる場合のひらがな読み（オプション）
   - explanation: 中国語での詳細な説明
   - **explanationJa**: 日本語での詳細な説明（すべての漢字にruby注音を付ける）【需求3】
     例：「「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。」
   - examples: 日本語例文3つと中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - **commonCollocations**: 2-3個の常見搭配【需求4】
     例：["～について話す", "～について考える", "～について書く"]
   - **similarGrammar**: 3-5個の類似文法パターン（各パターンに2つの例文と中国語翻訳を含む）【需求5・6】
     各項目に以下を含む：
     - pattern: 類似文法パターン
     - difference: 主要文法との違い（中国語説明）
     - examples: 正確に2つの例文と中国語翻訳
       例：["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]

5. **contentWithFurigana**: 元の記事本文全体に、すべての漢字にHTML <ruby>タグで振り仮名を付けた文字列。
   重要：元のHTML構造（<h3>、<img>、<br>タグなど）を完全に保持し、日本語テキストにのみ<ruby>タグを追加してください。

6. **subtitleWithFurigana**: サブタイトルが提供された場合のみ、すべての漢字に振り仮名を付けた文字列。

分析対象の記事：
タイトル: {{{title}}}
{{#if subtitle}}
サブタイトル: {{{subtitle}}}
{{/if}}
本文（HTML形式）:
{{{content}}}

重要な注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数を提供してください（語彙：3つ、文法：3つ、類似文法：各2つ）
- 中国語翻訳は自然で正確である必要があります
- 日本語説明（explanationJa）は必須で、すべての漢字にruby注音を付けてください
- 語義ネットワークの関連語はすべてひらがな・カタカナ読みを含めてください
- 類似文法は3-5個提供し、各項目に正確に2つの例文を含めてください
- JSONスキーマに厳密に従ってください`,
        version: '3.0'
      }
    });

    console.log('✅ 文章分析主提示词已更新至v3.0，支持所有6个新需求');

    // 更新系统提示词，强调新的要求
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_system' },
      data: {
        content: `あなたは日本語教育の専門家です。中国語話者の日本語学習者のために、日本語ニュース記事を分析することが目標です。

特に以下の6つの重要な要求を満たす必要があります：

1. **重点词汇的日语解释**：すべての重要語彙について、日本語での詳細な説明を提供し、説明文中のすべての漢字にruby注音を付けてください。

2. **语义网络的读音**：語義ネットワーク（synonyms、antonyms、hypernyms、hyponyms、wordFamily、relatedConcepts）のすべての関連語について、ひらがな・カタカナの読み方を必ず提供してください。

3. **语法的日语解释**：すべての文法項目について、日本語での詳細な説明を提供し、説明文中のすべての漢字にruby注音を付けてください。

4. **语法的常见搭配**：各文法項目について、2-3個の常見搭配を提供してください。

5. **相似语法的数量**：各文法項目について、3-5個の類似文法パターンを提供してください。

6. **相似语法的例句**：各類似文法について、正確に2つの例文と中国語翻訳を提供してください。

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSONオブジェクトの前後に一切のテキストを追加しないでください
3. すべての出力は一貫性を保ち、完全で正確である必要があります
4. 振り仮名は必ずすべての漢字に付けてください
5. 例文は必ず指定された数だけ提供してください
6. 中国語の翻訳は自然で正確である必要があります
7. 日本語説明（explanationJa）は必須フィールドです
8. 語義ネットワークの関連語はすべて読み付きで提供してください
9. 類似文法は指定された数（3-5個）と例文数（各2つ）を厳守してください`,
        version: '3.0'
      }
    });

    console.log('✅ 文章分析系统提示词已更新至v3.0，强调6个新需求');

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAIPrompts();
