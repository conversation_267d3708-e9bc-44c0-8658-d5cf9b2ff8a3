'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useState } from 'react';

export default function DevTools() {
  const { data: session, status } = useSession();
  const [isVisible, setIsVisible] = useState(false);

  // 只在开发模式下显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* 开发工具触发按钮 */}
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(!isVisible)}
          className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full shadow-lg transition-colors"
          title="开发工具"
        >
          🛠️
        </button>
      </div>

      {/* 开发工具面板 */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-gray-800">开发工具</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* 当前登录状态 */}
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-semibold text-sm text-gray-700 mb-2">登录状态</h4>
              {status === 'loading' && (
                <p className="text-sm text-gray-600">加载中...</p>
              )}
              {status === 'authenticated' && session && (
                <div className="text-sm">
                  <p className="text-green-600">✅ 已登录</p>
                  <p className="text-gray-600">用户: {session.user?.name || session.user?.email}</p>
                  <p className="text-gray-600">角色: {(session.user as any)?.role || '未知'}</p>
                </div>
              )}
              {status === 'unauthenticated' && (
                <p className="text-sm text-red-600">❌ 未登录</p>
              )}
            </div>

            {/* 快速登录按钮 */}
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">快速操作</h4>
              
              {status === 'unauthenticated' && (
                <button
                  onClick={() => signIn('credentials', {
                    email: '<EMAIL>',
                    password: 'dev',
                    redirect: false
                  })}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm transition-colors"
                >
                  以管理员身份登录
                </button>
              )}

              {status === 'authenticated' && (
                <button
                  onClick={() => signOut({ redirect: false })}
                  className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm transition-colors"
                >
                  退出登录
                </button>
              )}
            </div>

            {/* 环境信息 */}
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-semibold text-sm text-gray-700 mb-2">环境信息</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <p>模式: {process.env.NODE_ENV}</p>
                <p>自动登录: {process.env.NEXT_PUBLIC_DEV_AUTO_LOGIN === 'true' ? '启用' : '禁用'}</p>
              </div>
            </div>

            {/* 说明 */}
            <div className="text-xs text-gray-500 bg-yellow-50 p-2 rounded">
              <p className="font-semibold mb-1">💡 提示:</p>
              <p>在 .env 文件中设置 DEV_AUTO_LOGIN="false" 可以禁用自动登录</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
