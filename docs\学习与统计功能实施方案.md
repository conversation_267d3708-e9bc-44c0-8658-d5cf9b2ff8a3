
# 学习与统计功能 - 详细实施方案

## 1. 概述

本文档整合并细化了“学习行为记录”、“SRS学习复习”和“学习统计”三大关联功能的完整实施方案。目标是提供一个从后端数据库到前端交互的、清晰可执行的开发路线图。

## 2. 核心数据流

用户行为 -> [Server Action] -> 数据库 (`user_learning_records`) -> [Server Action] -> 功能模块 (学习统计 / SRS复习)

## 实施阶段一：后端与数据库基础建设 (Backend & Database Foundation)

此阶段的目标是搭建所有上层功能所需的数据模型和服务器端逻辑。

1.  **数据库升级 (已完成)**
    *   **已实现**：`user_learning_records` 表现在包含完整的 FSRS 字段：
        *   `status`: TEXT (new, learning, mastered) - 核心状态。
        *   `fsrs_difficulty`: REAL - FSRS 难度参数。
        *   `fsrs_stability`: REAL - FSRS 稳定性参数。
        *   `fsrs_reps`: INTEGER - FSRS 复习次数。
        *   `fsrs_due`: DATETIME - FSRS 下次复习时间。
        *   以及其他 FSRS 相关字段
    *   **状态**: ✅ 已完成，使用先进的 FSRS 算法

2.  **数据库接口层 (`src/lib/server/database.ts`)**
    *   **创建/更新**以下函数：
        *   `updateLearningProgress(data, userId)`: 实现**UPSERT**逻辑。根据 `userId` 和 `vocabulary_id`/`grammar_point_id`/`article_id` 更新或插入学习记录。这是所有学习行为的唯一入口。
        *   `getStudyQueue(userId)`: 实现SRS卡片队列的获取逻辑。
            *   查询 `status = 'new'` 的条目作为“新卡片队列”。
            *   查询 `status = 'learning'` 且 `next_review_at <= today` 的条目作为“到期复习队列”。
            *   返回 `{ newItems: [...], reviewItems: [...] }`。
        *   `submitReview(recordId, rating)`: 实现FSRS算法的核心逻辑。
            *   接收 `user_learning_records` 的 `id` 和用户评分 `rating`。
            *   根据FSRS算法计算新的稳定性、难度和下次复习时间。
            *   自动判断是否达到掌握标准。
            *   更新数据库中的记录。
        *   `getLearningStats(userId)`: 实现统计仪表盘的核心数据查询，一次性获取“连续学习天数”、“已学词汇”、“已掌握”、“已读文章”等指标。
        *   `getUserProgress(userId)`: 实现按N级分类的进度统计查询。

3.  **服务层 (`src/app/actions.ts`)**
    *   **创建/更新**以下Server Actions：
        *   `updateLearningProgressAction`: 接收前端的学习行为，调用`dbManager.updateLearningProgress`，并使用`revalidatePath`刷新相关页面。
        *   `getStudyQueueAction`: 调用`dbManager.getStudyQueue`，为SRS复习页面提供数据。
        *   `submitReviewAction`: 调用`dbManager.submitReview`，处理用户的复习反馈。
        *   `getLearningStatsAction`: 调用`dbManager.getLearningStats`。
        *   `getUserProgressAction`: 调用`dbManager.getUserProgress`。

## 实施阶段二：真实学习统计功能实现 (Learning Statistics Implementation)

此阶段将用真实数据替换现有的模拟统计数据。

1.  **后端数据获取 (`src/app/(main)/dashboard/page.tsx`)**
    *   **改造**: 将此页面重构为**客户端组件** (`'use client'`)。
    *   **逻辑**:
        *   使用 `useEffect` 在组件加载时，根据当前登录用户的 `userId`，分别调用 `getLearningStatsAction` 和 `getUserProgressAction`。
        *   引入 `loading` 和 `error` 状态管理。

2.  **前端UI展示 (`src/components/Dashboard.tsx`)**
    *   **改造**: 组件现在通过 `props` 接收从父页面获取的 `stats` 和 `progress` 数据。
    *   **移除**: 删除所有硬编码的假数据。
    *   **实现**:
        *   根据 `loading` 状态显示骨架屏或加载指示器。
        *   根据 `error` 状态显示错误提示和重试按钮。
        *   将 `props` 传入的真实数据绑定到各个UI元素。

## 实施阶段三：真实SRS学习/复习功能实现 (SRS Feature Implementation)

此阶段将把“学习/复习”页面原型连接到真实的后端逻辑。

1.  **数据获取 (`src/components/StudySession.tsx`)**
    *   **改造**: 组件将变为**客户端组件**。
    *   **逻辑**:
        *   在 `useEffect` 中，调用 `getStudyQueueAction` 获取当天的 `newItems` 和 `reviewItems`。
        *   将两个队列合并，并设置好 `newCount` 和 `reviewCount` 的状态。
        *   管理 `loading` 和 `error` 状态。

2.  **交互逻辑 (`src/components/StudySession.tsx`)**
    *   **改造**:
        *   移除所有模拟数据。
        *   当用户点击“重来”、“困难”、“良好”、“轻松”等反馈按钮时，调用 `submitReviewAction`，并将当前卡片的 `recordId` 和对应的 `quality` 值传给后端。
        *   在 `submitReviewAction` 成功返回后，再切换到下一张卡片。

## 实施阶段四：学习行为记录入口集成 (Learning Behavior Integration)

此阶段确保用户的学习行为能被准确记录，为统计和复习提供数据源。

1.  **文章阅读器 (`src/components/ArticleReader.tsx`)**
    *   **实现**: 在“重点词汇”和“语法解析”列表中，为每个项目添加一组状态切换按钮（如：“学习中”、“已掌握”）。
    *   **连接**: 这些按钮的 `onClick` 事件将调用 `updateLearningProgressAction`，传入对应的 `vocabulary_id`/`grammar_point_id` 和新的 `status`。

2.  **生词本/语法本 (`src/components/VocabularyBank.tsx`, `src/components/GrammarBank.tsx`)**
    *   **强化**: 确保这两个页面中的状态切换按钮也正确地调用了 `updateLearningProgressAction`。

## 5. 测试验证

1.  **单元测试**: 重点测试 `database.ts` 中的SRS算法计算和SQL查询逻辑的准确性。
2.  **集成测试**: 验证从前端UI操作 -> Server Action -> 数据库更新 -> 数据重新获取 -> UI刷新的完整链路是否通畅。
3.  **用户流程测试**:
    *   从文章中收藏一个新词，确认它出现在“学习/复习”页面的新卡片队列中。
    *   完成一次学习会话，确认卡片状态变为 `learning`，并计算出正确的 `next_review_at` 日期。
    *   确认“学习统计”页面的各项数据会随着用户的学习行为而实时更新。
