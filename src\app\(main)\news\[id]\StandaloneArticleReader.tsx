'use client';

import { useRouter } from 'next/navigation';
import ArticleReader from '@/components/ArticleReader';

interface StandaloneArticleReaderProps {
  article: any;
}

export default function StandaloneArticleReader({ article }: StandaloneArticleReaderProps) {
  const router = useRouter();

  const handleBack = () => {
    router.push('/');
  };

  return (
    <ArticleReader 
      article={article} 
      onBack={handleBack}
      standalone={true}
    />
  );
}
