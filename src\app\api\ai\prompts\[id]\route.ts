import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// PUT /api/ai/prompts/[id] - 更新AI提示词
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的提示词ID' },
        { status: 400 }
      );
    }

    const data = await request.json();
    const prompt = await dbManager.updateAIPrompt(id, data);
    return NextResponse.json(prompt);
  } catch (error: any) {
    console.error('更新AI提示词失败:', error);
    return NextResponse.json(
      { error: '更新AI提示词失败', details: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/ai/prompts/[id] - 删除AI提示词
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的提示词ID' },
        { status: 400 }
      );
    }

    await dbManager.deleteAIPrompt(id);
    return NextResponse.json({ success: true, message: '提示词已删除' });
  } catch (error: any) {
    console.error('删除AI提示词失败:', error);
    return NextResponse.json(
      { error: '删除AI提示词失败', details: error.message },
      { status: 500 }
    );
  }
}

// GET /api/ai/prompts/[id] - 获取单个AI提示词
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的提示词ID' },
        { status: 400 }
      );
    }

    const prompt = await dbManager.getAIPromptById(id);
    if (!prompt) {
      return NextResponse.json(
        { error: '提示词不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json(prompt);
  } catch (error: any) {
    console.error('获取AI提示词失败:', error);
    return NextResponse.json(
      { error: '获取AI提示词失败', details: error.message },
      { status: 500 }
    );
  }
}
