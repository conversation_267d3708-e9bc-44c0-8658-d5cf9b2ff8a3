import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  // 开发模式下的自动登录处理
  if (process.env.NODE_ENV === 'development' && process.env.DEV_AUTO_LOGIN === 'true') {
    const { pathname } = request.nextUrl;
    
    // 如果访问登录页面，直接重定向到主页
    if (pathname === '/login') {
      return NextResponse.redirect(new URL('/', request.url));
    }
    
    // 对于其他受保护的页面，允许访问（因为我们假设用户已登录）
    return NextResponse.next();
  }

  // 正常的认证流程
  const token = await getToken({ req: request });
  const { pathname } = request.nextUrl;

  // 如果用户已登录且访问登录页面，重定向到主页
  if (token && pathname === '/login') {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // 如果用户未登录且访问受保护的页面，重定向到登录页面
  if (!token && pathname !== '/login' && !pathname.startsWith('/api/auth')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
