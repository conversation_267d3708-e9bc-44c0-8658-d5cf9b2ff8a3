
# SRS学习与复习功能设计方案

## 1. 需求背景

当前系统虽然提供了“生词本”和“语法本”，但其功能主要停留在“收藏”和“状态跟踪”（如：学习中、已掌握）。这种被动的管理方式无法主动帮助用户对抗遗忘曲线，学习效率不高。

为了将本平台打造为真正高效的语言学习工具，我们迫切需要引入一个主动、智能的复习系统。Anki等记忆软件的成功已经证明，基于**间隔重复系统（Spaced Repetition System, SRS）**的算法是帮助用户实现长期记忆最有效的方法之一。

**核心目标**：实现一个类似Anki的、基于SRS算法的智能学习与复习功能，帮助用户科学、高效地初次学习新知识点，并对其进行长期复习和记忆。

## 2. 核心设计思想

- **学习与复习分离 (Learning vs. Reviewing)**: 一个知识点的生命周期分为两个核心阶段：
    - **学习 (Learning)**: 用户初次接触一个“新”的知识点。此阶段的目标是让用户形成初步记忆。
    - **复习 (Reviewing)**: 用户对一个已经“学习中”的知识点，根据SRS算法进行后续的巩固复习。

- **统一学习会话 (Unified Study Session)**: 用户无需在不同页面间切换。系统提供一个统一的“学习/复习中心”。每天，系统会构建一个包含“新卡片”和“到期复习卡片”的混合队列。**通常，会话会优先展示所有新卡片，待新卡片学习完毕后，再开始复习到期的卡片。**

- **用户反馈驱动 (User Feedback Driven)**: 复习效果完全由用户自己判断。用户在每次复习后，通过简单的按钮（如“重来”、“困难”、“良好”、“轻松”）来评价自己对知识点的掌握程度，系统根据此反馈来调整后续的复习计划。

- **无缝集成 (Seamless Integration)**: 新的复习系统将与现有的“生词本”和“语法本”无缝集成。用户可以将任何词汇或语法点加入学习计划，然后由SRS系统接管其后续的复习周期。

## 3. 核心算法：FSRS间隔重复系统

我们已经实现了先进的 FSRS (Free Spaced Repetition Scheduler) 算法，这是比传统 SM-2 算法更科学的记忆算法。

### 3.1 用户反馈

在每次复习（当卡片答案显示后），用户需要通过点击四个按钮之一来提供反馈：

1.  **重来 (Again)** - 完全不记得或记错了。
2.  **困难 (Hard)** - 思考了很久才记起来，过程很困难。
3.  **良好 (Good)** - 顺利地记起来了。
4.  **轻松 (Easy)** - 不假思索地就记起来了。

这四个按钮将对应 FSRS 算法的四个评分等级：`Again`, `Hard`, `Good`, `Easy`。

### 3.2 关键参数

FSRS 算法使用以下关键参数（存储在`user_learning_records`表中）：

- **`repetitions (n)`**: 整数，表示该项目被成功复习的次数。新项目为0。
- **`ease_factor (EF)`**: 浮点数，表示项目的“简易度”。初始值统一为2.5。EF越高，下次复习的间隔增长越快。
- **`interval (I)`**: 整数，表示当前复习间隔的天数。

### 3.3 算法逻辑

当用户提交一次复习反馈（回忆质量分 `q`）后，系统将按以下逻辑更新SRS参数：

1.  **如果用户回答“重来” (q < 3)**:
    -   用户的记忆失败了，需要重新开始学习这个项目。
    -   将重复次数重置为0 (`n = 0`)。
    -   将复习间隔重置为一个很短的时间（例如，10分钟后再次出现）。

2.  **如果用户回答“困难”、“良好”或“轻松” (q >= 3)**:
    -   用户成功回忆起来了，计算下一次的复习间隔。
    -   **首次成功复习 (n = 0)**:
        -   `I(1) = 1` (1天后复习)
    -   **第二次成功复习 (n = 1)**:
        -   `I(2) = 6` (6天后复习)
    -   **后续成功复习 (n > 1)**:
        -   `I(n) = I(n-1) * EF` (上一次的间隔 x 简易度)
    -   **增加重复次数**: `n = n + 1`

3.  **更新简易度 (EF)**:
    -   在每次成功回忆后，都需要根据用户的反馈微调简易度。
    -   `EF_new = EF + [0.1 - (5 - q) * (0.08 + (5 - q) * 0.02)]`
    -   确保 `EF` 的值永远不低于1.3。
    -   这个公式的核心思想是：回答“轻松”会增加EF，回答“困难”会降低EF。

4.  **计算下次复习日期**:
    -   `next_review_date = 当前日期 + I(n)`

## 4. 数据库结构变更

为了支持SRS算法，我们需要对 `user_learning_records` 表进行扩展。

**`user_learning_records` 表 - 新增/修改字段:**

```sql
CREATE TABLE user_learning_records (
  -- ... (现有字段保持不变) ...
  
  -- 学习状态: new, learning, mastered
  -- 'new' 状态的条目将进入“学习队列”
  -- 'learning' 状态的条目将进入“复习队列”
  -- 'mastered' 状态的条目可以被视为毕业，暂时不进入复习队列
  status TEXT DEFAULT 'new',
  
  -- SRS 核心字段 (新增)
  ease_factor REAL DEFAULT 2.5,  -- 简易度因子
  interval INTEGER DEFAULT 0,    -- 复习间隔天数
  repetitions INTEGER DEFAULT 0, -- 成功复习次数
  
  -- 其他字段
  last_reviewed_at DATETIME,     -- 最后一次复习的时间
  next_review_at DATETIME,       -- [用途变更] 精确的下次复习日期
  
  -- ... (其他现有字段保持不变) ...
);
```

## 5. 后端实现方案 (Server Actions)

需要创建两个新的Server Action来驱动复习功能。

1.  **`getStudyQueueAction(userId: string)`**:
    -   **职责**: 获取用户当天的“新卡片队列”和“到期复习队列”。
    -   **逻辑**:
        1.  **查询新卡片**:
            -   `SELECT ... FROM user_learning_records WHERE user_id = ? AND status = 'new' ORDER BY created_at ASC LIMIT 10` (例如，每天最多10个新卡片)。
        2.  **查询复习卡片**:
            -   `SELECT ... FROM user_learning_records WHERE user_id = ? AND status = 'learning' AND next_review_at <= CURRENT_DATE`。
        3.  将查询结果与 `vocabulary` 和 `grammar_points` 表进行关联（JOIN），以获取项目的完整内容。
        4.  返回一个包含两个数组的对象：`{ newItems: [...], reviewItems: [...] }`。

2.  **`submitReviewAction(recordId: number, quality: number)`**:
    -   **职责**: 处理用户的单次复习结果，并更新该项目的下一次复习计划。
    -   **逻辑**:
        1.  接收 `user_learning_records` 的 `id` 和用户给出的回忆质量分 `q` (1-4)。
        2.  从数据库中获取该 `id` 对应的当前SRS参数（`n`, `EF`, `I`, `status`）。
        3.  **如果卡片是新卡片 (`status = 'new'`) 且用户回答的不是“重来”**，则将其状态更新为 `'learning'`。
        4.  应用第3节中描述的SRS算法，计算出新的 `n'`, `EF'`, `I'`。
        5.  计算出新的 `next_review_at` 日期。
        6.  `UPDATE` 数据库中的这条记录，保存新的SRS参数和状态。

## 6. 前端实现方案

1.  **新增 “学习/复习” 页面**:
    -   在侧边栏添加入口。
    -   页面加载时，调用 `getStudyQueueAction` 获取当天的两个队列，并显示待办数量（例如：“新卡片: 10, 待复习: 25”）。

2.  **创建 `Flashcard` 复习组件**:
    -   这是学习页面的核心UI。
    -   **状态管理**:
        -   `studyQueue`: 存储一个合并后的学习队列，通常是 `[...newItems, ...reviewItems]`。
        -   `currentIndex`: 当前正在学习的卡片索引。
        -   `isAnswerVisible`: 控制是显示问题还是答案。
    -   **UI布局 (闪卡)**:
        -   **问题面**: 显示单词或语法模式（例如：“技術”）。
        -   **答案面**: 显示完整的信息，包括读音、释义、例句等。
    -   **交互流程**:
        1.  用户进入页面，组件显示队列中第一张卡片的问题面和“显示答案”按钮。
        2.  用户在心中回忆答案，然后点击“显示答案”。
        3.  UI翻转到答案面，同时在下方显示“重来”、“困难”、“良好”、“轻松”四个按钮。
        4.  用户根据自己的回忆情况，点击其中一个按钮。
        5.  `onClick` 事件触发，调用 `submitReviewAction`，并将`recordId`和对应的`quality`值传给后端。
        6.  后端处理完成后，前端的 `currentIndex` 加一，自动切换到下一张卡片。
        7.  当所有卡片学习完毕，显示一个“今日任务已完成！”的祝贺界面。

## 7. 分步实施计划

1.  **第一阶段：数据库升级**
    -   修改 `scripts/init-new-db-optimized.js` 脚本，为 `user_learning_records` 表添加 `ease_factor`, `interval`, `repetitions` 字段，并确保`status`和`next_review_at`字段存在。
2.  **第二阶段：后端服务开发**
    -   在 `src/lib/server/database.ts` 中添加获取和更新SRS参数的数据库操作函数。
    -   在 `src/app/actions.ts` 中实现 `getStudyQueueAction` 和 `submitReviewAction`。
3.  **第三阶段：前端UI开发**
    -   在侧边栏 `Sidebar.tsx` 中添加入口。
    -   创建新的 `/study` 页面路由。
    -   开发核心的 `Flashcard` 组件，实现完整的UI和交互逻辑。
4.  **第四阶段：集成与测试**
    -   将前端页面与后端Action连接起来。
    -   全面测试学习和复习流程，验证SRS算法的计算是否准确，状态转换是否正常。
    -   优化用户体验，例如添加复习进度条、快捷键支持等。

通过以上方案，我们可以构建一个功能强大、符合记忆科学的智能学习与复习系统，将平台的学习价值提升到一个新的高度。
