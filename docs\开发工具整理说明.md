# 开发工具整理说明

## 概述

为了改善用户体验，将原本分散在左侧边栏中的多个测试功能页面整合到一个统一的"开发工具"页面中。

## 改动内容

### 1. 移除的侧边栏菜单项

以下测试功能菜单项已从左侧边栏移除：

- 统计原型 (`/dashboard-prototype`)
- 图表测试 (`/charts-test`) 
- 设置演示 (`/settings-demo`)
- 学习调试 (`/debug-study`)
- 持续学习测试 (`/test-continuous-study`)
- 提示词测试 (`/admin/prompt-test`)
- Ruby标注测试 (`/test-ruby`)

### 2. 新增开发工具页面

创建了新的开发工具管理页面：

- **路径**: `/dev-tools`
- **文件**: `src/app/(main)/dev-tools/page.tsx`
- **权限**: 仅管理员可见

### 3. 侧边栏简化

左侧边栏现在只保留以下菜单项：

**所有用户可见：**
- 新闻阅读
- 学习统计  
- 学习/复习
- 学习模式
- 生词本
- 语法本
- AI助教
- 设置

**管理员专用：**
- RSS源管理
- 抓取管理
- **开发工具** (新增)

## 开发工具页面功能

### 工具分类

1. **原型设计**
   - 统计原型：学习统计面板的原型设计和功能测试
   - 设置演示：设置页面的UI组件和交互演示

2. **功能测试**
   - 图表测试：各种图表组件的展示和测试页面
   - 持续学习测试：持续学习算法和流程的测试页面
   - 提示词测试：AI提示词的测试和优化工具
   - Ruby标注测试：日语假名标注功能的测试页面

3. **调试工具**
   - 学习调试：学习功能的调试和测试工具

### 状态标识

- **稳定**：可用于生产环境的功能
- **实验性**：仅供测试使用的功能
- **已废弃**：不再维护的功能

## 使用方式

1. 以管理员身份登录系统
2. 在左侧边栏点击"开发工具"
3. 在开发工具页面中选择需要的测试功能
4. 点击对应的工具卡片即可跳转到相应的测试页面

## 优势

1. **简化界面**：左侧边栏更加简洁，提升用户体验
2. **统一管理**：所有开发和测试工具集中在一个页面
3. **分类清晰**：按功能类型和状态对工具进行分类
4. **易于维护**：新增测试功能时只需在开发工具页面添加即可

## 文件变更

### 新增文件
- `src/app/(main)/dev-tools/page.tsx` - 开发工具管理页面

### 修改文件  
- `src/components/Sidebar.tsx` - 简化侧边栏菜单配置

### 保留文件
所有原有的测试页面文件都保持不变，只是访问方式改为通过开发工具页面进入。

## 注意事项

- 开发工具页面仅对管理员可见
- 所有原有的测试功能页面路径保持不变
- 可以通过直接访问URL的方式继续使用这些测试功能
- 如需添加新的测试工具，请在开发工具页面的配置中添加相应条目
