import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';
import { cookies } from 'next/headers';

// GET /api/ai/chat/sessions - 获取用户的聊天会话列表
export async function GET() {
  try {
    // 获取当前用户ID（临时使用管理员用户）
    const userId = 1; // TODO: 从认证系统获取真实用户ID
    
    const sessions = await dbManager.getChatSessions(userId);
    
    return NextResponse.json({
      success: true,
      data: sessions
    });
  } catch (error: any) {
    console.error('获取聊天会话失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取聊天会话失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST /api/ai/chat/sessions - 创建新的聊天会话
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { sessionType = 'qa', title, scenarioConfig } = body;
    
    // 获取当前用户ID（临时使用管理员用户）
    const userId = 1; // TODO: 从认证系统获取真实用户ID
    
    const session = await dbManager.createChatSession(
      userId, 
      sessionType, 
      title, 
      scenarioConfig
    );
    
    return NextResponse.json({
      success: true,
      data: session
    }, { status: 201 });
  } catch (error: any) {
    console.error('创建聊天会话失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '创建聊天会话失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}
