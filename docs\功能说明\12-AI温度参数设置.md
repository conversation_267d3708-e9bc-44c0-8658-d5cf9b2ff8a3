# AI温度参数设置功能

## 功能概述

AI温度参数设置功能允许管理员通过设置界面配置AI模型的温度参数，该参数控制AI回答的创造性和随机性。温度值保存在数据库中，所有AI处理（文章分析、语法分析、AI助教聊天）都会使用这个统一的温度设置。

## 温度参数说明

### 什么是温度参数？

温度（Temperature）是AI模型生成文本时的一个重要参数：

- **低温度值（0.0-0.3）**: 输出更确定、一致和保守
- **中等温度值（0.4-0.7）**: 平衡的创造性和一致性
- **高温度值（0.8-1.2）**: 更有创造性和多样性的输出
- **极高温度值（1.3-2.0）**: 高度创造性，但可能不够连贯

### 默认设置

- **默认温度值**: 0.7
- **推荐范围**: 0.2 - 1.0
- **系统限制**: 0.0 - 2.0

## 访问路径

- **URL**: `/settings` → AI设置部分
- **权限**: 仅管理员可访问
- **导航**: 设置页面 → AI设置

## 功能特性

### 1. 设置界面

#### 温度参数输入
- **输入类型**: 数字输入框
- **步长**: 0.1
- **范围**: 0.0 - 2.0
- **实时预览**: 显示当前模式（保守/平衡/创造/高创造）

#### 模式指示器
根据温度值显示不同的模式：
- **0.0-0.3**: 保守模式
- **0.4-0.7**: 平衡模式
- **0.8-1.2**: 创造模式
- **1.3-2.0**: 高创造模式

### 2. 数据存储

#### 数据库存储
- **表**: `system_settings`
- **键**: `ai_temperature`
- **值**: 浮点数字符串
- **默认值**: "0.7"

#### 设置管理
```typescript
// 获取温度设置
const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

// 保存温度设置
await dbManager.setSystemSetting('ai_temperature', temperature.toString());
```

### 3. AI集成

#### 文章分析流程
```typescript
// src/ai/flows/analyze-article-flow.ts
const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

const { output } = await analysisPrompt(promptData, { 
  model: modelToUse,
  config: {
    temperature: temperature
  }
});
```

#### 语法分析流程
```typescript
// src/ai/flows/grammar-analysis-flow.ts
const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

const { output } = await grammarAnalysisPrompt(promptData, { 
  model: modelToUse,
  config: {
    temperature: temperature
  }
});
```

#### AI助教聊天
```typescript
// src/ai/flows/tutor-chat-flow.ts
const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

const result = await ai.generate({
  model: modelToUse,
  messages: [...],
  system: systemContent,
  config: {
    temperature: temperature
  }
});
```

## 使用指南

### 1. 设置温度参数

1. **访问设置页面**
   - 登录管理员账户
   - 导航到设置页面
   - 找到"AI设置"部分

2. **调整温度值**
   - 在"AI温度参数"输入框中输入期望的值
   - 观察模式指示器的变化
   - 根据需求选择合适的模式

3. **保存设置**
   - 点击"保存设置"按钮
   - 等待保存成功的确认消息

### 2. 温度值选择建议

#### 文章分析场景
- **新闻翻译**: 0.3-0.5（需要准确性）
- **词汇解释**: 0.4-0.6（平衡准确性和可读性）
- **语法分析**: 0.2-0.4（需要精确性）

#### AI助教聊天场景
- **语法解释**: 0.4-0.6（清晰准确）
- **对话练习**: 0.6-0.8（自然多样）
- **创意写作**: 0.8-1.0（鼓励创造性）

#### 一般建议
- **生产环境**: 0.5-0.7（平衡质量和多样性）
- **测试环境**: 0.3-1.0（根据测试需求调整）

## 技术实现

### 1. 前端组件

#### 设置界面
```tsx
<div>
  <label htmlFor="aiTemperature">AI温度参数</label>
  <input
    id="aiTemperature"
    type="number"
    value={aiTemperature}
    onChange={(e) => setAITemperature(Number(e.target.value))}
    min="0"
    max="2"
    step="0.1"
  />
  <div>
    当前值: {aiTemperature} 
    {/* 模式指示器 */}
  </div>
</div>
```

### 2. 后端API

#### 保存温度设置
```typescript
export async function setAITemperatureAction(temperature: number) {
  if (temperature < 0 || temperature > 2) {
    throw new Error('温度值必须在0到2之间。');
  }
  await dbManager.setSystemSetting('ai_temperature', temperature.toString());
  revalidatePath('/settings');
  return { success: true, message: "AI温度设置已保存。" };
}
```

### 3. 数据库操作

#### 系统设置管理
```typescript
// 获取设置
async getSystemSetting(key: string) {
  return prisma.system_settings.findUnique({ where: { key } });
}

// 保存设置
async setSystemSetting(key: string, value: string) {
  return prisma.system_settings.upsert({
    where: { key },
    update: { value },
    create: { key, value },
  });
}
```

## 注意事项

### 1. 性能影响
- 温度参数不会显著影响AI处理速度
- 主要影响输出质量和一致性

### 2. 质量控制
- 过低的温度可能导致输出过于机械化
- 过高的温度可能导致输出不够连贯
- 建议在0.3-1.0范围内调整

### 3. 实时生效
- 设置保存后立即生效
- 影响所有新的AI处理请求
- 不影响已完成的分析结果

### 4. 备份建议
- 记录当前有效的温度设置
- 在调整前测试不同温度值的效果
- 保持设置的一致性

## 故障排除

### 1. 设置不生效
- 检查是否成功保存设置
- 确认AI处理器重新启动
- 查看控制台错误信息

### 2. 输出质量问题
- 尝试调整温度值
- 对比不同温度下的输出效果
- 参考推荐的温度范围

### 3. 数据库问题
- 检查system_settings表是否存在
- 确认ai_temperature记录是否正确保存
- 验证数据库连接状态

这个功能为AI系统提供了灵活的输出控制能力，让管理员可以根据具体需求调整AI的行为特性。
