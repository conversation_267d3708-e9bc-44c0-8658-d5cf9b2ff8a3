# 数据库备份功能说明

## 功能概述

数据库备份功能为NHK日语学习系统提供了完整的数据保护方案，包括自动备份、手动备份、备份文件管理和数据库文件下载等功能。

## 主要特性

### 1. 自动备份
- **时间间隔配置**：支持1小时到168小时（1周）的备份间隔设置
- **数据一致性保证**：使用SQLite的VACUUM INTO命令确保备份时的数据完整性
- **后台调度**：自动在后台运行，无需手动干预
- **智能清理**：自动清理超过保留数量的旧备份文件

### 2. 手动备份
- **立即备份**：点击按钮即可立即创建数据库备份
- **压缩存储**：备份文件自动压缩为ZIP格式，节省存储空间
- **时间戳命名**：备份文件按照 `nhk_news_new_yyyy-mm-dd_hhmmss.zip` 格式命名

### 3. 备份文件管理
- **文件列表**：显示所有备份文件的详细信息（文件名、大小、创建时间）
- **下载功能**：支持下载任意备份文件到本地
- **删除功能**：支持删除不需要的备份文件
- **刷新列表**：手动刷新备份文件列表

### 4. 数据库文件下载
- **原始数据库下载**：支持直接下载当前的数据库文件
- **时间戳命名**：下载的数据库文件自动添加时间戳

## 使用指南

### 访问备份功能
1. 进入系统管理页面
2. 点击"抓取管理"菜单
3. 选择"数据管理"标签页
4. 在"数据库备份"部分进行操作

### 配置自动备份
1. 在"自动备份设置"区域：
   - 勾选"启用自动备份"复选框
   - 设置备份间隔（小时）：推荐24小时
   - 设置保留文件数：推荐10个
2. 点击"保存设置"按钮

### 手动创建备份
1. 点击"立即备份"按钮
2. 系统会显示创建进度
3. 备份完成后会显示成功消息
4. 新备份文件会出现在文件列表中

### 管理备份文件
1. **下载备份**：点击备份文件右侧的"下载"按钮
2. **删除备份**：点击备份文件右侧的"删除"按钮（需要确认）
3. **刷新列表**：点击"刷新列表"按钮更新文件列表

### 下载数据库文件
1. 在"数据管理"区域点击"下载数据库"按钮
2. 系统会自动下载当前的数据库文件

## 技术实现

### 数据一致性保证
备份功能使用SQLite的`VACUUM INTO`命令来创建备份，这确保了：
- **原子性**：备份过程是原子操作，不会产生不一致的状态
- **完整性**：备份文件包含完整的数据库结构和数据
- **压缩**：自动清理数据库碎片，减小备份文件大小

### 文件存储结构
```
data/
├── nhk_news_new.db          # 主数据库文件
└── backups/                 # 备份文件目录
    ├── nhk_news_new_2024-01-15_143022.zip
    ├── nhk_news_new_2024-01-14_143022.zip
    └── ...
```

### 自动备份调度
- 使用Node.js的`setInterval`实现定时调度
- 在应用启动时自动启动调度器
- 支持动态重启以应用新的设置

## 安全考虑

### 文件安全
- 备份文件存储在服务器本地，不会暴露给外部
- 下载时进行文件名验证，防止路径遍历攻击
- 删除操作需要确认，防止误删

### 数据保护
- 备份过程不会影响正常的数据库操作
- 使用事务确保数据一致性
- 自动清理机制防止磁盘空间耗尽

## 故障排除

### 常见问题

**Q: 自动备份没有运行**
A: 检查以下项目：
1. 确认已启用自动备份
2. 检查备份间隔设置是否合理
3. 查看服务器日志是否有错误信息

**Q: 备份文件创建失败**
A: 可能的原因：
1. 磁盘空间不足
2. 数据库文件被锁定
3. 权限不足

**Q: 无法下载备份文件**
A: 检查：
1. 备份文件是否存在
2. 服务器权限设置
3. 浏览器下载设置

### 日志查看
备份相关的日志信息会输出到服务器控制台，包括：
- 备份创建成功/失败信息
- 自动清理结果
- 调度器启动/停止状态

## 最佳实践

### 备份策略建议
1. **备份频率**：建议每24小时备份一次
2. **保留数量**：建议保留7-10个备份文件
3. **定期检查**：定期检查备份文件的完整性
4. **异地备份**：重要数据建议下载到本地或云存储

### 维护建议
1. 定期清理过期备份文件
2. 监控磁盘空间使用情况
3. 测试备份文件的可用性
4. 记录重要的备份时间点

## API接口

### 备份管理API
- `POST /api/database/backup` - 创建备份
- `GET /api/database/backup` - 获取备份列表
- `DELETE /api/database/backup/[filename]` - 删除备份文件
- `GET /api/database/backup/[filename]` - 下载备份文件

### 设置管理API
- `GET /api/database/backup/settings` - 获取备份设置
- `POST /api/database/backup/settings` - 更新备份设置

### 调度器管理API
- `GET /api/database/backup/scheduler` - 获取调度器状态
- `POST /api/database/backup/scheduler` - 重启调度器
