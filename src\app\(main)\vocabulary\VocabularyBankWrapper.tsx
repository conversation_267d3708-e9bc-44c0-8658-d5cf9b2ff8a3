'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import VocabularyBank from '@/components/VocabularyBank';
import { getVocabularyAction, getVocabularyStatsAction } from '@/app/actions';
import { useAuth } from '@/contexts/AuthContext';
import { dbManager } from '@/lib/server/database';
import { Loader } from 'lucide-react';

function LoadingFallback() {
  return (
    <div className="flex h-full items-center justify-center py-12">
      <div className="text-center">
        <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
        <p className="text-gray-600">正在加载词汇库...</p>
      </div>
    </div>
  );
}

export default function VocabularyBankWrapper() {
  const searchParams = useSearchParams();
  const { session, status: authStatus } = useAuth();

  const [vocabulary, setVocabulary] = useState<any>(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [vocabularyPerPage, setVocabularyPerPage] = useState(20);
  
  const level = searchParams.get('level') || 'all';
  const status = searchParams.get('status') || 'all';
  const search = searchParams.get('search') || '';
  const page = parseInt(searchParams.get('page') || '1', 10);

  // 获取分页设置
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // 优先获取用户设置
        const userResponse = await fetch('/api/user-settings/vocabulary_page_size');
        if (userResponse.ok) {
          const userData = await userResponse.json();
          if (userData.value) {
            setVocabularyPerPage(parseInt(userData.value, 10));
            return;
          }
        }

        // 如果没有用户设置，回退到系统设置
        const systemResponse = await fetch('/api/settings/vocabulary-per-page');
        if (systemResponse.ok) {
          const systemData = await systemResponse.json();
          setVocabularyPerPage(systemData.value || 20);
        }
      } catch (error) {
        console.warn('Failed to fetch vocabulary per page setting, using default:', error);
      }
    };
    fetchSettings();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (authStatus !== 'authenticated' || !session?.user) {
        if (authStatus !== 'loading') {
          setError("用户未登录，无法加载数据。");
          setLoading(false);
        }
        return;
      }

      setLoading(true);
      setError(null);
      
      try {
        const userId = session.user.id;
        const [vocabResult, statsResult] = await Promise.all([
          getVocabularyAction({ userId, level, status, search, page, limit: vocabularyPerPage }),
          getVocabularyStatsAction(userId)
        ]);

        if (vocabResult.success) {
          setVocabulary(vocabResult.data);
        } else {
          throw new Error(vocabResult.error || 'Failed to load vocabulary');
        }

        if (statsResult.success) {
          setStats(statsResult.data);
        } else {
          throw new Error(statsResult.error || 'Failed to load stats');
        }

      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (authStatus !== 'loading' && vocabularyPerPage > 0) {
      fetchData();
    }
  }, [level, status, search, page, session, authStatus, vocabularyPerPage]);

  if (loading || vocabulary === null) {
    return <LoadingFallback />;
  }
  
  return <VocabularyBank initialVocabulary={vocabulary} initialStats={stats} />;
}
