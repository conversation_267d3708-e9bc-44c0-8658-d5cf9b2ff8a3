'use client';
import LoginPageComponent from '@/components/LoginPage';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function LoginPage() {
  const router = useRouter();
  const { session, status } = useAuth();

  // 如果用户已登录，重定向到首页
  useEffect(() => {
    if (status === 'authenticated' && session) {
      router.push('/');
    }
  }, [status, session, router]);

  const handleLogin = () => {
    // 登录成功后重定向到首页
    router.push('/');
  };

  // 如果正在加载或已认证，不显示登录页面
  if (status === 'loading' || status === 'authenticated') {
    return null;
  }

  return <LoginPageComponent onLogin={handleLogin} />;
}
