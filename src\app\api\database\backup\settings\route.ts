import { NextRequest, NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// 更新备份设置
export async function POST(request: NextRequest) {
  try {
    const { enabled, intervalHours, maxFiles } = await request.json();
    
    // 验证参数
    if (typeof enabled !== 'boolean' || 
        typeof intervalHours !== 'number' || 
        typeof maxFiles !== 'number' ||
        intervalHours < 1 || intervalHours > 168 || // 1小时到1周
        maxFiles < 1 || maxFiles > 100) {
      return NextResponse.json({
        success: false,
        error: '参数无效'
      }, { status: 400 });
    }
    
    await dbManager.updateBackupSettings(enabled, intervalHours, maxFiles);
    
    return NextResponse.json({
      success: true,
      message: '备份设置已更新'
    });
  } catch (error: any) {
    console.error('更新备份设置失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// 获取备份设置
export async function GET(request: NextRequest) {
  try {
    const settings = await dbManager.getBackupSettings();
    
    return NextResponse.json({
      success: true,
      settings
    });
  } catch (error: any) {
    console.error('获取备份设置失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
