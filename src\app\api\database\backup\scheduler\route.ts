import { NextRequest, NextResponse } from 'next/server';
import { backupScheduler } from '@/lib/server/backup-scheduler';

// 获取调度器状态
export async function GET(request: NextRequest) {
  try {
    const status = backupScheduler.getStatus();
    
    return NextResponse.json({
      success: true,
      status
    });
  } catch (error: any) {
    console.error('获取备份调度器状态失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// 重启调度器
export async function POST(request: NextRequest) {
  try {
    await backupScheduler.restart();
    
    return NextResponse.json({
      success: true,
      message: '备份调度器已重启'
    });
  } catch (error: any) {
    console.error('重启备份调度器失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
