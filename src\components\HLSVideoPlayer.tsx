'use client';
import React, { useRef, useEffect } from 'react';
import Hls from 'hls.js';

interface HLSVideoPlayerProps {
  src: string;
  className?: string;
  controls?: boolean;
}

const HLSVideoPlayer: React.FC<HLSVideoPlayerProps> = ({ 
  src, 
  className = "w-full rounded-lg", 
  controls = true 
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // 检查是否支持HLS
    if (Hls.isSupported()) {
      // 使用hls.js
      const hls = new Hls({
        enableWorker: false,
        lowLatencyMode: false,
        backBufferLength: 90,
        // 添加音频编解码器兼容性配置
        forceKeyFrameOnDiscontinuity: false,
        abrEwmaDefaultEstimate: 500000,
        // 尝试更宽松的编解码器处理
        capLevelToPlayerSize: false,
        // 错误恢复配置
        fragLoadingTimeOut: 20000,
        manifestLoadingTimeOut: 10000
      });
      
      hlsRef.current = hls;
      
      hls.loadSource(src);
      hls.attachMedia(video);
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed successfully');
      });
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.log('HLS Error:', data.type, data.details, data.fatal);

        // 特殊处理音频编解码器错误
        if (data.details === 'bufferAddCodecError' && data.sourceBufferName === 'audio') {
          console.warn('音频编解码器不兼容，尝试恢复...');
          // 不将此错误视为致命错误，让播放器继续尝试
          return;
        }

        // 检查是否是加密相关错误
        if (data.details === 'fragParsingError' || data.details === 'fragLoadError') {
          console.warn('可能遇到加密或格式问题的视频片段:', data.details);
        }

        // 只记录严重错误，避免控制台噪音
        if (data.fatal) {
          console.error('HLS fatal error:', data);
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error, trying to recover...');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error, trying to recover...');
              hls.recoverMediaError();
              break;
            default:
              console.log('Fatal error, destroying HLS instance');
              if (data.details === 'fragParsingError') {
                console.log('提示：此视频可能使用了加密保护，建议使用在线播放方式');
              }
              hls.destroy();
              break;
          }
        } else {
          // 非致命错误只在开发模式下显示
          if (process.env.NODE_ENV === 'development') {
            console.warn('HLS non-fatal error:', data);
          }
        }
      });
      
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // 原生支持HLS (Safari)
      video.src = src;
    } else {
      console.error('HLS is not supported in this browser');
    }

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src]);

  return (
    <video
      ref={videoRef}
      className={className}
      controls={controls}
      playsInline
      preload="metadata"
    />
  );
};

export default HLSVideoPlayer;
