import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// GET /api/learning/today-progress - 获取今日学习进度
export async function GET() {
  try {
    // 获取当前用户ID（临时使用管理员用户）
    const userId = '1'; // TODO: 从认证系统获取真实用户ID
    
    const progress = await dbManager.getTodayLearningProgress(userId);
    
    return NextResponse.json({
      success: true,
      data: progress
    });
  } catch (error: any) {
    console.error('获取今日学习进度失败:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message,
        message: '获取今日学习进度失败'
      },
      { status: 500 }
    );
  }
}
