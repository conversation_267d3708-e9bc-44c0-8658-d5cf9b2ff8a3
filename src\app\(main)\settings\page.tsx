
import UnifiedSettings from '@/components/UnifiedSettings';
import { dbManager } from '@/lib/server/database';
import { getSession } from '@/lib/auth';
import { Suspense } from 'react';
import { Loader } from 'lucide-react';

async function SettingsData() {
    // 获取用户会话
    const session = await getSession();

    if (!session?.user) {
        return (
            <div className="flex h-full items-center justify-center py-12">
                <div className="text-center">
                    <p className="text-gray-600">请先登录以访问设置</p>
                </div>
            </div>
        );
    }

    const [models, defaultModelSetting, aiTemperatureSetting, apiKeys, aiPrompts] = await Promise.all([
        dbManager.getAIModels(),
        dbManager.getSystemSetting('default_text_model'),
        dbManager.getSystemSetting('ai_temperature'),
        dbManager.getApiKeys(),
        dbManager.getAllAIPrompts()
    ]);

    const defaultModelId = defaultModelSetting?.value || null;
    const aiTemperature = aiTemperatureSetting ? parseFloat(aiTemperatureSetting.value) : 0.7;

    return <UnifiedSettings
      userId={session.user.id.toString()}
      userRole={session.user.role}
      initialModels={models}
      initialDefaultModelId={defaultModelId}
      initialAITemperature={aiTemperature}
      initialApiKeys={apiKeys as any[]}
      initialAIPrompts={aiPrompts}
    />;
}

function LoadingFallback() {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载设置...</p>
        </div>
      </div>
    );
}

export default function SettingsPage() {
    return (
        <Suspense fallback={<LoadingFallback />}>
            <SettingsData />
        </Suspense>
    );
}
