# AI分析管理功能

## 功能概述

AI分析管理页面是一个专门用于管理文章AI分析任务的界面，类似于视频下载管理页面。通过这个页面，管理员可以：

- 查看所有文章的AI处理状态
- 多选文章并手动触发AI分析
- 批量处理未分析的文章
- 监控AI分析进度

## 访问路径

- **URL**: `/scraper` → AI分析管理标签页
- **权限**: 仅管理员可访问
- **导航**: 抓取管理页面 → AI分析管理

## 主要功能

### 1. 文章列表显示

#### 表格列信息
- **选择框**: 支持单选和全选文章
- **文章标题**: 显示文章的完整标题
- **AI处理状态**: 显示当前的AI分析状态
- **来源**: 显示文章的RSS源名称
- **发布时间**: 显示文章的发布日期

#### AI处理状态说明
- **未处理**: 灰色标签，表示尚未进行AI分析
- **待处理**: 黄色标签，表示已加入队列等待处理
- **处理中**: 蓝色标签，表示正在进行AI分析
- **已完成**: 绿色标签，表示AI分析已成功完成
- **失败**: 红色标签，表示AI分析失败
- **已跳过**: 灰色标签，表示因内容问题被跳过

### 2. 批量操作功能

#### 分析全部未处理
- 点击"分析全部未处理"按钮
- 自动筛选出未处理和失败的文章
- 将这些文章批量添加到AI分析队列

#### 分析选中文章
- 手动选择需要分析的文章
- 点击"分析选中"按钮
- 将选中的文章添加到AI分析队列
- 按钮显示当前选中的文章数量

### 3. 分页功能

#### 分页控件
- 显示当前页面信息（第X条到第Y条，共Z条）
- 上一页/下一页按钮
- 页码显示（第X页，共Y页）
- 每页显示10篇文章

### 4. 实时状态更新

#### 自动刷新
- 当触发AI分析任务后，页面会自动刷新
- 状态变化会实时反映在表格中
- 与AI处理器状态同步

## 技术实现

### 数据库查询
```typescript
// 获取文章列表用于AI分析管理
async getArticlesForAIManagement(page: number, limit: number) {
  // 查询文章基本信息和AI处理状态
  // 支持分页和排序
}

// 批量添加AI分析任务
async scheduleAIAnalysisForArticles(articleIds: number[]) {
  // 将文章添加到ai_processing_queue表
  // 设置处理类型为'analysis'
}
```

### API端点
- `getArticlesForAIManagementAction`: 获取文章列表
- `scheduleAIAnalysisAction`: 添加AI分析任务

### 状态管理
- 文章列表状态：`aiArticles`
- 选中文章状态：`selectedAiArticles`
- 分页状态：`aiArticlesCurrentPage`
- 总数状态：`aiArticlesTotalCount`

## 使用流程

### 1. 查看文章状态
1. 进入抓取管理页面
2. 点击"AI分析管理"标签页
3. 查看文章列表和AI处理状态

### 2. 批量分析文章
1. 点击"分析全部未处理"按钮
2. 系统自动筛选未处理的文章
3. 将文章添加到AI分析队列
4. 页面自动刷新显示最新状态

### 3. 选择性分析
1. 使用复选框选择需要分析的文章
2. 点击"分析选中"按钮
3. 确认选中的文章数量
4. 执行分析任务

### 4. 监控分析进度
1. 在"AI处理"标签页查看处理器状态
2. 观察AI分析队列的变化
3. 查看处理日志了解详细信息

## 注意事项

### 1. 权限控制
- 仅管理员可访问此功能
- 需要有效的API Key配置

### 2. 性能考虑
- 批量操作时注意API调用频率
- 大量文章分析可能消耗较多时间和资源

### 3. 错误处理
- 失败的文章可以重新添加到队列
- 查看AI处理日志了解失败原因

### 4. 数据一致性
- 状态更新与AI处理器同步
- 避免重复添加相同文章到队列

## 与其他功能的关系

### AI处理器
- 依赖AI处理器来执行实际的分析任务
- 需要先启动AI处理器才能处理队列中的任务

### 文章管理
- 与新闻阅读页面的AI分析结果关联
- 分析完成后可在文章详情页查看结果

### 抓取管理
- 新抓取的文章会自动显示在列表中
- 可以配置抓取后自动添加到AI分析队列

这个功能大大简化了AI分析任务的管理，提供了直观的界面来批量处理文章分析任务。
