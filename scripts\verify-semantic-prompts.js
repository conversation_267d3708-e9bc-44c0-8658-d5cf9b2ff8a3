const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifySemanticPrompts() {
  console.log('🔍 验证语义网络提示词更新...');
  console.log('');

  try {
    // 检查文章分析系统提示词
    const systemPrompt = await prisma.ai_prompts.findUnique({
      where: { name: 'article_analysis_system' }
    });

    if (systemPrompt) {
      console.log('✅ 文章分析系统提示词存在');
      console.log(`版本: ${systemPrompt.version}`);
      console.log(`更新时间: ${systemPrompt.updated_at}`);
      
      // 检查是否包含语义网络相关内容
      const hasSemanticContent = systemPrompt.content.includes('上位語') && 
                                 systemPrompt.content.includes('下位語') &&
                                 systemPrompt.content.includes('語族') &&
                                 systemPrompt.content.includes('関連概念');
      
      if (hasSemanticContent) {
        console.log('✅ 系统提示词包含语义网络指导');
      } else {
        console.log('❌ 系统提示词缺少语义网络指导');
      }
    } else {
      console.log('❌ 文章分析系统提示词不存在');
    }

    console.log('');

    // 检查文章分析主提示词
    const mainPrompt = await prisma.ai_prompts.findUnique({
      where: { name: 'article_analysis_prompt' }
    });

    if (mainPrompt) {
      console.log('✅ 文章分析主提示词存在');
      console.log(`版本: ${mainPrompt.version}`);
      console.log(`更新时间: ${mainPrompt.updated_at}`);
      
      // 检查是否包含语义网络字段
      const hasSemanticFields = mainPrompt.content.includes('hypernyms') && 
                                mainPrompt.content.includes('hyponyms') &&
                                mainPrompt.content.includes('wordFamily') &&
                                mainPrompt.content.includes('relatedConcepts');
      
      if (hasSemanticFields) {
        console.log('✅ 主提示词包含语义网络字段定义');
      } else {
        console.log('❌ 主提示词缺少语义网络字段定义');
      }

      // 检查具体的语义网络说明
      const hasExamples = mainPrompt.content.includes('「犬」の上位語') && 
                         mainPrompt.content.includes('「動物」の下位語');
      
      if (hasExamples) {
        console.log('✅ 主提示词包含语义网络示例');
      } else {
        console.log('❌ 主提示词缺少语义网络示例');
      }
    } else {
      console.log('❌ 文章分析主提示词不存在');
    }

    console.log('');

    // 显示提示词内容摘要
    if (mainPrompt) {
      console.log('📋 主提示词内容摘要：');
      const lines = mainPrompt.content.split('\n');
      const relevantLines = lines.filter(line => 
        line.includes('hypernyms') || 
        line.includes('hyponyms') || 
        line.includes('wordFamily') || 
        line.includes('relatedConcepts') ||
        line.includes('上位語') ||
        line.includes('下位語') ||
        line.includes('語族') ||
        line.includes('関連概念')
      );
      
      if (relevantLines.length > 0) {
        relevantLines.slice(0, 10).forEach(line => {
          console.log(`  ${line.trim()}`);
        });
        if (relevantLines.length > 10) {
          console.log(`  ... 还有 ${relevantLines.length - 10} 行相关内容`);
        }
      } else {
        console.log('  未找到语义网络相关内容');
      }
    }

    console.log('');
    console.log('🎯 验证结果总结：');
    
    if (systemPrompt && mainPrompt) {
      const systemHasContent = systemPrompt.content.includes('上位語');
      const mainHasFields = mainPrompt.content.includes('hypernyms');
      
      if (systemHasContent && mainHasFields) {
        console.log('✅ 语义网络提示词更新成功！');
        console.log('✅ 系统已准备好支持语义网络分析');
        console.log('');
        console.log('🔄 下一步建议：');
        console.log('1. 启动应用并访问提示词测试页面');
        console.log('2. 使用示例数据测试文章分析功能');
        console.log('3. 检查AI返回的词汇是否包含新的语义关系');
        console.log('4. 验证UI界面是否正确显示语义网络信息');
      } else {
        console.log('⚠️  语义网络提示词更新不完整');
        console.log('建议重新运行更新脚本');
      }
    } else {
      console.log('❌ 提示词缺失，请检查数据库');
    }

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行验证
verifySemanticPrompts()
  .catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
