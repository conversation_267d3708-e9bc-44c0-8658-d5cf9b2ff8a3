
import RSSSourceManager from '@/components/RSSSourceManager';
import { dbManager } from '@/lib/server/database';
import { Suspense } from 'react';
import { Loader } from 'lucide-react';

async function RSSSourcesData() {
  const sources = await dbManager.getAllRSSSources();
  return <RSSSourceManager initialSources={sources} />;
}

function LoadingFallback() {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <Loader className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载RSS源...</p>
        </div>
      </div>
    );
  }

export default function RSSManagerPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
        <RSSSourcesData />
    </Suspense>
    );
}
