import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // 构建文件路径
    const filePath = join(process.cwd(), 'public', 'media', ...params.path);
    
    // 安全检查：确保路径在media目录内
    const mediaDir = join(process.cwd(), 'public', 'media');
    if (!filePath.startsWith(mediaDir)) {
      return new NextResponse('Forbidden', { status: 403 });
    }
    
    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return new NextResponse('File not found', { status: 404 });
    }

    // 读取文件
    const fileBuffer = await readFile(filePath);
    
    // 根据文件扩展名设置Content-Type
    const fileName = params.path[params.path.length - 1];
    const ext = fileName.split('.').pop()?.toLowerCase();
    const contentType = getContentType(ext);

    // 设置响应头
    const headers = new Headers({
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=31536000, immutable',
      'Content-Length': fileBuffer.length.toString(),
    });

    // 如果是视频文件，添加额外的头部
    if (contentType.startsWith('video/') || fileName.endsWith('.m3u8') || fileName.endsWith('.ts')) {
      headers.set('Access-Control-Allow-Origin', '*');
      headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      headers.set('Access-Control-Allow-Headers', 'Range');
      
      if (fileName.endsWith('.m3u8')) {
        headers.set('Content-Type', 'application/vnd.apple.mpegurl');
      } else if (fileName.endsWith('.ts')) {
        headers.set('Content-Type', 'video/mp2t');
      }
    }

    return new NextResponse(fileBuffer, { headers });
  } catch (error: any) {
    console.error('Media file serving error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

function getContentType(ext: string | undefined): string {
  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'svg':
      return 'image/svg+xml';
    case 'mp4':
      return 'video/mp4';
    case 'webm':
      return 'video/webm';
    case 'mp3':
      return 'audio/mpeg';
    case 'wav':
      return 'audio/wav';
    case 'ogg':
      return 'audio/ogg';
    case 'm3u8':
      return 'application/vnd.apple.mpegurl';
    case 'ts':
      return 'video/mp2t';
    default:
      return 'application/octet-stream';
  }
}
