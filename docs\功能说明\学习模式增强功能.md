# 学习模式增强功能说明

## 功能概述

本次更新实现了用户要求的学习模式增强功能，允许用户自主选择学习内容类型（词汇/语法），并分别配置学习数量。

## 主要功能

### 1. 分别配置词汇和语法学习数量

**位置**: 设置页面 → 个人设置 → 学习设置

**功能说明**:
- 新增"每次学习词汇数量"配置项，默认8个，可选择3-20个
- 新增"每次学习语法数量"配置项，默认5个，可选择2-12个  
- 保留"混合模式总数量"配置项，用于向后兼容，默认10个

**数据库字段**:
- `study_vocabulary_per_session`: 每次学习词汇数量
- `study_grammar_per_session`: 每次学习语法数量
- `study_cards_per_session`: 混合模式总数量（保留兼容性）

### 2. 增强的学习模式选择界面

**位置**: 学习页面 (`/study-new`)

**功能说明**:
- 分别显示新词汇、新语法、复习词汇、复习语法的数量统计
- 提供6种学习选项：
  - **学习新内容**: 学习词汇、学习语法、混合学习
  - **复习已学内容**: 复习词汇、复习语法、混合复习
- 每个选项显示对应的数量和预计用时
- 智能的学习建议提示

**界面特点**:
- 清晰的视觉分类，使用不同颜色区分词汇和语法
- 响应式设计，支持桌面和移动端
- 禁用状态处理，当没有对应内容时自动禁用按钮

### 3. 智能的学习队列过滤

**后端实现**:
- 新增 `getFilteredStudyQueue()` 方法，支持按内容类型过滤
- 根据用户选择的模式和内容类型返回相应的学习队列
- 支持词汇、语法、混合三种内容类型

**过滤逻辑**:
- **词汇模式**: 只返回词汇相关的学习记录
- **语法模式**: 只返回语法相关的学习记录  
- **混合模式**: 返回所有类型的学习记录

### 4. 分别统计词汇和语法进度

**侧边栏进度显示**:
- 分别显示词汇学习进度和语法学习进度
- 每个类型都有独立的进度条和完成度显示
- 保留总体复习任务和完成度统计

**数据结构**:
```typescript
interface TodayProgressData {
  vocabulary: {
    learned: number;
    target: number;
    percentage: number;
    reviews: { completed: number; total: number; percentage: number; };
  };
  grammar: {
    learned: number;
    target: number;
    percentage: number;
    reviews: { completed: number; total: number; percentage: number; };
  };
  // ... 其他统计数据
}
```

### 5. 学习会话的内容类型支持

**StudySessionWithMode组件增强**:
- 支持 `contentType` 参数，可指定学习内容类型
- 动态显示模式名称（如"学习词汇"、"复习语法"等）
- 智能的空状态消息，根据内容类型显示相应提示

## 技术实现

### 核心文件修改

1. **用户设置相关**:
   - `src/lib/userSettings.ts`: 扩展设置数据结构
   - `src/components/UserSettings.tsx`: 更新设置界面

2. **学习模式选择**:
   - `src/components/EnhancedStudyModeSelector.tsx`: 新的增强版模式选择器
   - `src/app/(main)/study-new/page.tsx`: 更新页面使用新组件

3. **学习会话**:
   - `src/components/StudySessionWithMode.tsx`: 支持内容类型过滤
   - `src/app/actions.ts`: 新增过滤API

4. **数据库层**:
   - `src/lib/server/database.ts`: 新增过滤查询方法和分别统计逻辑

5. **进度显示**:
   - `src/hooks/useTodayProgress.ts`: 更新数据结构
   - `src/components/Sidebar.tsx`: 分别显示词汇和语法进度

### 向后兼容性

- 保留原有的 `study_cards_per_session` 设置项
- 保留原有的统计数据结构
- 新功能为增量式添加，不影响现有功能

## 使用说明

### 用户操作流程

1. **配置学习数量**:
   - 进入设置页面
   - 在"学习设置"部分分别设置词汇和语法的学习数量

2. **选择学习模式**:
   - 进入学习页面
   - 查看各类型内容的数量统计
   - 选择想要的学习模式和内容类型

3. **开始学习**:
   - 系统根据选择返回对应的学习队列
   - 学习过程中显示相应的模式标识
   - 完成后更新对应的进度统计

### 学习建议

- 建议词汇学习：每次5-10个新词汇
- 建议语法学习：每次2-5个新语法点
- 优先完成复习任务，巩固已学内容
- 根据个人情况调整学习数量设置

## 数据库影响

### 新增设置项
- `study_vocabulary_per_session`: 每次学习词汇数量
- `study_grammar_per_session`: 每次学习语法数量

### 查询优化
- 新增按内容类型过滤的查询方法
- 分别统计词汇和语法的学习进度
- 保持现有查询的性能和兼容性

## 后续扩展

本次实现为学习系统的内容类型分离奠定了基础，后续可以考虑：

1. 更细粒度的难度分级学习
2. 基于用户表现的智能推荐
3. 学习路径的个性化定制
4. 更详细的学习分析报告
