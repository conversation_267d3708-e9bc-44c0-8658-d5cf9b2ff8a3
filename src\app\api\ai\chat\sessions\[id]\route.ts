import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// GET /api/ai/chat/sessions/[id] - 获取特定聊天会话及其消息
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const sessionId = parseInt(id);
    if (isNaN(sessionId)) {
      return NextResponse.json(
        { success: false, error: '无效的会话ID' },
        { status: 400 }
      );
    }

    // 获取当前用户ID（临时使用管理员用户）
    const userId = 1; // TODO: 从认证系统获取真实用户ID
    
    const session = await dbManager.getChatSession(sessionId, userId);
    
    if (!session) {
      return NextResponse.json(
        { success: false, error: '会话不存在或无权访问' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: session
    });
  } catch (error: any) {
    console.error('获取聊天会话失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取聊天会话失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// PUT /api/ai/chat/sessions/[id] - 更新聊天会话
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const sessionId = parseInt(id);
    if (isNaN(sessionId)) {
      return NextResponse.json(
        { success: false, error: '无效的会话ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title, scenarioConfig } = body;
    
    // 获取当前用户ID（临时使用管理员用户）
    const userId = 1; // TODO: 从认证系统获取真实用户ID
    
    const session = await dbManager.updateChatSession(sessionId, userId, {
      title,
      scenario_config: scenarioConfig
    });
    
    return NextResponse.json({
      success: true,
      data: session
    });
  } catch (error: any) {
    console.error('更新聊天会话失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '更新聊天会话失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/ai/chat/sessions/[id] - 删除聊天会话
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const sessionId = parseInt(id);
    if (isNaN(sessionId)) {
      return NextResponse.json(
        { success: false, error: '无效的会话ID' },
        { status: 400 }
      );
    }

    // 获取当前用户ID（临时使用管理员用户）
    const userId = 1; // TODO: 从认证系统获取真实用户ID
    
    await dbManager.deleteChatSession(sessionId, userId);
    
    return NextResponse.json({
      success: true,
      message: '会话已删除'
    });
  } catch (error: any) {
    console.error('删除聊天会话失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '删除聊天会话失败', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}
