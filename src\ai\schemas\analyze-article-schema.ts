/**
 * @fileOverview Schemas for the article analysis flow.
 */
import { z } from 'zod';

export const ArticleAnalysisInputSchema = z.object({
  title: z.string().describe('The Japanese title of the article.'),
  subtitle: z.string().optional().describe('The Japanese subtitle or summary of the article.'),
  content: z.string().describe('The full Japanese content of the article.'),
  modelName: z.string().optional().describe('The name of the AI model to use for the analysis.'),
});
export type ArticleAnalysisInput = z.infer<typeof ArticleAnalysisInputSchema>;

export const ArticleAnalysisOutputSchema = z.object({
  titleWithFurigana: z.string().optional().describe('The full Japanese title with furigana for all Kanji, formatted using HTML ruby tags. e.g., "<ruby>漢字<rt>かんじ</rt></ruby>".'),
  
  translation: z.object({
    translatedTitle: z.string().describe('The translated Chinese title.'),
    translatedSubtitle: z.string().optional().describe('The translated Chinese subtitle or summary.'),
    translatedContent: z.string().describe('The full translated Chinese content, preserving paragraph structure.'),
  }).describe('The Chinese translation of the article.'),

  vocabulary: z.array(z.object({
    word: z.string().describe('The key Japanese vocabulary word found in the article.'),
    reading: z.string().describe('The hiragana or katakana reading of the word.'),
    partOfSpeech: z.string().describe('The part of speech of the word (e.g., "名詞", "動詞", "形容詞").'),
    meaning: z.string().describe('The Chinese meaning of the word.'),
    meaningEn: z.string().describe('The English meaning of the word.'),
    explanation: z.string().describe('A brief explanation of the word\'s usage or nuance in Chinese.'),
    explanationJa: z.string().describe('A detailed explanation of the word\'s meaning and usage in Japanese, with furigana for all kanji using HTML ruby tags. e.g., "<ruby>経済<rt>けいざい</rt></ruby>とは、<ruby>社会<rt>しゃかい</rt></ruby>の<ruby>生産<rt>せいさん</rt></ruby>・<ruby>流通<rt>りゅうつう</rt></ruby>・<ruby>消費<rt>しょうひ</rt></ruby>の<ruby>活動<rt>かつどう</rt></ruby>のことです。"'),
    examples: z.array(z.string()).describe('Exactly three example sentences in Japanese, each followed by its Chinese translation. e.g., ["日本の技術は進んでいます - 日本的技术很先进。"]'),
    commonCollocations: z.array(z.object({
        collocation: z.string().describe('A common collocation or phrase using this word in Japanese.'),
        meaning: z.string().describe('The Chinese meaning of this collocation.'),
        example: z.string().describe('An example sentence using this collocation, with Chinese translation.')
    })).optional().describe('Common collocations and phrases using this word (3-5 items).'),
    relatedWords: z.object({
        synonyms: z.array(z.object({
            word: z.string().describe('A synonym word in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the synonym.'),
            meaning: z.string().describe('The Chinese meaning of the synonym.')
        })).optional().describe('An array of synonyms with their readings and Chinese meanings.'),
        antonyms: z.array(z.object({
            word: z.string().describe('An antonym word in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the antonym.'),
            meaning: z.string().describe('The Chinese meaning of the antonym.')
        })).optional().describe('An array of antonyms with their readings and Chinese meanings.'),
        hypernyms: z.array(z.object({
            word: z.string().describe('A hypernym (broader category word) in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the hypernym.'),
            meaning: z.string().describe('The Chinese meaning of the hypernym.')
        })).optional().describe('An array of hypernyms with their readings and Chinese meanings. e.g., for "犬": [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]'),
        hyponyms: z.array(z.object({
            word: z.string().describe('A hyponym (more specific word) in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the hyponym.'),
            meaning: z.string().describe('The Chinese meaning of the hyponym.')
        })).optional().describe('An array of hyponyms with their readings and Chinese meanings. e.g., for "動物": [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]'),
        wordFamily: z.array(z.object({
            word: z.string().describe('A word sharing the same root or kanji in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the word.'),
            meaning: z.string().describe('The Chinese meaning of the word.')
        })).optional().describe('An array of words sharing the same root or kanji with their readings and Chinese meanings. e.g., for "食べ物": [{"word": "食べる", "reading": "たべる", "meaning": "吃"}, {"word": "食堂", "reading": "しょくどう", "meaning": "食堂"}]'),
        relatedConcepts: z.array(z.object({
            word: z.string().describe('A conceptually related word in Japanese.'),
            reading: z.string().describe('The hiragana or katakana reading of the related word.'),
            meaning: z.string().describe('The Chinese meaning of the related word.')
        })).optional().describe('An array of conceptually related words with their readings and Chinese meanings. e.g., for "犬": [{"word": "散歩", "reading": "さんぽ", "meaning": "散步"}, {"word": "餌", "reading": "えさ", "meaning": "饲料"}]'),
    }).optional().describe('Related words including synonyms, antonyms, hypernyms, hyponyms, word family, and related concepts, all with their readings.'),
    verbInfo: z.object({
        transitivity: z.string().optional().describe('The transitivity of the verb ("自動詞" or "他動詞").'),
        conjugations: z.object({
            teForm: z.string().optional().describe('The te-form (て形) of the verb.'),
            naiForm: z.string().optional().describe('The nai-form (ない形) of the verb.'),
            taForm: z.string().optional().describe('The ta-form (た形) of the verb.'),
            masuForm: z.string().optional().describe('The masu-form (ます形) of the verb.'),
        }).optional().describe('An object containing common verb conjugations.')
    }).optional().describe('Additional information if the word is a verb.')
  })).describe('An array of 5 to 10 key vocabulary words from the article with detailed analysis.'),

  grammar: z.array(z.object({
    pattern: z.string().describe('The key Japanese grammar pattern found in the article. e.g., "～について"'),
    reading: z.string().optional().describe('The hiragana reading of the grammar pattern if it contains kanji.'),
    explanation: z.string().describe('A detailed explanation of the grammar pattern\'s meaning and usage in Chinese.'),
    explanationJa: z.string().describe('A detailed explanation of the grammar pattern\'s meaning and usage in Japanese, with furigana for all kanji using HTML ruby tags. e.g., "「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。"'),
    examples: z.array(z.string()).describe('Exactly three example sentences in Japanese, each followed by its Chinese translation. e.g., ["この問題について話しましょう - 我们来谈谈关于这个问题的事情吧。"]'),
    commonCollocations: z.array(z.string()).describe('2-3 common collocations or usage patterns with this grammar point. e.g., ["～について話す", "～について考える", "～について書く"]'),
    similarGrammar: z.array(z.object({
        pattern: z.string().describe('A similar grammar pattern.'),
        difference: z.string().describe('The key difference between this pattern and the main pattern, explained in Chinese.'),
        examples: z.array(z.string()).describe('Exactly two example sentences for this similar grammar pattern, each followed by its Chinese translation. e.g., ["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]')
    })).describe('3-5 similar grammar patterns with their differences and example sentences (each with 2 examples).'),
  })).describe('An array of 2 to 4 key grammar points from the article.'),

  contentWithFurigana: z.string().optional().describe('The full Japanese content with furigana for all Kanji, formatted using HTML ruby tags. e.g., "<ruby>漢字<rt>かんじ</rt></ruby>". This should be the entire article content, not just snippets.'),
  subtitleWithFurigana: z.string().optional().describe('The full Japanese subtitle with furigana for all Kanji, formatted using HTML ruby tags. This field should only be present if a subtitle was provided in the input.'),
});
export type ArticleAnalysisOutput = z.infer<typeof ArticleAnalysisOutputSchema>;
