const { execSync } = require('child_process');
const path = require('path');

console.log('开始 FSRS 迁移...');

try {
  // 编译 TypeScript 文件
  console.log('编译迁移脚本...');
  execSync('npx tsc scripts/migrate-to-fsrs.ts --outDir scripts/dist --target es2020 --module commonjs --esModuleInterop --skipLibCheck', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // 运行迁移
  console.log('运行迁移脚本...');
  execSync('node scripts/dist/migrate-to-fsrs.js', {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  console.log('✅ FSRS 迁移完成！');
} catch (error) {
  console.error('❌ 迁移失败:', error.message);
  process.exit(1);
}
