# 抓取延迟配置功能说明

## 功能概述

新增了抓取延迟配置功能，允许用户通过设置页面自定义抓取过程中的各种延迟时间，以优化抓取性能并避免对目标网站造成过大压力。

## 主要特性

### 1. **可配置的延迟参数**

- **RSS源间延迟**：处理完一个RSS源后等待的时间（默认5000ms）
- **文章间延迟**：处理完一篇文章后等待的时间（默认2000ms）
- **最小请求延迟**：智能延迟的最小时间（默认2000ms）
- **最大请求延迟**：智能延迟的最大时间（默认5000ms）

### 2. **智能延迟策略**

- **跳过已存在文章时不延迟**：如果文章已存在于数据库中，系统会跳过该文章的处理，并且不会执行文章间延迟
- **最后一项不延迟**：处理最后一个RSS源或最后一篇文章时不执行延迟
- **错误情况仍延迟**：处理失败时仍会执行延迟，避免连续错误请求

### 3. **配置持久化**

- 配置保存在 `system_settings` 表中
- 系统启动时自动加载配置
- 支持实时更新，无需重启服务

## 使用方法

### 访问设置页面

1. 登录系统后，点击右上角的用户头像
2. 选择"设置"进入设置页面
3. 找到"抓取延迟配置"部分

### 配置延迟参数

1. **RSS源间延迟**：
   - 范围：1000-30000毫秒
   - 步长：1000毫秒
   - 说明：处理完一个RSS源的所有文章后，等待指定时间再处理下一个RSS源

2. **文章间延迟**：
   - 范围：500-10000毫秒
   - 步长：500毫秒
   - 说明：成功处理一篇文章后等待的时间，如果文章已存在则跳过延迟

3. **最小请求延迟**：
   - 范围：1000-10000毫秒
   - 步长：500毫秒
   - 说明：智能延迟的最小时间

4. **最大请求延迟**：
   - 范围：2000-20000毫秒
   - 步长：1000毫秒
   - 说明：智能延迟的最大时间

### 保存配置

点击"保存配置"按钮保存设置，系统会立即应用新的延迟配置。

## 延迟策略详解

### RSS源处理流程

```
RSS源1: 开始
├── 文章1 → [文章间延迟] → 文章2 → [文章间延迟] → ... → 文章N
└── [RSS源间延迟]
RSS源2: 开始
├── 文章1 → [文章间延迟] → 文章2 → [文章间延迟] → ... → 文章N
└── [RSS源间延迟]
...
RSS源N: 开始
├── 文章1 → [文章间延迟] → 文章2 → [文章间延迟] → ... → 文章N
└── 结束（无延迟）
```

### 智能跳过逻辑

- **文章已存在**：直接跳过，不执行文章间延迟
- **文章处理成功**：执行文章间延迟（除非是最后一篇）
- **文章处理失败**：执行文章间延迟（避免连续错误请求）

### 自适应调整

系统会根据服务器响应自动调整延迟策略：

- **403错误**：延迟时间翻倍（最大15秒）
- **429错误**：延迟时间增加3倍（最大20秒）
- **连续成功**：每10个请求后延迟增加1秒（最大8秒）

## 性能优化效果

### 优化前

- 所有文章都执行固定延迟
- 即使文章已存在也会延迟
- 延迟时间无法调整

### 优化后

- 跳过的文章不执行延迟
- 可根据需要调整延迟时间
- 最后一项不执行无意义的延迟

### 预期效果

假设抓取3个RSS源，每个源有10篇文章，其中5篇已存在：

**优化前**：
- 文章延迟：30篇 × 2秒 = 60秒
- RSS源延迟：2个间隔 × 5秒 = 10秒
- **总延迟**：70秒

**优化后**：
- 文章延迟：15篇新文章 × 2秒 = 30秒
- RSS源延迟：2个间隔 × 5秒 = 10秒
- **总延迟**：40秒
- **节省时间**：30秒（43%提升）

## 推荐配置

### 保守配置（适合生产环境）

- RSS源间延迟：8000ms
- 文章间延迟：3000ms
- 最小请求延迟：3000ms
- 最大请求延迟：8000ms

### 平衡配置（默认配置）

- RSS源间延迟：5000ms
- 文章间延迟：2000ms
- 最小请求延迟：2000ms
- 最大请求延迟：5000ms

### 快速配置（适合测试环境）

- RSS源间延迟：2000ms
- 文章间延迟：1000ms
- 最小请求延迟：1000ms
- 最大请求延迟：3000ms

## 注意事项

1. **延迟时间不宜过短**：过短的延迟可能导致被目标网站限制访问
2. **延迟时间不宜过长**：过长的延迟会显著增加抓取时间
3. **根据实际情况调整**：可以根据抓取成功率和速度需求进行调整
4. **监控抓取日志**：通过日志观察延迟效果和错误情况

## 技术实现

### 数据库存储

配置存储在 `system_settings` 表中：

```sql
INSERT INTO system_settings (key, value) VALUES
('rss_source_delay', '5000'),
('article_delay', '2000'),
('request_min_delay', '2000'),
('request_max_delay', '5000');
```

### 代码实现

- **前端**：`src/components/Settings.tsx` - 配置界面
- **后端**：`src/app/actions.ts` - 配置管理接口
- **数据库**：`src/lib/server/database.ts` - 配置存取方法
- **抓取器**：`src/lib/server/scraper.ts` - 延迟逻辑实现

### API接口

- `getScrapingDelayConfigAction()` - 获取延迟配置
- `updateScrapingDelayConfigAction(config)` - 更新延迟配置

## 相关文件

### 核心文件

- `src/components/Settings.tsx` - 设置页面UI
- `src/app/actions.ts` - 配置管理Actions
- `src/lib/server/database.ts` - 数据库操作
- `src/lib/server/scraper.ts` - 抓取器实现
- `server/utils/enhanced-scraper.js` - 增强抓取器
- `server/utils/anti-crawler.js` - 反爬虫管理器

### 数据库相关

- `src/lib/server/database.ts` - 统一的Prisma数据库管理器
- `prisma/schema.prisma` - 数据库模式定义

## 总结

抓取延迟配置功能提供了灵活的延迟控制机制，既保证了抓取的稳定性，又优化了抓取效率。通过智能跳过已存在文章的延迟，显著减少了不必要的等待时间，提升了整体抓取性能。
