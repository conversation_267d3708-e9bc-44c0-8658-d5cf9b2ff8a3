'use client';
import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  BookOpen, 
  RotateCcw, 
  Sparkles, 
  Calendar,
  TrendingUp,
  Clock,
  Target,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { getStudyQueueAction } from '@/app/actions';

interface StudyModeProps {
  onModeSelect: (mode: 'learn' | 'review') => void;
}

const StudyModeSelector: React.FC<StudyModeProps> = ({ onModeSelect }) => {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState({
    newItems: 0,
    reviewItems: 0,
    loading: true
  });

  useEffect(() => {
    const fetchStats = async () => {
      if (status === 'loading' || !session?.user?.id) {
        setStats(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        const result = await getStudyQueueAction(session.user.id);
        if (result.success && result.data) {
          setStats({
            newItems: result.data.newItems.length,
            reviewItems: result.data.reviewItems.length,
            loading: false
          });
        } else {
          setStats({ newItems: 0, reviewItems: 0, loading: false });
        }
      } catch (error) {
        console.error('Failed to fetch study stats:', error);
        setStats({ newItems: 0, reviewItems: 0, loading: false });
      }
    };

    fetchStats();
  }, [session, status]);

  if (stats.loading) {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">正在加载学习数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">选择学习模式</h1>
        <p className="text-gray-600">根据您的学习需求选择合适的模式</p>
      </div>

      {/* 学习统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-blue-600">新内容</p>
              <p className="text-2xl font-bold text-blue-900">{stats.newItems}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Calendar className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">今日复习</p>
              <p className="text-2xl font-bold text-green-900">{stats.reviewItems}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Target className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-purple-600">总任务</p>
              <p className="text-2xl font-bold text-purple-900">{stats.newItems + stats.reviewItems}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 模式选择卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 学习新内容模式 */}
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <div className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">学习新内容</h3>
                <p className="text-sm text-gray-500">学习从未接触过的词汇和语法</p>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Sparkles className="h-4 w-4 text-blue-500" />
                <span>学习 {stats.newItems} 个新的词汇和语法点</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <TrendingUp className="h-4 w-4 text-blue-500" />
                <span>扩展您的日语知识库</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4 text-blue-500" />
                <span>预计用时：{Math.ceil(stats.newItems * 1.5)} 分钟</span>
              </div>
            </div>

            <button
              onClick={() => onModeSelect('learn')}
              disabled={stats.newItems === 0}
              className={`w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-colors ${
                stats.newItems > 0
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>{stats.newItems > 0 ? '开始学习' : '暂无新内容'}</span>
              {stats.newItems > 0 && <ArrowRight className="h-4 w-4" />}
            </button>
          </div>
        </div>

        {/* 复习模式 */}
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
          <div className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <RotateCcw className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">复习已学内容</h3>
                <p className="text-sm text-gray-500">巩固今天需要复习的内容</p>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4 text-green-500" />
                <span>复习 {stats.reviewItems} 个到期的学习项目</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span>巩固记忆，防止遗忘</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4 text-green-500" />
                <span>预计用时：{Math.ceil(stats.reviewItems * 1)} 分钟</span>
              </div>
            </div>

            <button
              onClick={() => onModeSelect('review')}
              disabled={stats.reviewItems === 0}
              className={`w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-colors ${
                stats.reviewItems > 0
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>{stats.reviewItems > 0 ? '开始复习' : '暂无复习任务'}</span>
              {stats.reviewItems > 0 && <ArrowRight className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      {(stats.newItems > 0 || stats.reviewItems > 0) && (
        <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="h-6 w-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <Target className="h-4 w-4 text-amber-600" />
            </div>
            <div>
              <h4 className="font-medium text-amber-800 mb-1">学习建议</h4>
              <div className="text-sm text-amber-700 space-y-1">
                {stats.reviewItems > 0 && (
                  <p>• 建议优先完成复习任务，巩固已学内容</p>
                )}
                {stats.newItems > 0 && (
                  <p>• 每天学习新内容不宜过多，建议控制在10-15个以内</p>
                )}
                <p>• 学习过程中遇到困难的内容会自动安排更频繁的复习</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 空状态 */}
      {stats.newItems === 0 && stats.reviewItems === 0 && (
        <div className="text-center py-12">
          <Sparkles className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无学习任务</h3>
          <p className="text-gray-600 max-w-md mx-auto mb-6">
            您的学习队列是空的。请先去阅读新闻，并将遇到的重点词汇或语法点加入学习计划。
          </p>
          <button
            onClick={() => window.location.href = '/'}
            className="inline-flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            <BookOpen className="h-5 w-5" />
            <span>去新闻列表看看</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default StudyModeSelector;
