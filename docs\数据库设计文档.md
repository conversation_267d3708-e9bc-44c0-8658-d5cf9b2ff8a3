# 数据库设计文档

## 数据库概述

NHK日语学习平台使用SQLite作为主要数据库，采用关系型数据库设计，支持完整的ACID事务和SQL标准。数据库文件位于`data/nhk_news_new.db`。

## 数据库架构设计

### 设计原则
- **数据分离**：原始内容与AI处理结果分离存储
- **用户隔离**：每个用户的学习数据独立管理
- **扩展性**：支持未来功能扩展的灵活结构
- **性能优化**：合理的索引设计和查询优化

### 核心表结构关系图
```
rss_sources (RSS源配置)
    ↓ (1:N)
rss_field_mappings (字段映射)
    ↓
articles (文章基础信息)
    ↓ (1:N)
article_translations (翻译)   article_vocabulary (文章词汇关联)
    ↓                              ↓
vocabulary (词汇表) ← → user_learning_records (学习记录)
    ↓
grammar_points (语法点) ← → article_grammar_points (文章语法关联)
```

## 详细表结构

### 1. RSS源配置表 (rss_sources)

**用途**：存储RSS新闻源的配置信息

```sql
CREATE TABLE rss_sources (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- RSS源名称
  url TEXT UNIQUE NOT NULL,              -- RSS源URL
  description TEXT,                      -- RSS源描述
  category TEXT,                         -- 分类 (科技/经济/社会等)
  language TEXT DEFAULT 'ja',            -- 语言代码
  is_active BOOLEAN DEFAULT 1,           -- 是否启用
  max_articles INTEGER DEFAULT 10,       -- 每次抓取的最大文章数
  enable_ai_processing BOOLEAN DEFAULT 0, -- 是否启用AI处理
  content_selector TEXT,                 -- 内容选择器
  last_fetch_time DATETIME,              -- 最后抓取时间
  last_fetch_count INTEGER DEFAULT 0,    -- 最后一次抓取的文章数
  total_fetched INTEGER DEFAULT 0,       -- 总抓取文章数
  success_rate REAL DEFAULT 0.0,         -- 成功率
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**注意**: 抓取延迟配置已从RSS源级别移至系统级别，通过 `system_settings` 表统一管理。

**关键字段说明**：
- `content_selector`：用于从网页中提取正文内容的CSS选择器
- `enable_ai_processing`：控制是否对该源的文章进行AI处理
- `success_rate`：抓取成功率，用于评估RSS源质量

### 2. RSS字段映射配置表 (rss_field_mappings)

**用途**：定义如何从RSS XML中提取各个字段

```sql
CREATE TABLE rss_field_mappings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER NOT NULL,        -- RSS源ID
  field_name TEXT NOT NULL,               -- 目标字段名
  xpath_selector TEXT,                    -- XPath选择器
  css_selector TEXT,                      -- CSS选择器
  attribute_name TEXT,                    -- 属性名
  default_value TEXT,                     -- 默认值
  is_required BOOLEAN DEFAULT 0,         -- 是否必需字段
  transform_rule TEXT,                    -- 数据转换规则 (JSON格式)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id) ON DELETE CASCADE
);
```

**支持的字段类型**：
- `title`：文章标题
- `link`：文章链接
- `description`：文章描述
- `pub_date`：发布时间
- `image_url`：图片URL
- `guid`：唯一标识符

**转换规则示例**：
```json
{
  "type": "datetime",
  "inputFormat": "RFC2822",
  "outputFormat": "ISO8601"
}
```

### 3. 文章基础信息表 (articles)

**用途**：存储文章的基本信息和内容

```sql
CREATE TABLE articles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER,                 -- RSS源ID
  guid TEXT UNIQUE,                      -- RSS条目唯一标识
  title TEXT NOT NULL,                   -- 文章标题
  subtitle TEXT,                         -- 文章副标题/摘要
  content TEXT NOT NULL,                 -- 文章正文
  content_html TEXT,                     -- 原始HTML内容
  url TEXT UNIQUE NOT NULL,              -- 原文链接
  category TEXT,                         -- 文章分类
  tags TEXT,                             -- 标签 (JSON数组)
  publish_time DATETIME,                 -- 发布时间
  fetch_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 抓取时间
  
  -- 媒体文件
  featured_image_url TEXT,               -- 特色图片URL
  featured_image_path TEXT,              -- 本地图片路径
  video_url TEXT,                        -- 视频URL
  video_path TEXT,                       -- 本地视频路径
  audio_url TEXT,                        -- 音频URL
  audio_path TEXT,                       -- 本地音频路径
  
  -- AI处理状态
  ai_processing_status TEXT DEFAULT 'pending', -- AI处理状态
  ai_processed_at DATETIME,              -- AI处理完成时间
  ai_processing_error TEXT,              -- AI处理错误信息
  
  -- 状态信息
  processing_status TEXT DEFAULT 'pending', -- 抓取处理状态
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id)
);
```

**AI处理状态值**：
- `pending`：待处理
- `processing`：处理中
- `completed`：已完成
- `failed`：处理失败
- `disabled`：未启用

### 4. 文章翻译表 (article_translations)

**用途**：存储文章的多语言翻译

```sql
CREATE TABLE article_translations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,           -- 文章ID
  language_code TEXT NOT NULL,           -- 语言代码
  translated_title TEXT,                 -- 翻译标题
  translated_subtitle TEXT,              -- 翻译副标题
  translated_content TEXT,               -- 翻译正文
  translation_method TEXT DEFAULT 'ai',  -- 翻译方式
  quality_score REAL,                    -- 翻译质量评分
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  UNIQUE(article_id, language_code)
);
```

**支持的语言代码**：
- `zh`：中文
- `en`：英文
- `ko`：韩文

### 5. 词汇表 (vocabulary)

**用途**：存储日语词汇的详细信息

```sql
CREATE TABLE vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  word TEXT NOT NULL,                    -- 词汇
  reading TEXT,                          -- 读音 (假名)
  meaning_zh TEXT,                       -- 中文释义
  meaning_en TEXT,                       -- 英文释义
  part_of_speech TEXT,                   -- 词性
  jlpt_level TEXT,                       -- JLPT级别
  frequency_rank INTEGER,                -- 使用频率排名
  difficulty_score REAL,                 -- 难度评分
  extraction_method TEXT DEFAULT 'ai',   -- 提取方式
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(word, reading)
);
```

**JLPT级别**：N5（最简单）到N1（最难）

### 6. 文章词汇关联表 (article_vocabulary)

**用途**：记录文章中出现的词汇及其上下文

```sql
CREATE TABLE article_vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  vocabulary_id INTEGER NOT NULL,
  position_start INTEGER,                -- 在文章中的起始位置
  position_end INTEGER,                  -- 在文章中的结束位置
  context TEXT,                          -- 上下文
  is_key_vocabulary BOOLEAN DEFAULT 0,   -- 是否为重点词汇
  extraction_confidence REAL,            -- 提取置信度
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary (id),
  UNIQUE(article_id, vocabulary_id, position_start)
);
```

### 7. 语法点表 (grammar_points)

**用途**：存储日语语法点信息

```sql
CREATE TABLE grammar_points (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  pattern TEXT NOT NULL,                 -- 语法模式
  name TEXT,                             -- 语法名称
  meaning_zh TEXT,                       -- 中文解释
  meaning_en TEXT,                       -- 英文解释
  explanation TEXT,                      -- 详细说明
  jlpt_level TEXT,                       -- JLPT级别
  difficulty_score REAL,                 -- 难度评分
  usage_frequency INTEGER DEFAULT 0,     -- 使用频率
  extraction_method TEXT DEFAULT 'ai',   -- 提取方式
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(pattern)
);
```

### 8. 文章语法点关联表 (article_grammar_points)

**用途**：记录文章中出现的语法点，主要用于学习功能中的"查看原文"链接

```sql
CREATE TABLE article_grammar_points (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,
  grammar_point_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
  FOREIGN KEY (grammar_point_id) REFERENCES grammar_points (id),
  UNIQUE(article_id, grammar_point_id)
);
```

### 9. 用户学习记录表 (user_learning_records)

**用途**：跟踪用户的学习进度和状态

```sql
CREATE TABLE user_learning_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,              -- 用户ID
  article_id INTEGER,
  vocabulary_id INTEGER,
  grammar_point_id INTEGER,
  record_type TEXT NOT NULL,             -- 记录类型
  status TEXT DEFAULT 'new',             -- 学习状态
  -- FSRS fields - 科学的间隔重复算法字段
  fsrs_due DATETIME,                     -- 下次复习时间
  fsrs_stability REAL,                   -- 记忆稳定性
  fsrs_difficulty REAL,                  -- 内容难度
  fsrs_elapsed_days INTEGER,             -- 已过天数
  fsrs_scheduled_days INTEGER,           -- 计划间隔天数
  fsrs_learning_steps INTEGER,           -- 学习步骤
  fsrs_reps INTEGER DEFAULT 0,           -- 复习次数
  fsrs_lapses INTEGER DEFAULT 0,         -- 遗忘次数
  fsrs_state TEXT DEFAULT 'New',         -- 卡片状态
  fsrs_last_review DATETIME,             -- 最后复习时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id),
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary (id),
  FOREIGN KEY (grammar_point_id) REFERENCES grammar_points (id)
);
```

**记录类型**：
- `article`：文章学习记录
- `vocabulary`：词汇学习记录
- `grammar`：语法学习记录

**学习状态**：
- `new`：新内容
- `learning`：学习中
- `reviewing`：复习中
- `mastered`：已掌握
- `difficult`：困难

### 10. 抓取日志表 (scraping_logs) - 已优化

**用途**：记录抓取过程的关键日志信息

```sql
CREATE TABLE scraping_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER,
  session_id TEXT,                       -- 抓取会话ID
  log_level TEXT DEFAULT 'INFO',         -- 日志级别 (INFO/WARN/ERROR)
  message TEXT NOT NULL,                 -- 日志消息
  url TEXT,                              -- 相关URL
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources (id)
);
```

**优化说明**：
- 删除了 `details` 字段（减少存储开销）
- 删除了 `processing_time` 字段（基本未使用）
- 简化了日志级别，只保留关键信息
- 添加了自动清理机制（默认保留30天）

**日志级别**：
- `DEBUG`：调试信息
- `INFO`：一般信息
- `WARN`：警告信息
- `ERROR`：错误信息

### 11. AI处理队列表 (ai_processing_queue)

**用途**：管理AI处理任务的队列

```sql
CREATE TABLE ai_processing_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER NOT NULL,           -- 文章ID
  processing_type TEXT NOT NULL,         -- 处理类型
  priority INTEGER DEFAULT 0,            -- 优先级
  status TEXT DEFAULT 'pending',         -- 状态
  retry_count INTEGER DEFAULT 0,         -- 重试次数
  error_message TEXT,                    -- 错误信息
  scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 计划处理时间
  started_at DATETIME,                   -- 开始处理时间
  completed_at DATETIME,                 -- 完成时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE
);
```

## 索引设计

### 主要索引
```sql
-- 文章表索引
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_publish_time ON articles(publish_time);
CREATE INDEX idx_articles_rss_source ON articles(rss_source_id);
CREATE INDEX idx_articles_ai_status ON articles(ai_processing_status);

-- 词汇表索引
CREATE INDEX idx_vocabulary_word ON vocabulary(word);
CREATE INDEX idx_vocabulary_jlpt ON vocabulary(jlpt_level);

-- 学习记录索引
CREATE INDEX idx_learning_user ON user_learning_records(user_id);
CREATE INDEX idx_learning_type ON user_learning_records(record_type);
CREATE INDEX idx_learning_status ON user_learning_records(status);

-- 日志表索引
CREATE INDEX idx_logs_session ON scraping_logs(session_id);
CREATE INDEX idx_logs_time ON scraping_logs(created_at);

-- AI队列索引
CREATE INDEX idx_ai_queue_status ON ai_processing_queue(status);
CREATE INDEX idx_ai_queue_priority ON ai_processing_queue(priority);
```

## 数据完整性约束

### 外键约束
- 所有关联表都设置了适当的外键约束
- 使用`ON DELETE CASCADE`确保数据一致性
- 防止孤立记录的产生

### 唯一性约束
- 文章URL的唯一性
- 词汇的唯一性（词汇+读音组合）
- 用户学习记录的唯一性

### 检查约束
```sql
-- 添加检查约束（SQLite 3.38+支持）
ALTER TABLE user_learning_records 
ADD CONSTRAINT chk_proficiency_level 
CHECK (proficiency_level >= 0 AND proficiency_level <= 5);

ALTER TABLE ai_processing_queue 
ADD CONSTRAINT chk_retry_count 
CHECK (retry_count >= 0 AND retry_count <= 10);
```

## 数据库优化策略

### 查询优化
1. **使用索引**：为常用查询字段创建索引
2. **限制结果集**：使用LIMIT减少数据传输
3. **避免SELECT ***：只查询需要的字段
4. **使用JOIN**：减少多次查询

### 存储优化
1. **数据类型选择**：使用合适的数据类型
2. **TEXT vs BLOB**：文本内容使用TEXT类型
3. **NULL值处理**：合理使用DEFAULT值
4. **数据压缩**：对大文本内容考虑压缩

### 维护策略
```sql
-- 定期清理过期日志
DELETE FROM scraping_logs 
WHERE created_at < datetime('now', '-30 days');

-- 重建索引
REINDEX;

-- 分析表统计信息
ANALYZE;

-- 清理数据库碎片
VACUUM;
```

## 备份和恢复

### 备份策略
```bash
# 完整备份
cp data/nhk_news_new.db data/backup/nhk_news_$(date +%Y%m%d_%H%M%S).db

# 增量备份（使用SQLite的备份API）
sqlite3 data/nhk_news_new.db ".backup data/backup/incremental_backup.db"
```

### 恢复策略
```bash
# 从备份恢复
cp data/backup/nhk_news_20241201_120000.db data/nhk_news_new.db

# 验证数据完整性
sqlite3 data/nhk_news_new.db "PRAGMA integrity_check;"
```

## 数据迁移

### 版本升级脚本
```sql
-- 从旧版本迁移到新版本
BEGIN TRANSACTION;

-- 添加新字段
ALTER TABLE articles ADD COLUMN ai_processing_status TEXT DEFAULT 'pending';
ALTER TABLE rss_sources ADD COLUMN enable_ai_processing BOOLEAN DEFAULT 0;

-- 迁移数据
UPDATE articles SET ai_processing_status = 'disabled' WHERE ai_processing_status IS NULL;

-- 创建新表
CREATE TABLE ai_processing_queue (
  -- 表结构定义
);

COMMIT;
```

这个数据库设计文档详细描述了系统的数据存储架构，为开发者提供了完整的数据库理解和操作指南。