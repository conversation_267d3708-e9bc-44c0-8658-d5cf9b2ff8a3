import { NextResponse } from 'next/server';
import { db<PERSON>anager, prisma } from '@/lib/server/database';

// GET /api/rss-sources - 获取所有RSS源
export async function GET() {
  try {
    const rssSources = await prisma.rss_sources.findMany({
      include: {
        rss_field_mappings: true,
        _count: {
          select: {
            articles: true
          }
        }
      },
      orderBy: { created_at: 'desc' }
    });
    
    return NextResponse.json({
      success: true,
      data: rssSources,
      count: rssSources.length
    });
  } catch (error: any) {
    console.error('获取RSS源失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      message: '获取RSS源失败'
    }, { status: 500 });
  }
}
