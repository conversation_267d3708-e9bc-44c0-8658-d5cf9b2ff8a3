'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { useAuth } from '@/contexts/AuthContext';
import { NewsProvider } from '@/contexts/NewsContext';
import { Loader } from 'lucide-react';

export default function MainLayout({ children }: { children: React.ReactNode }) {
  const [sidebarMinimized, setSidebarMinimized] = useState<boolean>(false);
  const router = useRouter();
  const pathname = usePathname();
  const { session, status, signOut } = useAuth();

  // 检查认证状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router, pathname]); // 在导航时重新检查

  const handleLogout = () => {
    signOut();
  };

  const handleToggleSidebar = () => {
    setSidebarMinimized(!sidebarMinimized);
  };

  // 如果正在加载或未认证，显示加载器
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50">
        <Loader className="h-10 w-10 animate-spin text-indigo-600" />
      </div>
    );
  }

  // 检查是否在新闻页面
  const isNewsPage = pathname === '/';

  const content = (
    <div className="min-h-screen bg-gray-50">
      <Header
        currentUser={session?.user}
        onLogout={handleLogout}
      />
      <div className="flex">
        <Sidebar
          isMinimized={sidebarMinimized}
          onToggleMinimize={handleToggleSidebar}
        />
        <main className={`flex-1 p-6 pt-20 md:pt-6 transition-all duration-300 ease-in-out ${
          sidebarMinimized ? 'md:ml-16' : 'md:ml-64'
        } pb-20 md:pb-6`}>
          {children}
        </main>
      </div>
    </div>
  );

  // 如果在新闻页面，用NewsProvider包装
  if (isNewsPage) {
    return <NewsProvider>{content}</NewsProvider>;
  }

  return content;
}
