
import { z } from 'zod';

export const GrammarAnalysisInputSchema = z.object({
  text: z.string().describe('The Japanese text to be analyzed for grammar points.'),
  modelName: z.string().optional().describe('The AI model to use for the analysis.'),
});
export type GrammarAnalysisInput = z.infer<typeof GrammarAnalysisInputSchema>;

export const GrammarPointSchema = z.object({
  pattern: z.string().describe('The identified grammar pattern, e.g., "～について".'),
  reading: z.string().optional().describe('The hiragana reading of the grammar pattern if it contains kanji.'),
  explanation: z.string().describe('A detailed explanation of the grammar pattern\'s meaning and usage in Chinese.'),
  explanationJa: z.string().describe('A detailed explanation of the grammar pattern\'s meaning and usage in Japanese, with furigana for all kanji using HTML ruby tags. e.g., "「～について」は<ruby>話題<rt>わだい</rt></ruby>や<ruby>対象<rt>たいしょう</rt></ruby>を<ruby>表<rt>あらわ</rt></ruby>す<ruby>助詞<rt>じょし</rt></ruby>です。"'),
  examples: z.array(z.string()).describe('Exactly three example sentences in Japanese, each followed by its Chinese translation. e.g., ["この問題について話しましょう - 我们来谈谈关于这个问题的事情吧。"]'),
  commonCollocations: z.array(z.string()).describe('2-3 common collocations or usage patterns with this grammar point. e.g., ["～について話す", "～について考える", "～について書く"]'),
  similarGrammar: z.array(z.object({
    pattern: z.string().describe('A similar grammar pattern.'),
    difference: z.string().describe('The key difference between this pattern and the main pattern, explained in Chinese.'),
    examples: z.array(z.string()).describe('Exactly two example sentences for this similar grammar pattern, each followed by its Chinese translation. e.g., ["彼は勉強に関して真面目です - 他在学习方面很认真。", "この件に関しては後で話します - 关于这件事稍后再谈。"]')
  })).describe('3-5 similar grammar patterns with their differences and example sentences (each with 2 examples).'),
  difficulty: z.string().describe('The JLPT level of the grammar point, e.g., "N4".'),
});

export const GrammarAnalysisOutputSchema = z.array(GrammarPointSchema).describe('An array of identified grammar points from the text.');
export type GrammarAnalysisOutput = z.infer<typeof GrammarAnalysisOutputSchema>;
