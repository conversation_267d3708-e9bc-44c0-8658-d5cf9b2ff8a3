/**
 * 完全重置数据库脚本
 * 此脚本执行以下步骤：
 * 1. 删除所有迁移文件
 * 2. 删除现有数据库文件
 * 3. 重新创建数据库结构
 * 4. 填充种子数据
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始完全重置数据库...');

try {
  // 步骤 1: 删除迁移文件夹
  console.log('\n📁 步骤 1: 删除现有迁移文件...');
  const migrationsDir = path.join(__dirname, '..', 'prisma', 'migrations');
  if (fs.existsSync(migrationsDir)) {
    fs.rmSync(migrationsDir, { recursive: true, force: true });
    console.log('✅ 迁移文件已删除');
  } else {
    console.log('ℹ️  迁移文件夹不存在，跳过');
  }

  // 步骤 2: 删除数据库文件
  console.log('\n🗄️  步骤 2: 删除现有数据库文件...');
  const dbPath = path.join(__dirname, '..', 'data', 'nhk_news_new.db');
  if (fs.existsSync(dbPath)) {
    fs.unlinkSync(dbPath);
    console.log('✅ 数据库文件已删除');
  } else {
    console.log('ℹ️  数据库文件不存在，跳过');
  }

  // 步骤 3: 创建数据目录（如果不存在）
  console.log('\n📂 步骤 3: 确保数据目录存在...');
  const dataDir = path.join(__dirname, '..', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log('✅ 数据目录已创建');
  } else {
    console.log('ℹ️  数据目录已存在');
  }

  // 步骤 4: 生成Prisma客户端
  console.log('\n🔧 步骤 4: 生成Prisma客户端...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  // 步骤 5: 创建初始迁移
  console.log('\n🏗️  步骤 5: 创建初始迁移...');
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });

  // 步骤 6: 填充种子数据
  console.log('\n🌱 步骤 6: 填充种子数据...');
  execSync('npx prisma db seed', { stdio: 'inherit' });

  console.log('\n🎉 数据库重置完成！');
  console.log('📍 数据库位置:', dbPath);
  console.log('📁 迁移文件位置:', migrationsDir);
  
} catch (error) {
  console.error('\n❌ 数据库重置过程中发生错误:', error.message);
  console.log('\n🔧 可能的解决方案:');
  console.log('1. 确保没有其他进程正在使用数据库');
  console.log('2. 检查文件权限');
  console.log('3. 手动删除 data/nhk_news_new.db 文件后重试');
  process.exit(1);
}
