'use client';
import React from 'react';

interface TrendDataPoint {
  label: string;
  date: string;
  studied: number;
  scheduled: number;      // 总计划数量（向后兼容）
  newStudy: number;       // 新学习数量
  review: number;         // 正常复习数量
  overdueReview: number;  // 逾期复习数量
  isPast: boolean;
}

interface StudyTrendChartProps {
  data: TrendDataPoint[];
  period: 'week' | 'month' | 'year';
  onPeriodChange: (period: 'week' | 'month' | 'year') => void;
  loading?: boolean;
}

const StudyTrendChart: React.FC<StudyTrendChartProps> = ({ 
  data, 
  period, 
  onPeriodChange, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="h-6 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded w-48 animate-pulse"></div>
        </div>
        <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">学习/复习趋势</h3>
        <p className="text-gray-500 text-center py-8">暂无趋势数据</p>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => Math.max(d.studied, d.scheduled)));
  const todayIndex = data.findIndex(d => {
    const today = new Date().toISOString().split('T')[0];
    return d.date === today;
  });

  const getPeriodDescription = () => {
    switch (period) {
      case 'week':
        return '过去一周与未来一周的学习计划';
      case 'month':
        return '过去一月与未来半月的学习计划';
      case 'year':
        return '过去一年与未来三月的学习计划';
      default:
        return '';
    }
  };

  // 计算统计数据
  const pastData = data.filter(d => d.isPast);
  const futureData = data.filter(d => !d.isPast);
  const totalStudied = pastData.reduce((sum, d) => sum + d.studied, 0);
  const totalNewStudy = futureData.reduce((sum, d) => sum + d.newStudy, 0);
  const totalReview = futureData.reduce((sum, d) => sum + d.review, 0);
  const totalOverdueReview = futureData.reduce((sum, d) => sum + d.overdueReview, 0);
  const avgStudied = pastData.length > 0 ? Math.round(totalStudied / pastData.length) : 0;

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">学习/复习趋势</h3>
          <p className="text-sm text-gray-500 mt-1">{getPeriodDescription()}</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex bg-gray-50 rounded-xl p-1 border">
            <button
              onClick={() => onPeriodChange('week')}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                period === 'week' 
                  ? 'bg-white text-indigo-600 shadow-sm border border-indigo-100' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              一周
            </button>
            <button
              onClick={() => onPeriodChange('month')}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                period === 'month' 
                  ? 'bg-white text-indigo-600 shadow-sm border border-indigo-100' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              一月
            </button>
            <button
              onClick={() => onPeriodChange('year')}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                period === 'year' 
                  ? 'bg-white text-indigo-600 shadow-sm border border-indigo-100' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              一年
            </button>
          </div>
        </div>
      </div>
      
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gradient-to-t from-blue-600 to-blue-400 rounded-sm shadow-sm"></div>
            <span className="text-gray-700 font-medium">已学习/复习</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gradient-to-t from-green-400 to-green-300 rounded-sm shadow-sm"></div>
            <span className="text-gray-700 font-medium">计划学习</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gradient-to-t from-purple-400 to-purple-300 rounded-sm shadow-sm"></div>
            <span className="text-gray-700 font-medium">计划复习</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gradient-to-t from-red-500 to-red-400 rounded-sm shadow-sm"></div>
            <span className="text-gray-700 font-medium">逾期复习</span>
          </div>
        </div>
        <div className="text-xs text-gray-500">
          最大值: {maxValue}
        </div>
      </div>

      <div className="relative" style={{ height: '300px', overflow: 'hidden' }}>
        {/* 背景网格线 */}
        <div className="absolute inset-0 flex flex-col justify-between h-64 pointer-events-none">
          {[0, 1, 2, 3, 4].map(i => (
            <div key={i} className="border-t border-gray-100 w-full"></div>
          ))}
        </div>

        {/* Y轴标签 */}
        <div className="absolute left-2 top-0 h-64 flex flex-col justify-between text-xs text-gray-400">
          {[4, 3, 2, 1, 0].map(i => {
            const value = Math.round((maxValue * i) / 4);
            return <span key={i} className="bg-white px-1 rounded">{value}</span>;
          })}
        </div>

        <div className="overflow-x-auto overflow-y-hidden h-full">
          <div className="flex items-end justify-center space-x-2 min-w-max px-8 h-full">
            {data.map((item, index) => {
              const studiedHeight = maxValue > 0 ? (item.studied / maxValue) * 240 : 0;
              const isToday = index === todayIndex;
              
              return (
                <div key={index} className="flex flex-col items-center group relative">
                  {/* 柱状图 */}
                  <div
                    className="relative flex flex-col items-center justify-end cursor-pointer hover:bg-gray-50 hover:bg-opacity-50 rounded-lg p-1 transition-colors duration-200"
                    style={{ height: '280px' }}
                    title={item.isPast ? (
                      `已完成: ${item.studied}`
                    ) : (
                      `学习: ${item.newStudy} | 复习: ${item.review}${item.overdueReview > 0 ? ` | 逾期: ${item.overdueReview}` : ''}`
                    )}
                  >
                    {item.isPast ? (
                      // 过去：只显示已完成的学习
                      <div
                        className={`w-8 bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-lg shadow-sm transition-all duration-300 group-hover:shadow-md ${
                          isToday ? 'ring-2 ring-blue-300 ring-opacity-50' : ''
                        }`}
                        style={{ height: `${studiedHeight}px`, minHeight: item.studied > 0 ? '4px' : '0px' }}
                      />
                    ) : (
                      // 今天和未来：显示学习+正常复习+逾期复习的堆叠柱状图
                      <div className="flex flex-col items-center justify-end">
                        {/* 逾期复习部分（最上层 - 红色） */}
                        {item.overdueReview > 0 && (
                          <div
                            className={`w-8 bg-gradient-to-t from-red-500 to-red-400 shadow-sm transition-all duration-300 group-hover:shadow-md ${
                              isToday ? 'ring-2 ring-red-300 ring-opacity-50' : ''
                            }`}
                            style={{
                              height: `${(item.overdueReview / maxValue) * 240}px`,
                              minHeight: item.overdueReview > 0 ? '4px' : '0px',
                              borderRadius: '8px 8px 0 0'
                            }}
                          />
                        )}
                        {/* 正常复习部分（中上层 - 紫色） */}
                        {item.review > 0 && (
                          <div
                            className={`w-8 bg-gradient-to-t from-purple-400 to-purple-300 shadow-sm transition-all duration-300 group-hover:shadow-md ${
                              isToday ? 'ring-2 ring-purple-300 ring-opacity-50' : ''
                            }`}
                            style={{
                              height: `${(item.review / maxValue) * 240}px`,
                              minHeight: item.review > 0 ? '4px' : '0px',
                              borderRadius: (item.overdueReview > 0 || item.newStudy > 0) ? '0' : '8px 8px 0 0'
                            }}
                          />
                        )}
                        {/* 学习部分（底层 - 绿色） */}
                        {item.newStudy > 0 && (
                          <div
                            className={`w-8 bg-gradient-to-t from-green-400 to-green-300 shadow-sm transition-all duration-300 group-hover:shadow-md ${
                              isToday ? 'ring-2 ring-green-300 ring-opacity-50' : ''
                            }`}
                            style={{
                              height: `${(item.newStudy / maxValue) * 240}px`,
                              minHeight: item.newStudy > 0 ? '4px' : '0px',
                              borderRadius: (item.review > 0 || item.overdueReview > 0) ? '0' : '8px 8px 0 0'
                            }}
                          />
                        )}
                      </div>
                    )}
                    

                  </div>
                  
                  {/* 日期标签 */}
                  <div className={`text-xs text-center transition-colors duration-200 ${
                    isToday ? 'text-indigo-600 font-semibold' : 'text-gray-500 group-hover:text-gray-700'
                  }`}>
                    <div className={`${period === 'year' ? '' : 'transform -rotate-45 origin-center'} whitespace-nowrap`}>
                      {item.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* 统计摘要 */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-blue-600">{totalStudied}</div>
            <div className="text-xs text-gray-500">总已完成</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-green-600">{totalNewStudy}</div>
            <div className="text-xs text-gray-500">计划学习</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-purple-600">{totalReview}</div>
            <div className="text-xs text-gray-500">计划复习</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-red-600">{totalOverdueReview}</div>
            <div className="text-xs text-gray-500">逾期复习</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-600">{avgStudied}</div>
            <div className="text-xs text-gray-500">日均完成</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudyTrendChart;
