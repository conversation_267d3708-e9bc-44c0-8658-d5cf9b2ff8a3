# 系统设置功能说明

## 1. 功能概述

### 1.1 主要功能
系统设置是平台的全局配置管理中心，提供AI模型配置、API密钥管理、系统参数设置等功能。管理员可以通过此模块调整系统行为和性能参数。

### 1.2 核心特性
- **AI模型管理**: 配置和切换AI模型
- **API密钥管理**: 管理外部服务的访问密钥
- **系统参数配置**: 调整全局系统设置
- **显示选项**: 自定义界面显示参数
- **性能调优**: 优化系统运行参数
- **安全配置**: 管理访问控制和安全选项

### 1.3 适用场景
- 系统初始化和配置
- AI服务的切换和优化
- 性能参数的调整
- 安全策略的配置
- 用户体验的定制

## 2. 使用指南

### 2.1 配置AI模型
1. 进入系统设置页面
2. 选择"AI模型配置"选项卡
3. 查看可用的AI模型列表
4. 设置默认文本生成模型
5. 配置模型参数和限制
6. 保存配置并测试

### 2.2 管理API密钥
1. 进入"API密钥管理"部分
2. 查看现有密钥列表
3. 添加新的API密钥
4. 设置密钥优先级和状态
5. 测试密钥有效性
6. 配置密钥轮换策略

### 2.3 调整系统参数
1. 选择"系统参数"选项卡
2. 修改分页和显示设置
3. 调整缓存和性能参数
4. 配置日志和监控选项
5. 设置数据保留策略
6. 应用配置更改

### 2.4 自定义显示选项
1. 进入"显示设置"部分
2. 调整界面主题和布局
3. 配置语言和本地化
4. 设置默认筛选器
5. 自定义用户偏好
6. 保存个性化设置

## 3. 界面详解

### 3.1 系统设置主页面 (`/settings`)

#### 页面布局
- **左侧**: 设置分类导航
- **主体**: 配置表单和选项
- **右侧**: 帮助信息和状态显示

#### 设置分类导航
| 分类名称 | 图标 | 说明 | 权限要求 |
|----------|------|------|----------|
| AI模型配置 | 🤖 | AI服务和模型管理 | 管理员 |
| API密钥管理 | 🔑 | 外部服务密钥配置 | 管理员 |
| 系统参数 | ⚙️ | 全局系统设置 | 管理员 |
| 显示设置 | 🎨 | 界面和用户体验 | 用户 |
| 安全配置 | 🔒 | 安全和权限设置 | 超级管理员 |

### 3.2 AI模型配置

#### 模型列表显示
| 显示列 | 数据库字段 | 格式 | 说明 |
|--------|------------|------|------|
| 模型ID | ai_models.model_id | 文本 | 模型的唯一标识符 |
| 显示名称 | ai_models.display_name | 文本 | 用户友好的模型名称 |
| 描述 | ai_models.description | 文本 | 模型功能和特性说明 |
| 类型 | ai_models.model_type | 标签 | text/image/audio等 |
| 状态 | ai_models.is_enabled | 开关 | 启用/禁用状态 |
| 默认模型 | system_settings | 标识 | 是否为默认选择 |

#### 模型配置选项
| 配置项 | 设置键 | 数据类型 | 默认值 | 说明 |
|--------|--------|----------|--------|------|
| 默认文本模型 | default_text_model | 字符串 | gemini-2.5-flash | 文本生成默认模型 |
| 默认图像模型 | default_image_model | 字符串 | - | 图像处理默认模型 |
| 模型切换策略 | model_fallback_strategy | 字符串 | auto | 模型失败时的备选策略 |
| 请求超时 | ai_request_timeout | 数字 | 30000 | AI请求超时时间（毫秒） |
| 最大重试次数 | ai_max_retries | 数字 | 3 | 请求失败时的重试次数 |

#### 模型同步功能
- **同步按钮**: 从AI服务提供商获取最新模型列表
- **状态显示**: 显示同步进度和结果
- **错误处理**: 显示同步失败的原因和解决建议

### 3.3 API密钥管理

#### 密钥列表显示
| 显示列 | 数据库字段 | 显示格式 | 说明 |
|--------|------------|----------|------|
| 名称 | api_keys.name | 文本 | 密钥的描述性名称 |
| 提供商 | api_keys.provider | 标签 | google_gemini/openai等 |
| 密钥 | api_keys.api_key | 脱敏显示 | 显示前4位和后4位 |
| 优先级 | api_keys.priority | 数字 | 使用优先级（数字越小优先级越高） |
| 状态 | api_keys.is_active | 开关 | 启用/禁用状态 |
| 最后使用 | api_keys.last_used_at | 相对时间 | 最近使用时间 |
| 错误信息 | api_keys.last_error | 文本 | 最近的错误信息 |

#### 密钥配置表单
| 字段名 | 数据库字段 | 验证规则 | 说明 |
|--------|------------|----------|------|
| 密钥名称 | api_keys.name | 必填，1-100字符 | 便于识别的名称 |
| API密钥 | api_keys.api_key | 必填，格式验证 | 实际的API密钥 |
| 提供商 | api_keys.provider | 必选 | 服务提供商类型 |
| 优先级 | api_keys.priority | 数字，0-100 | 使用优先级 |
| 启用状态 | api_keys.is_active | 布尔值 | 是否启用此密钥 |

#### 密钥轮换策略
| 策略名称 | 说明 | 配置参数 |
|----------|------|----------|
| 优先级轮换 | 按优先级顺序使用 | priority字段 |
| 负载均衡 | 平均分配请求 | 自动计算 |
| 故障转移 | 失败时切换到备用 | 错误阈值 |
| 配额管理 | 基于使用量轮换 | 每日/月配额 |

### 3.4 系统参数配置

#### 显示和分页设置
| 参数名称 | 设置键 | 数据类型 | 默认值 | 说明 |
|----------|--------|----------|--------|------|
| 每页文章数 | articles_per_page | 整数 | 10 | 文章列表每页显示数量 |
| 每页词汇数 | vocabulary_per_page | 整数 | 20 | 词汇列表每页显示数量 |
| 搜索结果数 | search_results_limit | 整数 | 50 | 搜索结果最大数量 |
| 缓存过期时间 | cache_expiry_minutes | 整数 | 60 | 缓存数据过期时间 |

#### 性能和优化设置
| 参数名称 | 设置键 | 数据类型 | 默认值 | 说明 |
|----------|--------|----------|--------|------|
| 数据库连接池 | db_pool_size | 整数 | 10 | 数据库连接池大小 |
| 请求限流 | rate_limit_per_minute | 整数 | 100 | 每分钟最大请求数 |
| 文件上传限制 | max_upload_size_mb | 整数 | 10 | 文件上传大小限制 |
| 日志保留天数 | log_retention_days | 整数 | 30 | 日志文件保留时间 |

#### 功能开关设置
| 功能名称 | 设置键 | 数据类型 | 默认值 | 说明 |
|----------|--------|----------|--------|------|
| 启用用户注册 | enable_user_registration | 布尔值 | false | 是否允许新用户注册 |
| 启用AI处理 | enable_ai_processing | 布尔值 | true | 是否启用AI内容处理 |
| 启用媒体下载 | enable_media_download | 布尔值 | true | 是否下载媒体文件 |
| 启用数据导出 | enable_data_export | 布尔值 | true | 是否允许数据导出 |

### 3.5 显示设置

#### 界面主题配置
| 设置项 | 选项值 | 说明 |
|--------|--------|------|
| 主题模式 | light/dark/auto | 明亮/暗黑/自动 |
| 主色调 | blue/green/purple | 界面主色调 |
| 字体大小 | small/medium/large | 文字显示大小 |
| 紧凑模式 | 开启/关闭 | 界面元素间距 |

#### 语言和本地化
| 设置项 | 选项值 | 说明 |
|--------|--------|------|
| 界面语言 | zh-CN/en-US/ja-JP | 用户界面语言 |
| 时区设置 | 时区列表 | 时间显示时区 |
| 日期格式 | 格式选项 | 日期显示格式 |
| 数字格式 | 格式选项 | 数字显示格式 |

## 4. 技术实现

### 4.1 核心代码文件

#### 设置管理组件
- **文件**: `src/components/Settings.tsx`
- **功能**: 系统设置主界面
- **主要功能**:
  - 设置表单渲染和验证
  - 配置数据的读取和保存
  - 实时预览和应用

#### 设置服务
- **文件**: `src/lib/server/settings-service.ts`
- **功能**: 设置数据管理
- **主要方法**:
  - `getSystemSetting(key)`: 获取系统设置
  - `updateSystemSetting(key, value)`: 更新设置
  - `getAIModels()`: 获取AI模型列表
  - `syncAIModels()`: 同步AI模型

#### API密钥管理
- **文件**: `src/lib/server/api-key-manager.ts`
- **功能**: API密钥的管理和轮换
- **主要功能**:
  - 密钥验证和测试
  - 负载均衡和故障转移
  - 使用统计和监控

### 4.2 数据库表结构

#### system_settings表（系统设置）
```sql
CREATE TABLE system_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  data_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
  is_public BOOLEAN DEFAULT false, -- 是否可被前端访问
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### ai_models表（AI模型）
```sql
CREATE TABLE ai_models (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  model_id TEXT UNIQUE NOT NULL,
  display_name TEXT,
  description TEXT,
  model_type TEXT, -- 'text', 'image', 'audio', 'multimodal'
  provider TEXT, -- 'google', 'openai', 'anthropic'
  is_enabled BOOLEAN DEFAULT true,
  config_json TEXT, -- 模型特定配置
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### api_keys表（API密钥）
```sql
CREATE TABLE api_keys (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  api_key TEXT NOT NULL,
  provider TEXT NOT NULL, -- 'google_gemini', 'openai', 'anthropic'
  priority INTEGER DEFAULT 0, -- 优先级，数字越小优先级越高
  is_active BOOLEAN DEFAULT true,
  last_used_at DATETIME,
  last_error TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 4.3 设置管理架构

#### 设置读取和缓存
```typescript
class SettingsManager {
  private cache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();
  
  async getSetting(key: string, defaultValue?: any): Promise<any> {
    // 检查缓存
    if (this.cache.has(key) && this.cacheExpiry.get(key)! > Date.now()) {
      return this.cache.get(key);
    }
    
    // 从数据库读取
    const setting = await prisma.system_settings.findUnique({
      where: { key }
    });
    
    const value = setting ? this.parseValue(setting.value, setting.data_type) : defaultValue;
    
    // 更新缓存
    this.cache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + 5 * 60 * 1000); // 5分钟缓存
    
    return value;
  }
  
  async updateSetting(key: string, value: any): Promise<void> {
    const stringValue = this.stringifyValue(value);
    
    await prisma.system_settings.upsert({
      where: { key },
      update: { value: stringValue, updated_at: new Date() },
      create: { key, value: stringValue }
    });
    
    // 清除缓存
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
  }
}
```

#### API密钥轮换逻辑
```typescript
class APIKeyManager {
  async getActiveKey(provider: string): Promise<string | null> {
    const keys = await prisma.api_keys.findMany({
      where: { provider, is_active: true },
      orderBy: [
        { priority: 'asc' },
        { last_used_at: 'asc' }
      ]
    });
    
    if (keys.length === 0) {
      throw new Error(`No active API keys for provider: ${provider}`);
    }
    
    // 选择优先级最高且最少使用的密钥
    const selectedKey = keys[0];
    
    // 更新使用记录
    await prisma.api_keys.update({
      where: { id: selectedKey.id },
      data: {
        last_used_at: new Date(),
        usage_count: { increment: 1 }
      }
    });
    
    return selectedKey.api_key;
  }
  
  async markKeyError(apiKey: string, error: string): Promise<void> {
    await prisma.api_keys.updateMany({
      where: { api_key: apiKey },
      data: { last_error: error }
    });
  }
}
```

### 4.4 API接口

#### 获取系统设置
- **路径**: `GET /api/settings`
- **返回**: 公开的系统设置列表

#### 更新系统设置
- **路径**: `PUT /api/settings`
- **参数**: `{key: string, value: any}`
- **返回**: 更新结果

#### 获取AI模型列表
- **路径**: `GET /api/ai/models`
- **返回**: 可用的AI模型列表

#### 同步AI模型
- **路径**: `POST /api/ai/models/sync`
- **返回**: 同步结果和新增模型

#### API密钥管理
- **路径**: `GET /api/settings/api-keys` - 获取密钥列表
- **路径**: `POST /api/settings/api-keys` - 添加新密钥
- **路径**: `PUT /api/settings/api-keys/[id]` - 更新密钥
- **路径**: `DELETE /api/settings/api-keys/[id]` - 删除密钥

## 5. 配置说明

### 5.1 默认系统设置

#### 核心配置项
```sql
INSERT INTO system_settings (key, value, description, data_type, is_public) VALUES
('articles_per_page', '10', '每页显示的文章数量', 'number', true),
('default_text_model', 'googleai/gemini-2.5-flash', '默认文本生成模型', 'string', false),
('enable_ai_processing', 'true', '是否启用AI处理', 'boolean', true),
('cache_expiry_minutes', '60', '缓存过期时间（分钟）', 'number', false),
('max_upload_size_mb', '10', '最大上传文件大小（MB）', 'number', true);
```

### 5.2 环境变量配置

#### 必需的环境变量
```env
# 数据库配置
DATABASE_URL="file:./data/nhk_news_new.db"

# AI服务配置
GEMINI_API_KEY="your-gemini-api-key"

# 安全配置
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

## 6. 故障排除

### 6.1 常见问题

#### 设置保存失败
**症状**: 修改设置后无法保存或保存后不生效
**解决方案**:
1. 检查用户权限是否足够
2. 验证设置值的格式是否正确
3. 确认数据库连接正常
4. 清除设置缓存

#### AI模型同步失败
**症状**: 无法获取最新的AI模型列表
**解决方案**:
1. 检查API密钥是否有效
2. 确认网络连接正常
3. 验证API配额是否充足
4. 检查服务提供商状态

#### API密钥轮换异常
**症状**: 密钥切换不正常或频繁失败
**解决方案**:
1. 检查密钥优先级设置
2. 验证密钥有效性
3. 调整轮换策略
4. 监控使用统计

### 6.2 性能优化

#### 设置缓存优化
```typescript
// 实现分层缓存
class CachedSettingsManager {
  private memoryCache = new Map();
  private redisCache: Redis;
  
  async getSetting(key: string): Promise<any> {
    // L1: 内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // L2: Redis缓存
    const cached = await this.redisCache.get(`setting:${key}`);
    if (cached) {
      const value = JSON.parse(cached);
      this.memoryCache.set(key, value);
      return value;
    }
    
    // L3: 数据库
    const value = await this.loadFromDatabase(key);
    await this.redisCache.setex(`setting:${key}`, 300, JSON.stringify(value));
    this.memoryCache.set(key, value);
    
    return value;
  }
}
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
