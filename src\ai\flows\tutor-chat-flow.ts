
'use server';
/**
 * @fileOverview A flow for handling the AI Tutor chat.
 */
import { ai } from '@/ai/genkit';
import {
  TutorChatInputSchema,
  TutorChatOutputSchema,
  type TutorChatInput,
  type TutorChatOutput
} from '../schemas/tutor-chat-schema';
import { dbManager } from '@/lib/server/database';

// Export types for use in other modules
export type { TutorChatInput, TutorChatOutput };
import type { MessageData } from '@genkit-ai/ai';

const tutorChatFlow = ai.defineFlow(
  {
    name: 'tutorChatFlow',
    inputSchema: TutorChatInputSchema,
    outputSchema: TutorChatOutputSchema,
  },
  async (input) => {
    // Map the simple history format to Genkit's format
    let history: MessageData[] = input.history.map(msg => ({
      role: msg.type === 'ai' ? 'model' : 'user',
      content: [{ text: msg.content }],
    }));

    // 确保对话历史以用户消息开始（Google Generative AI的要求）
    if (history.length > 0 && history[0].role === 'model') {
      // 如果第一条消息是AI的回复，则移除它或添加一个默认的用户消息
      history = [
        { role: 'user', content: [{ text: '你好，我想学习日语。' }] },
        ...history
      ];
    }

    const modelToUse = input.modelName || 'googleai/gemini-1.5-flash';

    // 获取AI温度设置
    const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
    const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

    // 从数据库获取系统提示词
    const systemPrompt = await dbManager.getAIPromptByName('tutor_chat_system');
    const systemContent = systemPrompt?.content || `You are Sakura (小樱), a friendly and expert Japanese language tutor AI.
               You are assisting a Chinese-speaking student.
               Your responses should be encouraging, clear, and helpful.
               Keep your answers concise and easy to understand.
               When explaining grammar or vocabulary, provide simple examples.
               You must always respond in Simplified Chinese.`;

    try {
      const result = await ai.generate({
        model: modelToUse,
        messages: [
          ...history,
          {
            role: 'user',
            content: [{ text: input.message }],
          }
        ],
        system: systemContent,
        config: {
          temperature: temperature
        }
      });

      // 从message中获取响应文本
      const responseText = result.message?.content?.[0]?.text;

      if (!responseText) {
        throw new Error("AI Tutor failed to generate a response.");
      }

      return { response: responseText };
    } catch (error) {
      console.error('AI generation error:', error);
      throw error;
    }
  }
);

// Exported server action wrapper
export async function tutorChat(input: TutorChatInput): Promise<TutorChatOutput> {
  return tutorChatFlow(input);
}
