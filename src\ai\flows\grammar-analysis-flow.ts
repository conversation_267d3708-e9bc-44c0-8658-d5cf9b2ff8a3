
'use server';
/**
 * @fileOverview A flow for analyzing Japanese grammar from a given text.
 */
import { ai } from '@/ai/genkit';
import {
  GrammarAnalysisInputSchema,
  GrammarAnalysisOutputSchema,
  type GrammarAnalysisInput,
  type GrammarAnalysisOutput
} from '../schemas/grammar-analysis-schema';
import { dbManager } from '@/lib/server/database';

// Export types for use in other modules
export type { GrammarAnalysisInput, GrammarAnalysisOutput };

const promptInputSchema = GrammarAnalysisInputSchema.omit({ modelName: true });

// 动态创建语法分析提示词的函数
async function createGrammarAnalysisPrompt() {
  const [systemPrompt, mainPrompt] = await Promise.all([
    dbManager.getAIPromptByName('grammar_analysis_system'),
    dbManager.getAIPromptByName('grammar_analysis_prompt')
  ]);

  if (!systemPrompt || !mainPrompt) {
    throw new Error('无法从数据库获取语法分析提示词');
  }

  return ai.definePrompt({
    name: 'grammarAnalysisPrompt',
    input: { schema: promptInputSchema },
    output: { schema: GrammarAnalysisOutputSchema },
    system: systemPrompt.content,
    prompt: mainPrompt.content,
  });
}


const grammarAnalysisFlow = ai.defineFlow(
  {
    name: 'grammarAnalysisFlow',
    inputSchema: GrammarAnalysisInputSchema,
    outputSchema: GrammarAnalysisOutputSchema,
  },
  async (input) => {
    const { modelName, ...promptData } = input;
    const modelToUse = modelName || 'googleai/gemini-2.5-flash';

    // 获取AI温度设置
    const temperatureSetting = await dbManager.getSystemSetting('ai_temperature');
    const temperature = temperatureSetting ? parseFloat(temperatureSetting.value) : 0.7;

    // 动态创建提示词
    const grammarAnalysisPrompt = await createGrammarAnalysisPrompt();

    const { output } = await grammarAnalysisPrompt(promptData, {
      model: modelToUse,
      config: {
        temperature: temperature
      }
    });
    if (!output) {
      throw new Error("AI Grammar Analysis failed to produce an output.");
    }
    return output;
  }
);

// Exported server action wrapper
export async function analyzeGrammarText(input: GrammarAnalysisInput): Promise<GrammarAnalysisOutput> {
  return grammarAnalysisFlow(input);
}
