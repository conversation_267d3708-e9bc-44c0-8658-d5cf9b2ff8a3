'use client';
import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { Tag, ChevronDown } from 'lucide-react';

interface CategoryFilterProps {
  className?: string;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({ className = '' }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  
  const [categories, setCategories] = useState<string[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const currentCategory = searchParams.get('category') || '';

  // 获取可用分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryChange = (category: string) => {
    const params = new URLSearchParams(searchParams);
    
    if (category) {
      params.set('category', category);
    } else {
      params.delete('category');
    }
    
    // Reset to first page when changing category
    params.set('page', '1');
    
    router.push(`${pathname}?${params.toString()}`);
    setIsOpen(false);
  };

  const getCurrentCategoryLabel = () => {
    if (!currentCategory) return '全部分类';
    return categories.find(cat => cat === currentCategory) || currentCategory;
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="flex items-center space-x-1.5">
        <Tag className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700 whitespace-nowrap">分类筛选</span>
      </div>

      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={loading}
          className="flex items-center space-x-1 px-2.5 py-1 text-xs border border-gray-300 rounded bg-white hover:bg-gray-50 transition-colors whitespace-nowrap min-w-[80px]"
        >
          <span className="text-gray-700">
            {loading ? '加载中...' : getCurrentCategoryLabel()}
          </span>
          <ChevronDown className={`h-3 w-3 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && !loading && (
          <div className="absolute top-full left-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            <div className="py-1">
              <button
                onClick={() => handleCategoryChange('')}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                  !currentCategory ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700'
                }`}
              >
                全部分类
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors ${
                    currentCategory === category ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 点击外部关闭下拉框 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default CategoryFilter;
