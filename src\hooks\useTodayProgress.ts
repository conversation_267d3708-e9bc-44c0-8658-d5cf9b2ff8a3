import { useState, useEffect } from 'react';

interface TodayProgressData {
  vocabulary: {
    learned: number;
    target: number;
    remaining: number;
    percentage: number;
    reviews: {
      completed: number;
      total: number;
      remaining: number;
      percentage: number;
    };
  };
  grammar: {
    learned: number;
    target: number;
    remaining: number;
    percentage: number;
    reviews: {
      completed: number;
      total: number;
      remaining: number;
      percentage: number;
    };
  };
  newItems: {
    learned: number;
    target: number;
    remaining: number;
    percentage: number;
  };
  reviews: {
    completed: number;
    total: number;
    remaining: number;
    percentage: number;
  };
  overall: {
    totalTasks: number;
    completedTasks: number;
    percentage: number;
  };
}

interface UseTodayProgressReturn {
  progress: TodayProgressData | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useTodayProgress(): UseTodayProgressReturn {
  const [progress, setProgress] = useState<TodayProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProgress = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/learning/today-progress');
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || '获取今日学习进度失败');
      }
      
      if (result.success) {
        setProgress(result.data);
      } else {
        throw new Error(result.message || '获取今日学习进度失败');
      }
    } catch (err: any) {
      console.error('获取今日学习进度失败:', err);
      setError(err.message || '获取今日学习进度失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProgress();
  }, []);

  return {
    progress,
    loading,
    error,
    refetch: fetchProgress
  };
}
