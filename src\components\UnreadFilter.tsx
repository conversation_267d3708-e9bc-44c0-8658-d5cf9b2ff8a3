'use client';
import React from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { Eye, EyeOff } from 'lucide-react';

interface UnreadFilterProps {
  className?: string;
}

export default function UnreadFilter({ className = '' }: UnreadFilterProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  
  const currentUnreadOnly = searchParams.get('unreadOnly') === 'true';

  const handleUnreadFilterChange = (unreadOnly: boolean) => {
    const params = new URLSearchParams(searchParams);
    
    if (unreadOnly) {
      params.set('unreadOnly', 'true');
    } else {
      params.delete('unreadOnly');
    }
    
    // Reset to first page when changing filter
    params.set('page', '1');
    
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="flex items-center space-x-1.5">
        <Eye className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700 whitespace-nowrap">阅读状态</span>
      </div>

      <div className="flex gap-1.5">
        <button
          onClick={() => handleUnreadFilterChange(false)}
          className={`flex items-center space-x-1 px-2.5 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
            !currentUnreadOnly
              ? 'bg-indigo-600 text-white border-indigo-600'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Eye className="h-3 w-3" />
          <span>全部</span>
        </button>
        
        <button
          onClick={() => handleUnreadFilterChange(true)}
          className={`flex items-center space-x-1 px-2.5 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
            currentUnreadOnly
              ? 'bg-indigo-600 text-white border-indigo-600'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          <EyeOff className="h-3 w-3" />
          <span>未读</span>
        </button>
      </div>
    </div>
  );
}
