'use client';

import React from 'react';
import { Volume2, Pause } from 'lucide-react';
import { renderRubyText, renderCollocations, renderRelatedWords } from '@/utils/ruby-utils';

interface VocabularyCardProps {
  vocab: any;
  onVocabSpeak?: (vocab: any) => void;
  onContentSpeak?: (text: string, contentId: string, language?: 'ja-JP' | 'en-US') => void;
  playingVocabId?: string | null;
  playingContentId?: string | null;
  isPlaying?: boolean;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

const VocabularyCard: React.FC<VocabularyCardProps> = ({
  vocab,
  onVocabSpeak,
  onContentSpeak,
  playingVocabId,
  playingContentId,
  isPlaying = false,
  showActions = true,
  compact = false,
  className = ""
}) => {
  const vocabId = `${vocab.word}-${vocab.reading}`;

  return (
    <div className={`${compact ? 'p-3' : 'p-6'} border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* 基本信息 */}
          <div className={`flex items-center space-x-3 ${compact ? 'mb-2' : 'mb-3'}`}>
            <div className="flex items-center space-x-2">
              <span className={`${compact ? 'text-xl' : 'text-2xl'} font-bold text-gray-900`}>{vocab.word}</span>
              <span className={`${compact ? 'text-md' : 'text-lg'} text-gray-600`}>{vocab.reading}</span>
            </div>
            {vocab.part_of_speech && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                {vocab.part_of_speech}
              </span>
            )}
            {(vocab.level || vocab.jlpt_level) && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                {vocab.level || vocab.jlpt_level}
              </span>
            )}
          </div>
          
          {/* 释义 */}
          <div className={`${compact ? 'mb-2' : 'mb-3'}`}>
            <div className="flex items-center gap-2 flex-wrap">
              <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded text-sm font-medium">
                {vocab.meaning || vocab.meaning_zh}
              </span>
              {vocab.meaning_en && (
                <div className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm font-medium flex items-center gap-1">
                  {onContentSpeak && (
                    <button
                      onClick={() => onContentSpeak(vocab.meaning_en, `vocab-en-${vocab.id}`, 'en-US')}
                      className={`p-0.5 rounded transition-all ${
                        playingContentId === `vocab-en-${vocab.id}` && isPlaying
                          ? 'text-blue-800 bg-blue-200'
                          : 'text-blue-500 hover:text-blue-800 hover:bg-blue-200'
                      }`}
                      title="发音英文翻译"
                    >
                      <Volume2 className="h-3 w-3" />
                    </button>
                  )}
                  <span>{vocab.meaning_en}</span>
                </div>
              )}
            </div>
          </div>

          {/* 详细内容 */}
          <div className="space-y-3">
            {/* 中文解释 */}
            {vocab.explanation && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">中文解释</h5>
                <p className="text-gray-600">{vocab.explanation}</p>
              </div>
            )}

            {/* 日语解释 */}
            {vocab.explanationJa && (
              <div className="text-sm">
                <div className="flex items-center gap-2 mb-1">
                  <h5 className="text-xs font-semibold text-gray-700">日语解释</h5>
                  {onContentSpeak && (
                    <button
                      onClick={() => onContentSpeak(vocab.explanationJa, `vocab-ja-${vocab.id}`, 'ja-JP')}
                      className={`p-1 rounded transition-all ${
                        playingContentId === `vocab-ja-${vocab.id}` && isPlaying
                          ? 'text-blue-600 bg-blue-50'
                          : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
                      }`}
                      title="发音日语解释"
                    >
                      <Volume2 className="h-3 w-3" />
                    </button>
                  )}
                </div>
                <div className="text-gray-700 bg-blue-50 p-2 rounded-md border border-blue-100">
                  {renderRubyText(vocab.explanationJa)}
                </div>
              </div>
            )}

            {/* 动词信息 */}
            {vocab.verbInfo && vocab.part_of_speech && (vocab.part_of_speech.includes('動詞') || vocab.part_of_speech.includes('动词') || vocab.part_of_speech.toLowerCase().includes('verb')) && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">动词信息</h5>
                <div className="p-2 bg-gray-50 rounded-md border border-gray-100 space-y-1">
                  {vocab.verbInfo.transitivity && (
                    <p><span className="font-medium text-gray-600">类型:</span> {vocab.verbInfo.transitivity}</p>
                  )}
                  {vocab.verbInfo.conjugations && (
                    <div>
                      <span className="font-medium text-gray-600">变形:</span>
                      <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-1">
                        {Object.entries(vocab.verbInfo.conjugations).map(([form, value]) => (
                          <div key={form} className="text-xs">
                            <span className="text-gray-500">{form}:</span> <span className="font-mono text-gray-800">{value as string}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 常用搭配 */}
            {vocab.commonCollocations && vocab.commonCollocations.length > 0 && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">常用搭配</h5>
                <div className="space-y-2">
                  {vocab.commonCollocations.map((collocation: any, index: number) => (
                    <div key={index} className="bg-blue-50 p-3 rounded-lg border border-blue-100">
                      <div className="font-medium text-blue-800 flex items-center gap-2 flex-wrap">
                        {onContentSpeak && (
                          <button
                            onClick={() => onContentSpeak(collocation.collocation, `vocab-collocation-${vocab.id}-${index}`, 'ja-JP')}
                            className={`p-1 rounded transition-all ${
                              playingContentId === `vocab-collocation-${vocab.id}-${index}` && isPlaying
                                ? 'text-blue-600 bg-blue-50'
                                : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
                            }`}
                            title="发音搭配"
                          >
                            <Volume2 className="h-3 w-3" />
                          </button>
                        )}
                        <span>{renderRubyText(collocation.collocation)}</span>
                        <span className="text-blue-600 text-sm">- {renderRubyText(collocation.meaning)}</span>
                      </div>
                      {collocation.example && (
                        <div className="text-gray-600 text-sm mt-1">
                          {renderRubyText(collocation.example)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 语义网络 */}
            {vocab.relatedWords && (vocab.relatedWords.synonyms?.length || vocab.relatedWords.antonyms?.length || vocab.relatedWords.hypernyms?.length || vocab.relatedWords.hyponyms?.length || vocab.relatedWords.wordFamily?.length || vocab.relatedWords.relatedConcepts?.length) && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">语义网络</h5>
                {renderRelatedWords(vocab.relatedWords, onContentSpeak, playingContentId, isPlaying)}
              </div>
            )}

            {/* 例句 */}
            {vocab.examples && vocab.examples.length > 0 && (
              <div className="text-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-1">例句</h5>
                <div className="space-y-1">
                  {vocab.examples.map((example: string, index: number) => (
                    <div key={index} className="text-xs text-gray-600 bg-gray-50 p-2 rounded-md border border-gray-100">
                      {renderRubyText(example)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* 操作按钮 */}
        {showActions && (
          <div className="flex flex-col items-center space-y-2 ml-4">
            <div className="flex items-center space-x-2">
              {onVocabSpeak && (
                <button
                  onClick={() => onVocabSpeak(vocab)}
                  className={`p-2 rounded-lg transition-all ${
                    playingVocabId === vocabId && isPlaying
                      ? 'text-indigo-600 bg-indigo-50'
                      : 'text-gray-400 hover:text-indigo-600 hover:bg-indigo-50'
                  }`}
                  title="发音"
                >
                  {playingVocabId === vocabId && isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Volume2 className="h-4 w-4" />
                  )}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VocabularyCard;
