import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { SessionProvider } from '@/providers/SessionProvider';
import { AuthProvider } from '@/contexts/AuthContext';
import DevAutoLogin from '@/components/DevAutoLogin';
import DevTools from '@/components/DevTools';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'NHK - AI智能日语新闻学习',
  description: '基于NHK新闻的智能日语学习平台，提供AI辅助学习、词汇标注、语法解析等功能',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <SessionProvider>
          <AuthProvider>
            <DevAutoLogin>
              {children}
              <DevTools />
            </DevAutoLogin>
          </AuthProvider>
        </SessionProvider>
      </body>
    </html>
  );
}