'use client';
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Newspaper,
  BarChart3,
  BookOpen,
  Bot,
  Settings as SettingsIcon,
  ChevronRight,
  Rss,
  Zap,
  ChevronLeft,
  BookText,
  Sparkles,
  TestTube,
  GraduationCap
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNewsSafe } from '@/contexts/NewsContext';
import { useTodayProgress } from '@/hooks/useTodayProgress';

interface SidebarProps {
  isMinimized: boolean;
  onToggleMinimize: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isMinimized,
  onToggleMinimize
}) => {
  const pathname = usePathname();
  const { isAdmin: isAdminFn, status } = useAuth();
  const isAdmin = isAdminFn();

  // 安全获取新闻上下文，如果不在新闻页面则为undefined
  const newsContext = useNewsSafe();

  // 获取今日学习进度
  const { progress, loading: progressLoading } = useTodayProgress();

  const allMenuItems = [
    { id: 'news', href: '/', label: '新闻阅读', icon: Newspaper, adminOnly: false },
    { id: 'dashboard', href: '/dashboard', label: '学习统计', icon: BarChart3, adminOnly: false },
    { id: 'study-new', href: '/study-new', label: '学习模式', icon: GraduationCap, adminOnly: false },
    { id: 'vocabulary', href: '/vocabulary', label: '生词本', icon: BookOpen, adminOnly: false },
    { id: 'grammar', href: '/grammar', label: '语法本', icon: BookText, adminOnly: false },
    { id: 'ai-tutor', href: '/ai-tutor', label: 'AI助教', icon: Bot, adminOnly: false },
    { id: 'rss-manager', href: '/rss-manager', label: 'RSS源管理', icon: Rss, adminOnly: true },
    { id: 'scraper', href: '/scraper', label: '抓取管理', icon: Zap, adminOnly: true },
    { id: 'dev-tools', href: '/dev-tools', label: '开发工具', icon: TestTube, adminOnly: true },
    { id: 'settings', href: '/settings', label: '设置', icon: SettingsIcon, adminOnly: false },
  ];

  // 确保只有在认证状态下才显示菜单项
  const menuItems = status === 'authenticated'
    ? (isAdmin
        ? allMenuItems // 管理员可以看到所有菜单
        : allMenuItems.filter(item => !item.adminOnly)) // 非管理员只能看到非管理员菜单
    : [];

  // 处理新闻阅读按钮点击
  const handleNewsClick = (e: React.MouseEvent) => {
    // 如果当前在新闻页面且有选中的文章，重置到新闻一览
    if (pathname === '/' && newsContext?.selectedArticle) {
      e.preventDefault();
      newsContext.resetToNewsList();
    }
    // 否则正常跳转到新闻页面
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className={`hidden md:block fixed left-0 top-14 h-[calc(100vh-3.5rem)] bg-white border-r border-gray-200 overflow-y-auto transition-all duration-300 ease-in-out z-40 ${
        isMinimized ? 'w-16' : 'w-64'
      }`}>
        {/* 最小化按钮 */}
        <div className="flex justify-end p-2 border-b border-gray-200">
          <button
            onClick={onToggleMinimize}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all"
            title={isMinimized ? '展开侧边栏' : '收起侧边栏'}
          >
            {isMinimized ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </button>
        </div>

        <div className={`${isMinimized ? 'px-2' : 'px-6'} py-4`}>
          <nav className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              // 为新闻阅读按钮添加特殊处理
              const linkProps = item.id === 'news' ? { onClick: handleNewsClick } : {};

              return (
                <Link
                  key={item.id}
                  href={item.href}
                  {...linkProps}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all group relative ${
                    isActive
                      ? 'bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  title={isMinimized ? item.label : ''}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!isMinimized && (
                    <>
                      <span className="font-medium">{item.label}</span>
                      {isActive && (
                        <ChevronRight className="h-4 w-4 ml-auto" />
                      )}
                    </>
                  )}

                  {isMinimized && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                      {item.label}
                    </div>
                  )}
                </Link>
              );
            })}
          </nav>

          {!isMinimized && (
            <div className="mt-8 p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">今日学习进度</h3>
              {progressLoading ? (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>加载中...</span>
                    <span>--/--</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gray-300 h-2 rounded-full animate-pulse" style={{ width: '50%' }}></div>
                  </div>
                </div>
              ) : progress ? (
                <div className="space-y-3">
                  {/* 词汇学习 */}
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>词汇学习</span>
                      <span>{progress.vocabulary.learned}/{progress.vocabulary.target}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress.vocabulary.percentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 语法学习 */}
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>语法学习</span>
                      <span>{progress.grammar.learned}/{progress.grammar.target}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-indigo-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress.grammar.percentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 复习任务 */}
                  {(progress.vocabulary.reviews.total > 0 || progress.grammar.reviews.total > 0) && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-600">
                        <span>复习任务</span>
                        <span>{progress.reviews.completed}/{progress.reviews.total}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress.reviews.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* 总体进度 */}
                  <div className="pt-1 border-t border-emerald-100">
                    <div className="flex justify-between text-xs font-medium text-gray-700">
                      <span>总体完成度</span>
                      <span>{progress.overall.percentage}%</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>暂无数据</span>
                    <span>0/0</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gray-300 h-2 rounded-full" style={{ width: '0%' }}></div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </aside>

      {/* Mobile Bottom Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div className="grid grid-cols-7 gap-0">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.id}
                href={item.href}
                className={`flex flex-col items-center justify-center py-3 px-1 transition-colors ${
                  isActive
                    ? 'text-indigo-600 bg-indigo-50'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title={item.label}
              >
                <Icon className="h-6 w-6" />
              </Link>
            );
          })}
        </div>
      </nav>
    </>
  );
};

export default Sidebar;
