# 用户认证系统功能说明

## 1. 功能概述

### 1.1 主要功能
用户认证系统是平台的基础安全模块，负责用户身份验证、会话管理和权限控制。系统支持用户注册、登录、自动登录（开发模式）等功能。

### 1.2 核心特性
- **安全认证**: 基于NextAuth.js的安全认证机制
- **会话管理**: JWT令牌管理和自动续期
- **权限控制**: 基于角色的访问控制（RBAC）
- **开发便利**: 开发模式下的自动登录功能
- **用户管理**: 用户注册、信息管理

### 1.3 适用场景
- 用户首次访问系统时的身份验证
- 开发阶段跳过繁琐的登录流程
- 不同权限用户的功能访问控制
- 用户会话状态的持久化管理

## 2. 使用指南

### 2.1 用户注册流程
1. 访问注册页面
2. 填写用户名、邮箱、密码
3. 系统验证信息有效性
4. 创建用户账户并自动登录

### 2.2 用户登录流程
1. 访问登录页面 (`/login`)
2. 输入邮箱和密码
3. 点击登录按钮
4. 系统验证后跳转到主页

### 2.3 开发模式自动登录
1. 设置环境变量 `DEV_AUTO_LOGIN="true"`
2. 重启开发服务器
3. 系统自动以管理员身份登录
4. 可通过开发工具面板手动切换状态

### 2.4 退出登录
1. 点击用户头像或菜单中的退出按钮
2. 系统清除会话信息
3. 自动跳转到登录页面

## 3. 界面详解

### 3.1 登录页面 (`/login`)

#### 页面布局
- **左侧**: 系统介绍和特性展示
- **右侧**: 登录表单区域

#### 表单字段
| 字段名 | 类型 | 必填 | 数据库对应 | 说明 |
|--------|------|------|------------|------|
| 邮箱 | email | 是 | users.email | 用户登录标识 |
| 密码 | password | 是 | users.password | 用户密码（明文存储） |

#### 操作按钮
- **登录按钮**: 提交登录表单
- **注册链接**: 跳转到注册页面（如果启用）
- **忘记密码**: 密码重置功能（待实现）

### 3.2 开发工具面板

#### 触发按钮
- **位置**: 页面右下角
- **图标**: 🛠️ 工具图标
- **显示条件**: 仅在开发模式下显示

#### 面板内容
| 区域 | 内容 | 数据来源 |
|------|------|----------|
| 登录状态 | 当前用户信息 | NextAuth session |
| 快速操作 | 登录/退出按钮 | 本地操作 |
| 环境信息 | 运行环境和配置 | 环境变量 |
| 使用提示 | 功能说明 | 静态内容 |

### 3.3 用户信息显示

#### Header组件中的用户信息
| 显示项 | 数据库字段 | 说明 |
|--------|------------|------|
| 用户名 | users.name | 显示用户姓名 |
| 用户头像 | users.image | 用户头像图片 |
| 用户角色 | users.role | admin/user等角色 |

## 4. 技术实现

### 4.1 核心代码文件

#### 认证配置
- **文件**: `src/app/api/auth/[...nextauth]/route.ts`
- **功能**: NextAuth.js配置，包含认证提供者和回调函数
- **关键逻辑**: 
  - 开发模式自动登录检测
  - 用户密码验证（明文比较）
  - JWT令牌生成和会话管理

#### 中间件
- **文件**: `src/middleware.ts`
- **功能**: 路由保护和重定向逻辑
- **关键逻辑**:
  - 开发模式下的自动重定向
  - 受保护路由的访问控制
  - 登录状态检查

#### 用户服务
- **文件**: `src/lib/server/user-service.ts`
- **功能**: 用户CRUD操作
- **主要方法**:
  - `createUser()`: 创建新用户
  - `getUserByEmail()`: 根据邮箱查找用户
  - `updateUser()`: 更新用户信息

#### 认证上下文
- **文件**: `src/contexts/AuthContext.tsx`
- **功能**: 全局认证状态管理
- **提供功能**:
  - 用户登录状态
  - 用户信息获取
  - 登录/退出操作

### 4.2 数据库表结构

#### users表
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT DEFAULT 'user',
  image TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 字段说明
| 字段 | 类型 | 说明 | 约束 |
|------|------|------|------|
| id | INTEGER | 用户唯一标识 | 主键，自增 |
| name | TEXT | 用户姓名 | 非空 |
| email | TEXT | 用户邮箱 | 唯一，非空 |
| password | TEXT | 用户密码 | 非空，明文存储 |
| role | TEXT | 用户角色 | 默认'user' |
| image | TEXT | 头像URL | 可空 |
| created_at | DATETIME | 创建时间 | 默认当前时间 |
| updated_at | DATETIME | 更新时间 | 默认当前时间 |

### 4.3 API接口

#### 注册接口
- **路径**: `POST /api/auth/register`
- **参数**: `{name, email, password}`
- **返回**: `{message, user}`

#### 登录接口
- **路径**: `POST /api/auth/signin`
- **参数**: `{email, password}`
- **返回**: NextAuth标准响应

#### 会话接口
- **路径**: `GET /api/auth/session`
- **返回**: 当前用户会话信息

## 5. 配置说明

### 5.1 环境变量

#### 必需配置
```env
# NextAuth配置
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_URL_TRUST_HOST=true
```

#### 开发模式配置
```env
# 开发模式自动登录
DEV_AUTO_LOGIN="true"
NEXT_PUBLIC_DEV_AUTO_LOGIN="true"
```

### 5.2 默认用户配置

#### 开发模式默认管理员
- **用户名**: 开发管理员
- **邮箱**: <EMAIL>
- **角色**: admin
- **ID**: 1

### 5.3 安全配置

#### 密码策略
- 当前：明文存储（开发阶段）
- 建议：生产环境使用bcrypt加密

#### 会话配置
- 策略：JWT令牌
- 过期时间：默认30天
- 自动续期：支持

## 6. 故障排除

### 6.1 常见问题

#### 自动登录不生效
**症状**: 开发模式下仍需手动登录
**解决方案**:
1. 检查环境变量设置
2. 确认开发服务器重启
3. 清除浏览器缓存
4. 检查控制台错误信息

#### 登录后立即退出
**症状**: 登录成功但立即被退出
**解决方案**:
1. 检查NEXTAUTH_SECRET配置
2. 确认数据库连接正常
3. 检查用户表数据完整性

#### 权限访问被拒绝
**症状**: 登录用户无法访问某些页面
**解决方案**:
1. 检查用户角色设置
2. 确认中间件配置正确
3. 验证路由保护逻辑

### 6.2 调试方法

#### 开启调试日志
```env
NEXTAUTH_DEBUG=true
```

#### 检查会话状态
使用开发工具面板查看当前登录状态和用户信息

#### 数据库查询
```sql
-- 查看用户表数据
SELECT * FROM users;

-- 检查特定用户
SELECT * FROM users WHERE email = '<EMAIL>';
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
