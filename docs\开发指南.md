# 开发指南

## 快速开始

### 环境要求
- **Node.js**: 16.0.0 或更高版本
- **npm**: 7.0.0 或更高版本
- **操作系统**: Windows、macOS、Linux
- **浏览器**: Chrome 90+、Firefox 88+、Safari 14+、Edge 90+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd nhk-japanese-learning
```

2. **安装依赖**
```bash
npm install
```

3. **初始化数据库**
```bash
npm run init-new-db
```

4. **启动开发服务器**
```bash
# 启动Next.js开发服务器
npm run dev
```

5. **访问应用**
- 应用地址: http://localhost:3000

## 项目脚本说明

### 开发相关
```bash
npm run dev                # 启动Next.js开发服务器
npm run build              # 构建生产版本
npm run start              # 启动生产版本
npm run lint               # 代码检查
```

### 数据库相关
```bash
npm run init-new-db        # 初始化新数据库结构
npm run init-db            # 初始化旧数据库结构
```

### 抓取相关
```bash
npm run scrape             # 运行增强版抓取器
```

### 代码质量
```bash
npm run lint               # 运行ESLint检查
```

## 开发环境配置

### VS Code 推荐扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### VS Code 设置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

## 代码规范

### 文件命名规范
- **React组件**: PascalCase (例: `NewsReader.tsx`)
- **工具函数**: kebab-case (例: `database-api.js`)
- **常量文件**: UPPER_SNAKE_CASE (例: `API_CONSTANTS.js`)
- **样式文件**: kebab-case (例: `component-styles.css`)

### 组件开发规范

#### 函数组件模板
```typescript
import React, { useState, useEffect } from 'react';
import { Icon1, Icon2 } from 'lucide-react';

interface ComponentProps {
  prop1: string;
  prop2?: number;
  onAction?: (data: any) => void;
}

const ComponentName: React.FC<ComponentProps> = ({ 
  prop1, 
  prop2 = 0, 
  onAction 
}) => {
  const [state, setState] = useState<string>('');

  useEffect(() => {
    // 副作用逻辑
  }, []);

  const handleAction = () => {
    // 事件处理逻辑
    if (onAction) {
      onAction(data);
    }
  };

  return (
    <div className="component-container">
      {/* JSX内容 */}
    </div>
  );
};

export default ComponentName;
```

#### Hooks使用规范
```typescript
// 状态管理
const [loading, setLoading] = useState<boolean>(false);
const [data, setData] = useState<DataType[]>([]);
const [error, setError] = useState<string | null>(null);

// 副作用
useEffect(() => {
  const fetchData = async () => {
    try {
      setLoading(true);
      const result = await apiCall();
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, [dependency]);

// 清理函数
useEffect(() => {
  const timer = setInterval(() => {
    // 定时任务
  }, 1000);

  return () => clearInterval(timer);
}, []);
```

### API开发规范

#### Express路由模板
```javascript
import express from 'express';
import cors from 'cors';

const app = express();
const port = 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 路由处理
app.get('/api/resource', async (req, res) => {
  try {
    const { param1, param2 } = req.query;
    
    // 参数验证
    if (!param1) {
      return res.status(400).json({
        success: false,
        error: '缺少必需参数',
        message: 'param1 is required'
      });
    }

    // 业务逻辑
    const result = await processData(param1, param2);
    
    res.json({
      success: true,
      data: result,
      message: '操作成功'
    });
    
  } catch (error) {
    console.error('API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: '服务器内部错误'
    });
  }
});

// 启动服务器
app.listen(port, () => {
  console.log(`API服务器运行在端口 ${port}`);
});
```

#### 数据库操作规范
```javascript
// Promise包装的数据库操作
async function queryDatabase(sql, params = []) {
  return new Promise((resolve, reject) => {
    const db = getConnection();
    db.all(sql, params, (err, rows) => {
      db.close();
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

// 事务操作
async function transactionExample() {
  const db = getConnection();
  
  try {
    await runQuery(db, 'BEGIN TRANSACTION');
    
    const result1 = await runQuery(db, 'INSERT INTO table1 VALUES (?, ?)', [val1, val2]);
    const result2 = await runQuery(db, 'UPDATE table2 SET col = ? WHERE id = ?', [val3, id]);
    
    await runQuery(db, 'COMMIT');
    return { result1, result2 };
    
  } catch (error) {
    await runQuery(db, 'ROLLBACK');
    throw error;
  } finally {
    db.close();
  }
}
```

## 样式开发规范

### Tailwind CSS 使用指南

#### 响应式设计
```jsx
<div className="
  w-full 
  md:w-1/2 
  lg:w-1/3
  p-4 
  md:p-6 
  lg:p-8
">
  {/* 内容 */}
</div>
```

#### 组件样式模式
```jsx
// 基础样式
const baseStyles = "px-4 py-2 rounded-lg font-medium transition-colors";

// 变体样式
const variants = {
  primary: "bg-indigo-600 text-white hover:bg-indigo-700",
  secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
  danger: "bg-red-600 text-white hover:bg-red-700"
};

// 使用
<button className={`${baseStyles} ${variants.primary}`}>
  按钮
</button>
```

#### 自定义组件类
```css
/* 在index.css中定义 */
@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
}
```

## 调试指南

### 前端调试

#### React DevTools
1. 安装React Developer Tools浏览器扩展
2. 在开发者工具中查看组件树
3. 检查组件状态和props

#### 控制台调试
```javascript
// 调试API请求
console.log('API请求:', { url, params, response });

// 调试状态变化
useEffect(() => {
  console.log('状态更新:', { oldState, newState });
}, [state]);

// 性能调试
console.time('操作耗时');
// 执行操作
console.timeEnd('操作耗时');
```

### 后端调试

#### 日志记录
```javascript
// 结构化日志
const log = {
  timestamp: new Date().toISOString(),
  level: 'INFO',
  message: '操作完成',
  data: { userId, action, result }
};
console.log(JSON.stringify(log));

// 错误日志
try {
  // 操作
} catch (error) {
  console.error('操作失败:', {
    error: error.message,
    stack: error.stack,
    context: { userId, operation }
  });
}
```

#### 数据库调试
```javascript
// SQL查询调试
const debugQuery = (sql, params) => {
  console.log('SQL:', sql);
  console.log('参数:', params);
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      const endTime = Date.now();
      console.log(`查询耗时: ${endTime - startTime}ms`);
      console.log(`返回行数: ${rows?.length || 0}`);
      
      if (err) reject(err);
      else resolve(rows);
    });
  });
};
```

## 测试指南

### 单元测试（计划中）
```javascript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import NewsCard from '../components/NewsCard';

test('显示文章标题', () => {
  const article = {
    title: '测试文章',
    content: '测试内容'
  };
  
  render(<NewsCard article={article} />);
  
  expect(screen.getByText('测试文章')).toBeInTheDocument();
});

// API测试示例
test('获取文章列表', async () => {
  const articles = await dbApi.getArticles();
  
  expect(Array.isArray(articles)).toBe(true);
  expect(articles.length).toBeGreaterThan(0);
});
```

### 集成测试
```javascript
// 端到端测试示例
test('完整的文章阅读流程', async () => {
  // 1. 登录
  await login('testuser', 'password');
  
  // 2. 浏览文章列表
  const articles = await getArticles();
  expect(articles.length).toBeGreaterThan(0);
  
  // 3. 点击文章
  await clickArticle(articles[0].id);
  
  // 4. 验证文章内容显示
  expect(screen.getByText(articles[0].title)).toBeInTheDocument();
});
```

## 性能优化

### 前端优化

#### 组件优化
```typescript
// 使用React.memo避免不必要的重渲染
const NewsCard = React.memo<NewsCardProps>(({ article, onRead }) => {
  // 组件内容
});

// 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 使用useCallback缓存函数
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

#### 懒加载
```typescript
// 组件懒加载
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// 图片懒加载
<img 
  src={imageUrl} 
  loading="lazy" 
  alt="描述"
/>
```

### 后端优化

#### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_articles_category ON articles(category);
CREATE INDEX idx_articles_publish_time ON articles(publish_time);

-- 查询优化
SELECT * FROM articles 
WHERE category = ? 
ORDER BY publish_time DESC 
LIMIT 20;
```

#### 缓存策略
```javascript
// 内存缓存
const cache = new Map();

async function getCachedData(key) {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const data = await fetchData(key);
  cache.set(key, data);
  
  // 设置过期时间
  setTimeout(() => cache.delete(key), 5 * 60 * 1000);
  
  return data;
}
```

## 部署指南

### 开发环境部署
```bash
# 1. 安装依赖
npm install

# 2. 初始化数据库
npm run init-new-db

# 3. 启动服务
npm run dev &
npm run start-all-apis &
```

### 生产环境部署
```bash
# 1. 构建前端
npm run build

# 2. 启动生产服务器
npm run preview

# 3. 使用PM2管理进程（推荐）
npm install -g pm2
pm2 start ecosystem.config.js
```

### Docker部署（计划中）
```dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库文件是否存在
ls -la data/

# 重新初始化数据库
npm run init-new-db
```

#### 2. API服务器无法启动
```bash
# 检查端口是否被占用
lsof -i :3001
lsof -i :3002

# 杀死占用进程
kill -9 <PID>
```

#### 3. 前端构建失败
```bash
# 清理缓存
rm -rf node_modules
rm package-lock.json
npm install

# 检查TypeScript错误
npx tsc --noEmit
```

#### 4. RSS抓取失败
```bash
# 检查网络连接
curl -I https://www3.nhk.or.jp/rss/news/cat0.xml

# 查看抓取日志
npm run scrape
```

### 日志分析
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 查看API请求日志
grep "API" logs/app.log | tail -20
```

## 贡献指南

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加用户登录功能"

# 问题修复
git commit -m "fix: 修复文章列表分页问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 样式调整
git commit -m "style: 优化移动端布局"

# 重构代码
git commit -m "refactor: 重构数据库查询逻辑"
```

### 分支管理
```bash
# 主分支
main - 生产环境代码

# 开发分支
develop - 开发环境代码

# 功能分支
feature/user-auth - 用户认证功能
feature/rss-manager - RSS管理功能

# 修复分支
hotfix/login-bug - 登录问题修复
```

### 代码审查清单
- [ ] 代码符合项目规范
- [ ] 添加了必要的注释
- [ ] 处理了错误情况
- [ ] 测试了主要功能
- [ ] 检查了性能影响
- [ ] 更新了相关文档

这个开发指南为新开发者提供了完整的项目上手指导，包括环境配置、代码规范、调试技巧和最佳实践。遵循这些指南可以确保代码质量和团队协作效率。