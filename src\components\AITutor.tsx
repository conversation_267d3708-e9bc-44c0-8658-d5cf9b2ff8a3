
'use client';
import React, { useState, useEffect, useRef } from 'react';
import {
  Bot,
  Send,
  Volume2,
  Lightbulb,
  Sparkles,
  Mic,
  Settings,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  Loader,
  AlertCircle,
  Star,
  Trash2,
} from 'lucide-react';
import { aiTutorChatAction, analyzeGrammarAction } from '@/app/actions';
import type { GrammarAnalysisInput } from '@/ai/flows/grammar-analysis-flow';

interface Conversation {
  type: 'ai' | 'user';
  content: string;
  timestamp: string;
}

interface ChatSession {
  id: number;
  user_id: number;
  session_type: string;
  title: string | null;
  scenario_config: string | null;
  created_at: string;
  updated_at: string;
  chat_messages?: ChatMessage[];
  _count?: {
    chat_messages: number;
  };
}

interface ChatMessage {
  id: number;
  session_id: number;
  role: string;
  content: string;
  metadata: string | null;
  created_at: string;
}

interface PracticeScenario {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  icon: string;
  category: string;
  estimatedTime: number;
}

interface GrammarAnalysis {
  pattern: string;
  explanation: string;
  examples: string[];
  difficulty: string;
}

const AITutor: React.FC = () => {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [selectedMode, setSelectedMode] = useState('chat');
  const [conversation, setConversation] = useState<Conversation[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [grammarText, setGrammarText] = useState('');
  const [grammarAnalysis, setGrammarAnalysis] = useState<GrammarAnalysis[]>([]);
  const [analyzingGrammar, setAnalyzingGrammar] = useState(false);

  const chatContainerRef = useRef<HTMLDivElement>(null);

  // 聊天记录相关状态
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<number | null>(null);
  const [loadingSessions, setLoadingSessions] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  const practiceScenarios: PracticeScenario[] = [
    { id: 'airport', title: '机场办理登机', description: '练习在机场的日常对话，包括值机、安检、登机等场景', difficulty: 'N4', icon: '✈️', category: '旅行', estimatedTime: 15 },
    { id: 'restaurant', title: '餐厅点餐', description: '学习餐厅用餐的礼貌用语，包括点餐、询问、结账等', difficulty: 'N5', icon: '🍜', category: '日常生活', estimatedTime: 10 },
    { id: 'business', title: '商务会议', description: '提升商务日语表达能力，包括会议发言、讨论、汇报等', difficulty: 'N2', icon: '💼', category: '商务', estimatedTime: 25 },
    { id: 'hospital', title: '医院就诊', description: '掌握医疗相关词汇和表达，包括挂号、问诊、取药等', difficulty: 'N3', icon: '🏥', category: '医疗', estimatedTime: 20 },
    { id: 'shopping', title: '购物消费', description: '学习购物时的常用表达，包括询价、试穿、付款等', difficulty: 'N4', icon: '🛍️', category: '日常生活', estimatedTime: 12 },
    { id: 'interview', title: '求职面试', description: '掌握面试时的专业表达，包括自我介绍、回答问题等', difficulty: 'N2', icon: '👔', category: '职场', estimatedTime: 30 }
  ];

  useEffect(() => {
    loadChatSessions();
  }, []);
  
  useEffect(() => {
    // Scroll to the bottom of the chat container when conversation updates
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [conversation, isTyping]);




  // 加载聊天会话列表
  const loadChatSessions = async () => {
    setLoadingSessions(true);
    try {
      const response = await fetch('/api/ai/chat/sessions');
      const result = await response.json();

      if (result.success) {
        setChatSessions(result.data);

        // 如果有会话，加载最新的一个
        if (result.data.length > 0) {
          await loadChatSession(result.data[0].id);
        } else {
          // 如果没有会话，创建一个新的
          await createNewSession();
        }
      }
    } catch (error) {
      console.error('加载聊天会话失败:', error);
      // 如果加载失败，初始化一个新对话
      initializeConversation();
    } finally {
      setLoadingSessions(false);
    }
  };

  // 加载特定聊天会话
  const loadChatSession = async (sessionId: number) => {
    try {
      const response = await fetch(`/api/ai/chat/sessions/${sessionId}`);
      const result = await response.json();

      if (result.success && result.data) {
        setCurrentSessionId(sessionId);

        // 根据会话类型切换模式
        const sessionType = result.data.session_type;
        if (sessionType === 'grammar') {
          setSelectedMode('grammar');
        } else if (sessionType === 'scenario') {
          setSelectedMode('practice');
        } else {
          setSelectedMode('chat');
        }

        // 转换消息格式
        const messages: Conversation[] = result.data.chat_messages.map((msg: ChatMessage) => ({
          type: msg.role === 'assistant' ? 'ai' : msg.role as 'ai' | 'user',
          content: msg.content,
          timestamp: new Date(msg.created_at).toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
        }));

        setConversation(messages);

        // 如果是语法分析会话，清空语法分析结果状态，让聊天记录显示
        if (sessionType === 'grammar') {
          setGrammarAnalysis([]);
        }
      }
    } catch (error) {
      console.error('加载聊天会话失败:', error);
    }
  };

  // 创建新会话
  const createNewSession = async (sessionType: string = 'qa') => {
    try {
      const response = await fetch('/api/ai/chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionType })
      });

      const result = await response.json();

      if (result.success) {
        setCurrentSessionId(result.data.id);

        // 根据会话类型切换模式
        if (sessionType === 'grammar') {
          setSelectedMode('grammar');
        } else if (sessionType === 'scenario') {
          setSelectedMode('practice');
        } else {
          setSelectedMode('chat');
        }

        // 重新加载会话列表
        await loadChatSessions();

        // 初始化新对话
        initializeConversation();
      }
    } catch (error) {
      console.error('创建新会话失败:', error);
      // 如果创建失败，至少初始化本地对话
      initializeConversation();
    }
  };

  // 删除会话
  const deleteChatSession = async (sessionId: number, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation(); // 阻止事件冒泡，避免触发会话加载
    }

    if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
      return;
    }

    try {
      const response = await fetch(`/api/ai/chat/sessions/${sessionId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        // 如果删除的是当前会话，创建新会话
        if (currentSessionId === sessionId) {
          await createNewSession();
        } else {
          // 否则只重新加载会话列表
          await loadChatSessions();
        }
      } else {
        alert('删除会话失败：' + result.error);
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      alert('删除会话失败，请稍后再试');
    }
  };

  // 获取会话预览内容
  const getSessionPreview = (session: ChatSession): string => {
    if (!session.chat_messages || session.chat_messages.length === 0) {
      return '暂无消息';
    }

    // 查找第一条用户消息
    const firstUserMessage = session.chat_messages.find(msg => msg.role === 'user');

    if (!firstUserMessage) {
      return '暂无用户消息';
    }

    let content = firstUserMessage.content;

    // 如果是语法分析会话，提取分析的原文
    if (session.session_type === 'grammar' && content.includes('请分析这个日语句子的语法：')) {
      content = content.replace('请分析这个日语句子的语法：', '').trim();
    }

    // 限制长度，根据侧边栏宽度适当截取
    const maxLength = 30;
    if (content.length > maxLength) {
      return content.substring(0, maxLength) + '...';
    }

    return content;
  };

  const initializeConversation = () => {
    const welcomeMessage: Conversation = {
      type: 'ai',
      content: '你好！我是你的AI日语助教小樱。我可以帮你解答日语学习中的问题，比如语法、词汇、发音等。你也可以和我进行对话练习，或者让我分析日语句子的语法结构。你想聊什么呢？',
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };
    setConversation([welcomeMessage]);
  };

  // 保存消息到数据库
  const saveMessageToDatabase = async (role: string, content: string) => {
    if (!currentSessionId) return;

    try {
      await fetch('/api/ai/chat/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: currentSessionId,
          role,
          content
        })
      });
    } catch (error) {
      console.error('保存消息失败:', error);
    }
  };

  const sendMessage = async () => {
    if (!message.trim() || isTyping) return;

    const userMessage: Conversation = {
      type: 'user',
      content: message,
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };

    // Use a functional update to ensure we're using the latest state
    setConversation(prev => [...prev, userMessage]);
    const currentMessage = message;
    setMessage('');
    setIsTyping(true);

    // 保存用户消息到数据库
    await saveMessageToDatabase('user', currentMessage);

    // Pass the correct history (without the new user message yet)
    // 过滤掉初始的欢迎消息，因为它不应该影响AI的上下文
    const historyForApi = [...conversation]
      .filter((msg, index) => !(index === 0 && msg.type === 'ai' && msg.content.startsWith('你好！我是你的AI日语助教小樱')))
      .map(({ type, content }) => ({ type, content }));

    const result = await aiTutorChatAction({
      history: historyForApi,
      message: currentMessage,
    });

    setIsTyping(false);

    let responseContent: string;
    if (result.success && result.data) {
      responseContent = result.data.response;
    } else {
      responseContent = result.error || '抱歉，我暂时无法回复。请稍后再试。';
    }

    const aiMessage: Conversation = {
      type: 'ai',
      content: responseContent,
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };
    setConversation(prev => [...prev, aiMessage]);

    // 保存AI回复到数据库
    await saveMessageToDatabase('assistant', responseContent);
  };


  const analyzeGrammar = async () => {
    if (!grammarText.trim() || analyzingGrammar) return;

    setAnalyzingGrammar(true);
    setGrammarAnalysis([]);

    // 如果没有当前会话或当前会话不是语法类型，创建新的语法分析会话
    if (!currentSessionId || selectedMode !== 'grammar') {
      await createNewSession('grammar');
    }

    // 添加用户的语法分析请求到聊天记录
    const userMessage: Conversation = {
      type: 'user',
      content: `请分析这个日语句子的语法：${grammarText}`,
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };
    setConversation(prev => [...prev, userMessage]);
    await saveMessageToDatabase('user', userMessage.content);

    const result = await analyzeGrammarAction({ text: grammarText });

    let analysisContent = '';
    if (result.success && result.data) {
      setGrammarAnalysis(result.data);

      // 格式化分析结果为文本
      analysisContent = `语法分析结果：\n\n`;
      result.data.forEach((analysis, index) => {
        analysisContent += `${index + 1}. **${analysis.pattern}** (${analysis.difficulty})\n`;
        analysisContent += `   ${analysis.explanation}\n`;
        if (analysis.examples && analysis.examples.length > 0) {
          analysisContent += `   例句：\n`;
          analysis.examples.forEach(example => {
            analysisContent += `   - ${example}\n`;
          });
        }
        analysisContent += '\n';
      });
    } else {
      console.error(result.error);
      setGrammarAnalysis([{
        pattern: '分析失败',
        explanation: result.error || '无法分析该句子，请稍后再试或检查您的输入。',
        examples: [],
        difficulty: 'N/A'
      }]);
      analysisContent = `分析失败：${result.error || '无法分析该句子，请稍后再试或检查您的输入。'}`;
    }

    // 添加AI的分析结果到聊天记录
    const aiMessage: Conversation = {
      type: 'ai',
      content: analysisContent,
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };
    setConversation(prev => [...prev, aiMessage]);
    await saveMessageToDatabase('assistant', analysisContent);

    setAnalyzingGrammar(false);
    setGrammarText(''); // 清空输入框

    // 分析完成后切换到聊天模式显示结果
    setSelectedMode('chat');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const startPracticeScenario = (scenario: PracticeScenario) => {
    const practiceMessage: Conversation = {
      type: 'ai',
      content: `很好！让我们开始「${scenario.title}」的对话练习。\n\n场景设定：${scenario.description}\n\n我会扮演相关角色，你可以用日语和我对话。不用担心犯错，我会在对话过程中给你建议和纠正。\n\n准备好了吗？让我们开始吧！`,
      timestamp: new Date().toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })
    };
    
    setConversation(prev => [...prev, practiceMessage]);
    setSelectedMode('chat');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI助教</h2>
          <p className="text-gray-600 mt-1">智能日语学习助手，随时为你答疑解惑</p>
        </div>

        <div className="flex items-center space-x-4">

          {/* 模式切换标签页 */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedMode('chat')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                selectedMode === 'chat'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              智能问答
            </button>
            <button
              onClick={() => setSelectedMode('grammar')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                selectedMode === 'grammar'
                  ? 'bg-white text-green-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              语法解析
            </button>
            <button
              onClick={() => setSelectedMode('practice')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                selectedMode === 'practice'
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              情景对话
            </button>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => createNewSession('qa')}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all"
            >
              新对话
            </button>
            <button
              onClick={() => createNewSession('grammar')}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all"
            >
              语法分析
            </button>
          </div>

          <button
            onClick={() => setShowSidebar(!showSidebar)}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all"
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>
      </div>



      {selectedMode === 'chat' && (
        <div className="flex gap-6">
          {/* 聊天历史侧栏 */}
          {showSidebar && (
            <div className="w-80 bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col" style={{ height: 'calc(100vh - 200px)' }}>
              <div className="p-4 border-b border-gray-200 flex-shrink-0">
                <h3 className="font-medium text-gray-900">聊天历史</h3>
                <p className="text-sm text-gray-600 mt-1">选择一个对话继续聊天</p>
              </div>

              <div className="p-3 space-y-1 overflow-y-auto flex-1">
                {loadingSessions ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader className="h-6 w-6 animate-spin text-gray-400" />
                  </div>
                ) : chatSessions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>还没有聊天记录</p>
                    <p className="text-sm mt-1">开始新对话吧！</p>
                  </div>
                ) : (
                  chatSessions.map((session) => (
                    <div
                      key={session.id}
                      onClick={() => loadChatSession(session.id)}
                      className={`p-2.5 rounded-lg cursor-pointer transition-all ${
                        currentSessionId === session.id
                          ? 'bg-indigo-50 border border-indigo-200'
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {session.title || '未命名对话'}
                          </p>
                          <p className="text-xs text-gray-600 mt-0.5 truncate">
                            {getSessionPreview(session)}
                          </p>
                          <div className="flex items-center justify-between mt-1.5">
                            <p className="text-xs text-gray-500">
                              {session._count?.chat_messages || 0} 条消息
                            </p>
                            <p className="text-xs text-gray-400">
                              {new Date(session.updated_at).toLocaleDateString('zh-CN')}
                            </p>
                          </div>
                        </div>
                        <div className="ml-2 flex flex-col items-center space-y-2">
                          <span className={`inline-block w-2 h-2 rounded-full ${
                            session.session_type === 'qa' ? 'bg-blue-400' :
                            session.session_type === 'grammar' ? 'bg-green-400' :
                            session.session_type === 'scenario' ? 'bg-purple-400' : 'bg-gray-400'
                          }`} />
                          <button
                            onClick={(e) => deleteChatSession(session.id, e)}
                            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors"
                            title="删除会话"
                          >
                            <Trash2 className="h-3 w-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* 主聊天区域 */}
          <div className="flex-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col" style={{height: '70vh'}}>
              <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">AI助教 小樱</h3>
                    <p className="text-sm text-gray-600">
                      随时为你服务
                    </p>
                  </div>
                  <div className="ml-auto flex items-center space-x-2">
                    <button onClick={() => initializeConversation()} className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-all">
                      <RefreshCw className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-4 space-y-4">
                {conversation.map((msg, index) => (
                  <div key={index} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      msg.type === 'user' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div className="whitespace-pre-wrap">{msg.content}</div>
                      <div className={`text-xs mt-1 text-right ${
                        msg.type === 'user' ? 'text-indigo-200' : 'text-gray-500'
                      }`}>
                        {msg.timestamp}
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-4 border-t border-gray-200">
                <div className="flex items-end space-x-2">
                  <button onClick={() => setIsListening(!isListening)} className={`p-2 rounded-lg transition-all ${isListening ? 'bg-red-500 text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`}>
                    <Mic className="h-5 w-5" />
                  </button>
                  
                  <textarea value={message} onChange={(e) => setMessage(e.target.value)} onKeyPress={handleKeyPress} placeholder="输入你的问题..." className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent" rows={1} />
                  
                  <button onClick={sendMessage} disabled={!message.trim() || isTyping} className="p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* 快速提问区域 - 只在没有侧栏时显示 */}
            {!showSidebar && (
              <div className="mt-4 space-y-4">
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <h4 className="font-medium text-gray-900 mb-3">快速提问</h4>
                  <div className="space-y-2">
                    {[ '这个语法怎么用？', '帮我纠正发音', '推荐学习资料', '解释这个词汇', '分析句子结构', '练习对话' ].map((q, i) => (
                      <button key={i} onClick={() => setMessage(q)} className="w-full text-left p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">{q}</button>
                    ))}
                  </div>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
                  <div className="flex items-center space-x-2 mb-2"><Sparkles className="h-5 w-5 text-purple-600" /><span className="font-medium text-purple-900">AI提示</span></div>
                  <p className="text-sm text-purple-700">你可以问我任何关于日语的问题，包括语法、词汇、发音、文化等方面。我会为你提供详细的解释和例句。</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {selectedMode === 'practice' && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">情景对话练习</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {practiceScenarios.map((scenario) => (
              <div key={scenario.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{scenario.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2"><h4 className="font-medium text-gray-900">{scenario.title}</h4><span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">{scenario.difficulty}</span></div>
                    <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3"><span>{scenario.category}</span><span>约 {scenario.estimatedTime} 分钟</span></div>
                    <button onClick={() => startPracticeScenario(scenario)} className="px-3 py-1 bg-indigo-600 text-white rounded-lg text-sm hover:bg-indigo-700 transition-colors">开始练习</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedMode === 'grammar' && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">语法解析助手</h3>
            <button
              onClick={() => setSelectedMode('chat')}
              className="px-3 py-1.5 text-sm bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-colors flex items-center space-x-1"
            >
              <Bot className="h-4 w-4" />
              <span>查看聊天记录</span>
            </button>
          </div>
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <textarea placeholder="输入要分析的日语句子..." className="w-full h-24 border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500" value={grammarText} onChange={(e) => setGrammarText(e.target.value)} />
              <div className="flex justify-end mt-3">
                <button onClick={analyzeGrammar} disabled={!grammarText.trim() || analyzingGrammar} className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 flex items-center space-x-2">
                  {analyzingGrammar ? (<><Loader className="h-4 w-4 animate-spin" /><span>分析中...</span></>) : (<><Lightbulb className="h-4 w-4" /><span>开始分析</span></>)}
                </button>
              </div>
            </div>

            {grammarAnalysis.length > 0 && (
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">分析结果</h4>
                <div className="space-y-4">
                  {grammarAnalysis.map((analysis, index) => (
                    <div key={index} className="p-4 bg-indigo-50 rounded-lg border border-indigo-200">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2"><span className="font-medium text-indigo-900">{analysis.pattern}</span><span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs">{analysis.difficulty}</span></div>
                        <div className="flex items-center space-x-1"><button className="p-1 text-gray-500 hover:text-yellow-500"><Star className="h-4 w-4" /></button><button className="p-1 text-gray-500 hover:text-indigo-500"><Volume2 className="h-4 w-4" /></button></div>
                      </div>
                      <p className="text-sm text-gray-700 mb-3">{analysis.explanation}</p>
                      <div className="space-y-2">
                        <h5 className="text-xs font-medium text-gray-700">例句：</h5>
                        {analysis.examples.map((example, i) => (<div key={i} className="text-xs text-gray-600 bg-white p-2 rounded border border-gray-200">{example}</div>))}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-700">
                    💡 分析结果已保存到聊天记录中。点击上方的"查看聊天记录"按钮或切换到"智能问答"模式可以查看完整的对话历史。
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AITutor;
