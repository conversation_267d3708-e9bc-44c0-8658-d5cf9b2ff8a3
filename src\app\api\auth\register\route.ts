import { NextRequest, NextResponse } from 'next/server';
import { createUser } from '@/lib/server/user-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password } = body;

    // 验证请求数据
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: '请提供姓名、邮箱和密码' },
        { status: 400 }
      );
    }

    // 创建用户
    const user = await createUser(name, email, password);

    return NextResponse.json(
      { message: '注册成功', user },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('注册失败:', error);
    return NextResponse.json(
      { error: error.message || '注册失败' },
      { status: 500 }
    );
  }
}