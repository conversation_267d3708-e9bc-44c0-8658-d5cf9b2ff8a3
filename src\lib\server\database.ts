
import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import { join } from 'path';
import archiver from 'archiver';
import { fixAnalysisResultRubyTags } from '@/utils/ruby-server-utils';

// Singleton Prisma Client
const prisma = new PrismaClient();

class DatabaseManager {
  constructor() {
    this.ensureMediaDirectories();
    // 异步初始化WAL模式，不阻塞构造函数
    this.initializeWALMode();
  }

  /**
   * 异步初始化WAL模式
   */
  private async initializeWALMode() {
    try {
      await this.checkAndSetWALMode();
      await this.optimizeWALConfiguration();
    } catch (error) {
      console.error('初始化WAL模式失败:', error);
    }
  }

  private ensureMediaDirectories() {
    const projectRoot = process.cwd();
    const mediaDir = join(projectRoot, 'public', 'media');
    ['images', 'videos', 'audios'].forEach(subDir => {
      const dirPath = join(mediaDir, subDir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    });
  }

  // --- Settings ---
  async getSystemSetting(key: string) {
    return prisma.system_settings.findUnique({ where: { key } });
  }

  async setSystemSetting(key: string, value: string) {
    return prisma.system_settings.upsert({
      where: { key },
      update: { value },
      create: { key, value },
    });
  }

  // --- User Settings ---
  async getUserSetting(userId: string, key: string) {
    const userIdInt = parseInt(userId);
    return prisma.user_settings.findUnique({
      where: {
        user_id_key: {
          user_id: userIdInt,
          key: key
        }
      }
    });
  }

  async setUserSetting(userId: string, key: string, value: string) {
    const userIdInt = parseInt(userId);
    return prisma.user_settings.upsert({
      where: {
        user_id_key: {
          user_id: userIdInt,
          key: key
        }
      },
      update: { value, updated_at: new Date() },
      create: { user_id: userIdInt, key, value },
    });
  }

  async getUserSettings(userId: string) {
    const userIdInt = parseInt(userId);
    const settings = await prisma.user_settings.findMany({
      where: { user_id: userIdInt }
    });

    // 转换为键值对对象
    const settingsMap: Record<string, string> = {};
    settings.forEach(setting => {
      settingsMap[setting.key] = setting.value;
    });

    return settingsMap;
  }

  async deleteUserSetting(userId: string, key: string) {
    const userIdInt = parseInt(userId);
    return prisma.user_settings.delete({
      where: {
        user_id_key: {
          user_id: userIdInt,
          key: key
        }
      }
    });
  }

  async getAIModels() {
    return prisma.ai_models.findMany({ where: { is_enabled: true } });
  }

  async syncAIModels(models: { model_id: string, display_name: string, description: string, model_type: string }[]) {
    // This could be a more complex transaction, but for now, let's just upsert
    // First disable all existing models
    await prisma.ai_models.updateMany({
        where: {},
        data: { is_enabled: false }
    });

    const operations = models.map(model => 
        prisma.ai_models.upsert({
            where: { model_id: model.model_id },
            update: { display_name: model.display_name, description: model.description, model_type: model.model_type, is_enabled: true },
            create: { ...model, is_enabled: true },
        })
    );
    return prisma.$transaction(operations);
  }

  async getApiKeys() {
    // 检查字段是否存在
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(api_keys)`;
    const columns = (tableInfo as any[]).map(col => col.name);
    const hasNewFields = columns.includes('quota_reset_interval_minutes') && columns.includes('min_usage_interval_seconds');

    if (hasNewFields) {
      // 如果新字段存在，使用原始SQL查询
      const keys = await prisma.$queryRaw`
        SELECT id, name, api_key, provider, priority, is_active, last_used_at, last_error,
               quota_reset_interval_minutes, min_usage_interval_seconds, created_at
        FROM api_keys
        ORDER BY priority ASC
      `;
      return keys;
    } else {
      // 如果新字段不存在，使用基本查询并添加默认值
      console.log('新字段不存在，使用基本查询...');
      const basicKeys = await prisma.api_keys.findMany({
        orderBy: { priority: 'asc' }
      });

      // 添加默认的间隔值
      return basicKeys.map(key => ({
        ...key,
        quota_reset_interval_minutes: 60,
        min_usage_interval_seconds: 1
      }));
    }
  }

  async getActiveApiKeys() {
    // 检查字段是否存在
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(api_keys)`;
    const columns = (tableInfo as any[]).map(col => col.name);
    const hasNewFields = columns.includes('quota_reset_interval_minutes') && columns.includes('min_usage_interval_seconds');

    if (hasNewFields) {
      // 如果新字段存在，使用原始SQL查询
      const keys = await prisma.$queryRaw`
        SELECT id, name, api_key, provider, priority, is_active, last_used_at, last_error,
               quota_reset_interval_minutes, min_usage_interval_seconds, created_at
        FROM api_keys
        WHERE is_active = 1
        ORDER BY priority ASC, last_used_at ASC
      `;
      return keys;
    } else {
      // 如果新字段不存在，使用基本查询并添加默认值
      console.log('新字段不存在，使用基本查询...');
      const basicKeys = await prisma.api_keys.findMany({
        where: { is_active: true },
        orderBy: [
          { priority: 'asc' },
          { last_used_at: 'asc' }
        ]
      });

      // 添加默认的间隔值
      return basicKeys.map(key => ({
        ...key,
        quota_reset_interval_minutes: 60,
        min_usage_interval_seconds: 1
      }));
    }
  }

  async addApiKey(name: string, key: string, quotaResetInterval: number = 60, minUsageInterval: number = 1) {
    const maxPriority = await prisma.api_keys.aggregate({ _max: { priority: true } });
    const priority = (maxPriority._max.priority || 0) + 1;

    // 检查字段是否存在
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(api_keys)`;
    const columns = (tableInfo as any[]).map(col => col.name);
    const hasNewFields = columns.includes('quota_reset_interval_minutes') && columns.includes('min_usage_interval_seconds');

    if (hasNewFields) {
      // 如果新字段存在，使用原始SQL插入
      const result = await prisma.$executeRaw`
        INSERT INTO api_keys (name, api_key, provider, priority, is_active, quota_reset_interval_minutes, min_usage_interval_seconds, created_at)
        VALUES (${name}, ${key}, 'google_gemini', ${priority}, 1, ${quotaResetInterval}, ${minUsageInterval}, datetime('now'))
      `;

      // 返回创建的记录
      const newKey = await prisma.$queryRaw`
        SELECT * FROM api_keys WHERE api_key = ${key}
      `;
      return (newKey as any[])[0];
    } else {
      // 如果新字段不存在，使用基本创建
      console.log('新字段不存在，使用基本创建...');
      return await prisma.api_keys.create({
        data: {
          name,
          api_key: key,
          priority
        }
      });
    }
  }

  async deleteApiKey(id: number) {
    return prisma.api_keys.delete({ where: { id } });
  }

  async updateApiKeyOrder(orderedIds: number[]) {
    const operations = orderedIds.map((id, index) => 
        prisma.api_keys.update({
            where: { id },
            data: { priority: index }
        })
    );
    return prisma.$transaction(operations);
  }

  async updateApiKeyUsage(id: number, error?: string) {
      if (id === 0) return; // Ignore fallback key
      return prisma.api_keys.update({
          where: { id },
          data: {
              last_used_at: new Date(),
              last_error: error || null
          }
      });
  }

  async toggleApiKeyStatus(id: number) {
    const apiKey = await prisma.api_keys.findUnique({ where: { id } });
    if (!apiKey) {
      throw new Error('API Key not found');
    }

    return prisma.api_keys.update({
      where: { id },
      data: {
        is_active: !apiKey.is_active
      }
    });
  }

  async updateApiKeyIntervals(id: number, quotaResetInterval: number, minUsageInterval: number) {
    console.log(`尝试更新API Key ${id} 的间隔配置: 配额重置=${quotaResetInterval}分钟, 使用间隔=${minUsageInterval}秒`);

    // 检查字段是否存在
    const tableInfo = await prisma.$queryRaw`PRAGMA table_info(api_keys)`;
    const columns = (tableInfo as any[]).map(col => col.name);
    const hasNewFields = columns.includes('quota_reset_interval_minutes') && columns.includes('min_usage_interval_seconds');

    if (!hasNewFields) {
      throw new Error('数据库缺少必要的间隔配置字段，请联系管理员');
    }

    // 使用原始SQL更新
    const result = await prisma.$executeRaw`
      UPDATE api_keys
      SET quota_reset_interval_minutes = ${quotaResetInterval},
          min_usage_interval_seconds = ${minUsageInterval}
      WHERE id = ${id}
    `;

    console.log('更新成功，影响行数:', result);

    // 返回更新后的记录
    const updatedKey = await prisma.$queryRaw`
      SELECT * FROM api_keys WHERE id = ${id}
    `;
    return (updatedKey as any[])[0];
  }

  // --- AI Prompts ---
  async getAllAIPrompts() {
    return prisma.ai_prompts.findMany({
      orderBy: [
        { category: 'asc' },
        { type: 'asc' },
        { name: 'asc' }
      ]
    });
  }

  async getAIPromptByName(name: string) {
    return prisma.ai_prompts.findUnique({
      where: { name, is_active: true }
    });
  }

  async getAIPromptById(id: number) {
    return prisma.ai_prompts.findUnique({
      where: { id }
    });
  }

  async getAIPromptsByCategory(category: string) {
    return prisma.ai_prompts.findMany({
      where: { category, is_active: true },
      orderBy: { type: 'asc' }
    });
  }

  async createAIPrompt(data: {
    name: string;
    type: string;
    category: string;
    title: string;
    description?: string;
    content: string;
    variables?: string;
    version?: string;
  }) {
    return prisma.ai_prompts.create({
      data: {
        ...data,
        variables: data.variables || '{}',
        version: data.version || '1.0'
      }
    });
  }

  async updateAIPrompt(id: number, data: {
    title?: string;
    description?: string;
    content?: string;
    variables?: string;
    is_active?: boolean;
    version?: string;
  }) {
    return prisma.ai_prompts.update({
      where: { id },
      data: {
        ...data,
        updated_at: new Date()
      }
    });
  }

  async deleteAIPrompt(id: number) {
    return prisma.ai_prompts.delete({
      where: { id }
    });
  }


  // --- RSS Sources ---
  async getAllRSSSources() {
    return prisma.rss_sources.findMany({
      orderBy: { created_at: 'desc' },
      include: { rss_field_mappings: true }
    });
  }

  async getActiveRSSSources() {
    return prisma.rss_sources.findMany({
      where: { is_active: true },
      orderBy: { created_at: 'desc' }
    });
  }

  async getRSSSourceById(id: number) {
    return prisma.rss_sources.findUnique({
      where: { id },
      include: { rss_field_mappings: true }
    });
  }

  async createRSSSource(data: any) {
    const source = await prisma.rss_sources.create({
      data: {
        name: data.name,
        url: data.url,
        description: data.description,
        category: data.category,
        language: data.language,
        max_articles: data.max_articles,
        enable_ai_processing: data.enable_ai_processing,
        content_selector: data.content_selector,
        is_active: data.is_active,
      }
    });
    return source.id;
  }

  async updateRSSSource(id: number, data: any) {
    return prisma.rss_sources.update({
      where: { id },
      data: {
        name: data.name,
        url: data.url,
        description: data.description,
        category: data.category,
        language: data.language,
        max_articles: data.max_articles,
        enable_ai_processing: data.enable_ai_processing,
        content_selector: data.content_selector,
        is_active: data.is_active
      }
    });
  }

  async deleteRSSSource(id: number) {
    return prisma.rss_sources.delete({ where: { id } });
  }

  async updateRSSSourceStats(id: number, fetchCount: number, successCount: number) {
    const source = await prisma.rss_sources.findUnique({ where: { id } });
    if (!source) return;
    const totalFetched = (source.total_fetched || 0) + successCount;
    const currentSuccesses = ((source.total_fetched || 0) * (source.success_rate || 0));
    const newSuccessRate = totalFetched > 0 ? (currentSuccesses + successCount) / totalFetched : 0;
    return prisma.rss_sources.update({
      where: { id },
      data: {
        last_fetch_time: new Date(),
        last_fetch_count: successCount,
        total_fetched: totalFetched,
        success_rate: newSuccessRate,
      }
    });
  }

  // --- Field Mappings ---
  async updateFieldMappings(rssSourceId: number, mappings: any[]) {
    return prisma.$transaction([
      prisma.rss_field_mappings.deleteMany({ where: { rss_source_id: rssSourceId } }),
      prisma.rss_field_mappings.createMany({
        data: mappings.map(m => ({
          rss_source_id: rssSourceId,
          field_name: m.field_name,
          xpath_selector: m.xpath_selector,
          css_selector: m.css_selector,
          attribute_name: m.attribute_name,
          default_value: m.default_value,
          is_required: m.is_required,
          transform_rule: m.transform_rule,
        }))
      })
    ]);
  }
  
  // --- Articles ---
    async getArticles(filters: { level?: string; category?: string; search?: string; timeRange?: string; sortBy?: string; sortOrder?: string; limit?: number; page?: number; userId?: number; unreadOnly?: boolean } = {}) {
        const page = filters.page || 1;
        const limit = filters.limit || 10;
        const offset = (page - 1) * limit;

        const where: any = {};
        if (filters.category) {
            where.rss_source = {
                category: filters.category
            };
        }
        if (filters.search) {
            where.OR = [
                { title: { contains: filters.search } },
                { content: { contains: filters.search } }
            ];
        }

        // Add time range filtering
        if (filters.timeRange) {
            const now = new Date();
            let startDate: Date | null = null;

            switch (filters.timeRange) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case '2days':
                    startDate = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
                    break;
                case '3days':
                    startDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
                    break;
                case '1week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case '1month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startDate = null;
            }

            if (startDate) {
                where.OR = where.OR ? [
                    ...where.OR,
                    {
                        publish_time: {
                            gte: startDate
                        }
                    }
                ] : [{
                    publish_time: {
                        gte: startDate
                    }
                }];

                // If we have both search and time filter, we need to combine them properly
                if (filters.search) {
                    where.AND = [
                        {
                            OR: [
                                { title: { contains: filters.search } },
                                { content: { contains: filters.search } }
                            ]
                        },
                        {
                            publish_time: {
                                gte: startDate
                            }
                        }
                    ];
                    delete where.OR;
                }
            }
        }

        // 设置排序
        let orderBy: any = [{ publish_time: 'desc' }, { created_at: 'desc' }];
        if (filters.sortBy === 'publish_time') {
            const order = filters.sortOrder === 'asc' ? 'asc' : 'desc';
            orderBy = [{ publish_time: order }, { created_at: order }];
        }

        // 如果需要筛选未读文章，需要特殊处理
        let articles, totalCount;

        if (filters.unreadOnly && filters.userId) {
            // 获取用户已读的文章ID列表
            const readArticleIds = await prisma.user_article_reads.findMany({
                where: { user_id: filters.userId },
                select: { article_id: true }
            });
            const readIds = readArticleIds.map(r => r.article_id);

            // 添加未读条件到where子句
            if (readIds.length > 0) {
                where.id = { notIn: readIds };
            }
            // 如果用户没有阅读任何文章，where条件保持不变（显示所有文章）
        }

        [articles, totalCount] = await prisma.$transaction([
            prisma.articles.findMany({
                where,
                include: {
                    rss_source: { select: { name: true, category: true } },
                    article_vocabulary: { select: { id: true } },
                    article_grammar_points: { select: { id: true } }
                },
                orderBy,
                take: limit,
                skip: offset
            }),
            prisma.articles.count({ where })
        ]);

        // 获取用户的阅读记录
        let readMap = new Map();
        if (filters.userId) {
            const articleIds = articles.map(a => a.id);
            readMap = await this.getUserReadArticles(filters.userId, articleIds);
        }

        return {
            articles: articles.map(a => ({
                ...a,
                rss_source_name: a.rss_source?.name,
                category: a.rss_source?.category || null,
                vocabularyCount: a.article_vocabulary?.length || 0,
                grammarCount: a.article_grammar_points?.length || 0,
                isRead: readMap.has(a.id),
                readAt: readMap.get(a.id) || null
            })),
            totalCount
        };
    }

  // 获取所有可用的分类（从RSS源中）
  async getAvailableCategories() {
    const categories = await prisma.rss_sources.findMany({
      where: {
        category: { not: null },
        is_active: true
      },
      select: { category: true },
      distinct: ['category']
    });

    return categories
      .map(item => item.category)
      .filter(Boolean)
      .sort();
  }

  async getArticleById(id: number) {
    const article = await prisma.articles.findUnique({
      where: { id },
      include: {
        rss_source: { select: { name: true, category: true } },
        article_translations: { where: { language_code: 'zh' } },
        article_vocabulary: { 
            include: { 
                vocabulary: {
                    include: {
                        user_learning_records: { where: { record_type: 'vocabulary' } } // Assuming user 'default_user'
                    }
                }
            }, 
            orderBy: { vocabulary: { word: 'asc' } } 
        },
        article_grammar_points: { 
            include: { 
                grammar_point: {
                    include: {
                        user_learning_records: { where: { record_type: 'grammar' } }
                    }
                }
            }, 
            orderBy: { grammar_point: { pattern: 'asc' } } 
        },
      }
    });

    if (!article) return null;
    
    // Process joined data
    const formattedArticle = {
        ...article,
        translation: article.article_translations[0]?.translated_content,
        vocabulary: article.article_vocabulary.map(av => {
            const vocab = av.vocabulary;

            // Parse JSON fields
            let relatedWords = null;
            let verbInfo = null;
            let commonCollocations = null;
            let examples = null;

            try {
                if (vocab.related_words_json) {
                    relatedWords = JSON.parse(vocab.related_words_json);
                }
            } catch (e) {
                console.warn('Failed to parse related_words_json:', e);
            }

            try {
                if (vocab.verb_info_json) {
                    verbInfo = JSON.parse(vocab.verb_info_json);
                }
            } catch (e) {
                console.warn('Failed to parse verb_info_json:', e);
            }

            try {
                if ((vocab as any).common_collocations_json) {
                    commonCollocations = JSON.parse((vocab as any).common_collocations_json);
                }
            } catch (e) {
                console.warn('Failed to parse common_collocations_json:', e);
            }

            // Parse examples from context (stored as newline-separated string)
            if (av.context) {
                examples = av.context.split('\n').filter(line => line.trim());
            }

            return {
                ...vocab,
                context: av.context,
                is_key_vocabulary: av.is_key_vocabulary,
                status: vocab.user_learning_records[0]?.status || 'new',
                relatedWords,
                verbInfo,
                commonCollocations,
                examples,
                // Map fields for compatibility
                meaning: vocab.meaning_zh || vocab.meaning_en,
                explanationJa: vocab.explanation_ja // 添加日语解释字段
            };
        }),
        grammarPoints: article.article_grammar_points.map(agp => {
            const gp = agp.grammar_point;

            // Parse JSON fields for grammar points
            let similarGrammar = null;
            let examples: string[] = [];
            let commonCollocations: string[] = [];

            try {
                if ((gp as any).similar_grammar_json) {
                    similarGrammar = JSON.parse((gp as any).similar_grammar_json);
                }
            } catch (e) {
                console.warn('Failed to parse similar_grammar_json for', gp.pattern, ':', e);
            }

            try {
                if ((gp as any).examples_json) {
                    examples = JSON.parse((gp as any).examples_json);
                }
            } catch (e) {
                console.warn('Failed to parse examples_json for', gp.pattern, ':', e);
            }

            try {
                if ((gp as any).common_collocations_json) {
                    commonCollocations = JSON.parse((gp as any).common_collocations_json);
                }
            } catch (e) {
                console.warn('Failed to parse common_collocations_json for', gp.pattern, ':', e);
            }

            return {
                ...gp,
                status: gp.user_learning_records[0]?.status || 'new',
                similarGrammar,
                examples,
                commonCollocations,
                explanationJa: gp.explanation_ja // 添加日语解释字段
            };
        })
    };
    return formattedArticle;
  }

  async getArticleByUrl(url: string) {
    return prisma.articles.findUnique({ where: { url } });
  }

  async createArticle(data: any) {
    const article = await prisma.articles.create({ data: {
      rss_source_id: data.rss_source_id,
      guid: data.guid,
      title: data.title,
      subtitle: data.subtitle,
      content: data.content,
      content_html: data.content_html,
      url: data.url,
      publish_time: data.publish_time,
      featured_image_url: data.featured_image_url,
      featured_image_path: data.featured_image_path,
      video_url: data.video_url,
      video_path: data.video_path,
      video_metadata_json: data.video_metadata_json,
      video_m3u8_content: data.video_m3u8_content,
      video_download_status: data.video_download_status,
      use_local_video: data.use_local_video,
      audio_url: data.audio_url,
      audio_path: data.audio_path,
      ai_processing_status: data.ai_processing_status,
      processing_status: data.processing_status
    } });
    return article.id;
  }

  // --- Statistics & Data Management ---
    async getDatabaseStats() {
        const [totalArticles, totalVocabulary, totalGrammarPoints, totalTranslations, aiProcessingQueue, rssSourcesCount, activeSources] = await prisma.$transaction([
            prisma.articles.count(),
            prisma.vocabulary.count(),
            prisma.grammar_points.count(),
            prisma.article_translations.count(),
            prisma.ai_processing_queue.count({ where: { status: 'pending' } }),
            prisma.rss_sources.count(),
            prisma.rss_sources.count({ where: { is_active: true } })
        ]);
        return { totalArticles, totalVocabulary, totalGrammarPoints, totalTranslations, aiProcessingQueue, rssSourcesCount, activeSources };
    }

    async getDatabaseStatsWithMedia() {
        const stats = await this.getDatabaseStats();

        // 统计媒体文件
        const [imagesCount, videosCount, audiosCount] = await prisma.$transaction([
            prisma.articles.count({ where: { featured_image_path: { not: null } } }),
            prisma.articles.count({ where: { video_path: { not: null } } }),
            prisma.articles.count({ where: { audio_path: { not: null } } })
        ]);

        // 计算真实磁盘使用量
        const realDiskUsage = await this.calculateRealDiskUsage();

        return {
            ...stats,
            mediaFiles: {
                images: imagesCount,
                videos: videosCount,
                audios: audiosCount
            },
            diskUsage: realDiskUsage,
            lastUpdate: new Date().toLocaleString('zh-CN')
        };
    }

    // 计算真实的磁盘使用量
    async calculateRealDiskUsage(): Promise<string> {
        try {
            // 1. 从media_files表获取所有媒体文件的总大小
            const mediaFilesStats = await prisma.media_files.aggregate({
                _sum: {
                    file_size: true
                }
            });

            const mediaSize = mediaFilesStats._sum.file_size || 0;

            // 2. 计算数据库文件大小
            let dbSize = 0;
            try {
                const dbPath = join(process.cwd(), 'data', 'nhk_news_new.db');
                if (fs.existsSync(dbPath)) {
                    const dbStats = fs.statSync(dbPath);
                    dbSize = dbStats.size;
                }
            } catch (error) {
                console.warn('无法获取数据库文件大小:', error);
            }

            // 3. 计算总大小并格式化
            const totalSize = mediaSize + dbSize;
            return this.formatBytes(totalSize);
        } catch (error) {
            console.error('计算磁盘使用量失败:', error);
            return '0 MB';
        }
    }

    // 格式化字节数为可读格式
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 MB';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 更新文章的媒体文件路径
    async updateArticleMediaPaths(articleId: number, paths: { featured_image_path?: string | null, audio_path?: string | null }) {
        const updateData: any = {};

        if (paths.featured_image_path !== undefined) {
            updateData.featured_image_path = paths.featured_image_path;
        }

        if (paths.audio_path !== undefined) {
            updateData.audio_path = paths.audio_path;
        }

        if (Object.keys(updateData).length > 0) {
            await prisma.articles.update({
                where: { id: articleId },
                data: updateData
            });
        }
    }

  async getAIStats() {
    const [pending, processing, completed, failed, totalProcessed] = await prisma.$transaction([
        prisma.ai_processing_queue.count({ where: { status: 'pending' } }),
        prisma.ai_processing_queue.count({ where: { status: 'processing' } }),
        prisma.ai_processing_queue.count({ where: { status: 'completed' } }),
        prisma.ai_processing_queue.count({ where: { status: 'failed' } }),
        prisma.ai_processing_queue.count()
    ]);
    return {
        pending, processing, completed, failed, totalProcessed
    };
  }

  async clearArticlesAndRelatedData() {
    return prisma.$transaction([
        prisma.article_grammar_points.deleteMany(),
        prisma.article_vocabulary.deleteMany(),
        prisma.article_translations.deleteMany(),
        prisma.ai_processing_queue.deleteMany(),
        prisma.user_learning_records.deleteMany(),
        prisma.media_files.deleteMany(), // 清理媒体文件记录
        prisma.articles.deleteMany(),
        prisma.vocabulary.deleteMany(),
        prisma.grammar_points.deleteMany()
    ]);
  }

  async exportData() {
    const [articles, vocabulary, grammar_points, rss_sources] = await prisma.$transaction([
        prisma.articles.findMany(),
        prisma.vocabulary.findMany(),
        prisma.grammar_points.findMany(),
        prisma.rss_sources.findMany()
    ]);
    return { exportTime: new Date().toISOString(), version: "2.0.0", data: { articles, vocabulary, grammar_points, rss_sources } };
  }

  // --- Database Backup Management ---

  /**
   * 创建数据库备份
   * 使用SQLite的VACUUM INTO命令确保备份时的数据一致性
   */
  async createDatabaseBackup(): Promise<{ success: boolean; filename?: string; error?: string }> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
      const backupFilename = `nhk_news_new_${timestamp}.zip`;
      const projectRoot = process.cwd();
      const backupDir = join(projectRoot, 'data', 'backups');
      const dbPath = join(projectRoot, 'data', 'nhk_news_new.db');
      const tempDbPath = join(backupDir, `temp_${timestamp}.db`);
      const backupPath = join(backupDir, backupFilename);

      // 确保备份目录存在
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // 使用VACUUM INTO创建一致性备份
      await prisma.$executeRaw`VACUUM INTO ${tempDbPath}`;

      // 压缩备份文件
      const output = fs.createWriteStream(backupPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      return new Promise((resolve, reject) => {
        output.on('close', async () => {
          // 删除临时文件
          if (fs.existsSync(tempDbPath)) {
            fs.unlinkSync(tempDbPath);
          }
          // 记录备份时间
          await this.updateLastBackupTime();
          resolve({ success: true, filename: backupFilename });
        });

        archive.on('error', (err: any) => {
          // 清理临时文件
          if (fs.existsSync(tempDbPath)) {
            fs.unlinkSync(tempDbPath);
          }
          reject({ success: false, error: err.message });
        });

        archive.pipe(output);
        archive.file(tempDbPath, { name: 'nhk_news_new.db' });
        archive.finalize();
      });

    } catch (error: any) {
      console.error('创建数据库备份失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取所有备份文件列表
   */
  async getBackupFiles(): Promise<Array<{
    filename: string;
    size: number;
    createdAt: Date;
    formattedSize: string;
  }>> {
    try {
      const projectRoot = process.cwd();
      const backupDir = join(projectRoot, 'data', 'backups');

      if (!fs.existsSync(backupDir)) {
        return [];
      }

      const files = fs.readdirSync(backupDir);
      const backupFiles = files
        .filter(file => file.startsWith('nhk_news_new_') && file.endsWith('.zip'))
        .map(file => {
          const filePath = join(backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            filename: file,
            size: stats.size,
            createdAt: stats.birthtime,
            formattedSize: this.formatBytes(stats.size)
          };
        })
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return backupFiles;
    } catch (error: any) {
      console.error('获取备份文件列表失败:', error);
      return [];
    }
  }

  /**
   * 删除备份文件
   */
  async deleteBackupFile(filename: string): Promise<{ success: boolean; error?: string }> {
    try {
      const projectRoot = process.cwd();
      const backupDir = join(projectRoot, 'data', 'backups');
      const filePath = join(backupDir, filename);

      // 安全检查：确保文件名符合备份文件格式
      if (!filename.startsWith('nhk_news_new_') || !filename.endsWith('.zip')) {
        return { success: false, error: '无效的备份文件名' };
      }

      if (!fs.existsSync(filePath)) {
        return { success: false, error: '备份文件不存在' };
      }

      fs.unlinkSync(filePath);
      return { success: true };
    } catch (error: any) {
      console.error('删除备份文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取备份文件路径（用于下载）
   */
  getBackupFilePath(filename: string): string | null {
    try {
      const projectRoot = process.cwd();
      const backupDir = join(projectRoot, 'data', 'backups');
      const filePath = join(backupDir, filename);

      // 安全检查
      if (!filename.startsWith('nhk_news_new_') || !filename.endsWith('.zip')) {
        return null;
      }

      if (!fs.existsSync(filePath)) {
        return null;
      }

      return filePath;
    } catch (error) {
      console.error('获取备份文件路径失败:', error);
      return null;
    }
  }

  /**
   * 获取数据库文件路径（用于下载原始数据库文件）
   */
  getDatabaseFilePath(): string | null {
    try {
      const projectRoot = process.cwd();
      const dbPath = join(projectRoot, 'data', 'nhk_news_new.db');

      if (!fs.existsSync(dbPath)) {
        return null;
      }

      return dbPath;
    } catch (error) {
      console.error('获取数据库文件路径失败:', error);
      return null;
    }
  }

  /**
   * 获取或设置自动备份配置
   */
  async getBackupSettings() {
    const settings = await Promise.all([
      this.getSystemSetting('backup_enabled'),
      this.getSystemSetting('backup_interval_hours'),
      this.getSystemSetting('backup_max_files')
    ]);

    return {
      enabled: settings[0]?.value === 'true' || false,
      intervalHours: parseInt(settings[1]?.value || '24'),
      maxFiles: parseInt(settings[2]?.value || '10')
    };
  }

  async updateBackupSettings(enabled: boolean, intervalHours: number, maxFiles: number) {
    await Promise.all([
      this.setSystemSetting('backup_enabled', enabled.toString()),
      this.setSystemSetting('backup_interval_hours', intervalHours.toString()),
      this.setSystemSetting('backup_max_files', maxFiles.toString())
    ]);
  }

  /**
   * 记录最后备份时间
   */
  async updateLastBackupTime() {
    await this.setSystemSetting('last_backup_time', new Date().toISOString());
  }

  /**
   * 获取最后备份时间
   */
  async getLastBackupTime(): Promise<Date | null> {
    const setting = await this.getSystemSetting('last_backup_time');
    return setting?.value ? new Date(setting.value) : null;
  }

  /**
   * 检查和设置SQLite WAL模式
   */
  async checkAndSetWALMode() {
    try {
      // 检查当前journal模式
      const currentMode = await prisma.$queryRaw`PRAGMA journal_mode;`;
      console.log('当前SQLite journal模式:', currentMode);

      // 如果不是WAL模式，则设置为WAL模式
      if (Array.isArray(currentMode) && currentMode[0] && (currentMode[0] as any).journal_mode !== 'wal') {
        console.log('设置SQLite为WAL模式...');
        const result = await prisma.$executeRaw`PRAGMA journal_mode=WAL;`;
        console.log('WAL模式设置结果:', result);

        // 验证设置是否成功
        const newMode = await prisma.$queryRaw`PRAGMA journal_mode;`;
        console.log('新的journal模式:', newMode);
      } else {
        console.log('SQLite已经在WAL模式下运行');
      }

      // 获取其他WAL相关配置
      const walConfig = await this.getWALConfiguration();
      console.log('WAL配置信息:', walConfig);

      return true;
    } catch (error) {
      console.error('检查/设置WAL模式失败:', error);
      return false;
    }
  }

  /**
   * 获取WAL模式相关配置信息
   */
  async getWALConfiguration() {
    try {
      const [journalMode, walAutocheckpoint, synchronous, cacheSize] = await Promise.all([
        prisma.$queryRaw`PRAGMA journal_mode;`,
        prisma.$queryRaw`PRAGMA wal_autocheckpoint;`,
        prisma.$queryRaw`PRAGMA synchronous;`,
        prisma.$queryRaw`PRAGMA cache_size;`
      ]);

      return {
        journalMode,
        walAutocheckpoint,
        synchronous,
        cacheSize
      };
    } catch (error) {
      console.error('获取WAL配置失败:', error);
      return null;
    }
  }

  /**
   * 优化WAL模式配置
   */
  async optimizeWALConfiguration() {
    try {
      console.log('优化SQLite WAL配置...');

      // 使用$queryRaw来执行PRAGMA命令，因为它们会返回结果
      await prisma.$queryRaw`PRAGMA wal_autocheckpoint=1000;`;
      console.log('✅ WAL自动检查点设置为1000页');

      await prisma.$queryRaw`PRAGMA synchronous=NORMAL;`;
      console.log('✅ 同步模式设置为NORMAL');

      await prisma.$queryRaw`PRAGMA cache_size=-65536;`;
      console.log('✅ 缓存大小设置为64MB');

      await prisma.$queryRaw`PRAGMA temp_store=MEMORY;`;
      console.log('✅ 临时存储设置为内存');

      await prisma.$queryRaw`PRAGMA mmap_size=268435456;`;
      console.log('✅ 内存映射大小设置为256MB');

      console.log('WAL配置优化完成');

      // 验证配置
      const config = await this.getWALConfiguration();
      console.log('优化后的配置:', config);

      return true;
    } catch (error) {
      console.error('优化WAL配置失败:', error);
      return false;
    }
  }

  /**
   * 清理旧的备份文件（保留最新的N个文件）
   */
  async cleanupOldBackups(): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
    try {
      const settings = await this.getBackupSettings();
      const backupFiles = await this.getBackupFiles();

      if (backupFiles.length <= settings.maxFiles) {
        return { success: true, deletedCount: 0 };
      }

      const filesToDelete = backupFiles.slice(settings.maxFiles);
      let deletedCount = 0;

      for (const file of filesToDelete) {
        const result = await this.deleteBackupFile(file.filename);
        if (result.success) {
          deletedCount++;
        }
      }

      return { success: true, deletedCount };
    } catch (error: any) {
      console.error('清理旧备份文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  // --- Logs ---
  async addLog(sessionId: string, level: string, message: string, rssSourceId?: number, url?: string) {
    return prisma.scraping_logs.create({ data: { session_id: sessionId, log_level: level, message, rss_source_id: rssSourceId, url } });
  }
  
    async getScrapingProgress(sessionId: string) {
        const logs = await prisma.scraping_logs.findMany({ where: { session_id: sessionId }, orderBy: { created_at: 'asc' }, take: 100 });
        const statsResult = await prisma.scraping_logs.groupBy({ by: ['message'], where: { session_id: sessionId }, _count: { message: true } });
        const stats = {
            total: statsResult.filter(r => r.message.includes('开始处理') || r.message.includes('处理条目失败')).reduce((acc, r) => acc + r._count.message, 0),
            success: statsResult.find(r => r.message.startsWith('文章处理成功'))?._count.message || 0,
            failed: statsResult.find(r => r.message.startsWith('处理条目失败'))?._count.message || 0,
            aiScheduled: statsResult.find(r => r.message.includes('AI处理队列'))?._count.message || 0
        };
        return { logs, stats };
    }

    async getScrapingSessions() {
        const sessions = await prisma.scraping_logs.groupBy({
            by: ['session_id'],
            where: { session_id: { not: null } },
            _min: { created_at: true },
            _max: { created_at: true },
            orderBy: { _min: { created_at: 'desc' } },
            take: 10
        });
        
        const fullSessions = await Promise.all(sessions.map(async s => {
            if (!s.session_id) return null;
            const stats = await this.getScrapingProgress(s.session_id);
            const sourceCountResult = await prisma.scraping_logs.findMany({
                where: { session_id: s.session_id, rss_source_id: { not: null } },
                distinct: ['rss_source_id']
            });
            return {
                id: s.session_id,
                startTime: s._min.created_at,
                endTime: s._max.created_at,
                totalSources: sourceCountResult.length,
                totalArticles: stats.stats.total,
                successfulArticles: stats.stats.success,
                failedArticles: stats.stats.failed,
                aiProcessingScheduled: stats.stats.aiScheduled
            }
        }));

        return fullSessions.filter((s): s is NonNullable<typeof s> => s !== null);
    }
  
    async getLogsBySessionId(sessionId: string) {
        return prisma.scraping_logs.findMany({
            where: { session_id: sessionId },
            orderBy: { created_at: 'asc' },
            take: 100
        });
    }

    async getRecentAIProcessingLogs(seconds: number = 60) {
        const cutoffTime = new Date(Date.now() - seconds * 1000);
        return prisma.scraping_logs.findMany({
            where: {
                session_id: { startsWith: 'ai_session_' },
                created_at: { gte: cutoffTime }
            },
            orderBy: { created_at: 'desc' },
            take: 10
        });
    }

    // 日志清理功能
    async cleanupOldLogs(daysToKeep: number = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        const result = await prisma.scraping_logs.deleteMany({
            where: {
                created_at: {
                    lt: cutoffDate
                }
            }
        });

        return result.count;
    }

    async shouldCleanupLogs(): Promise<boolean> {
        const lastCleanup = await prisma.system_settings.findUnique({
            where: { key: 'last_log_cleanup' }
        });

        if (!lastCleanup) return true;

        const lastCleanupTime = new Date(lastCleanup.value);
        const now = new Date();
        const hoursSinceLastCleanup = (now.getTime() - lastCleanupTime.getTime()) / (1000 * 60 * 60);

        return hoursSinceLastCleanup >= 24; // 24小时检查一次
    }

    async getLogRetentionDays(): Promise<number> {
        const setting = await prisma.system_settings.findUnique({
            where: { key: 'log_retention_days' }
        });

        return setting ? parseInt(setting.value) : 30; // 默认30天
    }

    async getLogStats() {
        const retentionDays = await this.getLogRetentionDays();
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        const [total, old] = await Promise.all([
            prisma.scraping_logs.count(),
            prisma.scraping_logs.count({
                where: { created_at: { lt: cutoffDate } }
            })
        ]);

        return {
            totalLogs: total,
            oldLogs: old,
            retentionDays,
            needsCleanup: old > 0
        };
    }

    async performLogCleanupIfNeeded() {
        if (await this.shouldCleanupLogs()) {
            try {
                const retentionDays = await this.getLogRetentionDays();
                const deletedCount = await this.cleanupOldLogs(retentionDays);
                console.log(`清理了 ${deletedCount} 条过期日志（保留 ${retentionDays} 天）`);

                // 更新清理时间
                await prisma.system_settings.upsert({
                    where: { key: 'last_log_cleanup' },
                    update: { value: new Date().toISOString() },
                    create: { key: 'last_log_cleanup', value: new Date().toISOString() }
                });

                return deletedCount;
            } catch (error) {
                console.error('日志清理失败:', error);
                return 0;
            }
        }
        return 0;
    }

    // 抓取延迟配置管理
    async getScrapingDelayConfig() {
        const defaultConfig = {
            rssSourceDelay: 5000,    // RSS源间延迟（毫秒）
            articleDelay: 2000,      // 文章间延迟（毫秒）
            requestMinDelay: 2000,   // 最小请求延迟（毫秒）
            requestMaxDelay: 5000    // 最大请求延迟（毫秒）
        };

        const settings = await prisma.system_settings.findMany({
            where: {
                key: {
                    in: ['rss_source_delay', 'article_delay', 'request_min_delay', 'request_max_delay']
                }
            }
        });

        const config = { ...defaultConfig };
        settings.forEach(setting => {
            switch (setting.key) {
                case 'rss_source_delay':
                    config.rssSourceDelay = parseInt(setting.value);
                    break;
                case 'article_delay':
                    config.articleDelay = parseInt(setting.value);
                    break;
                case 'request_min_delay':
                    config.requestMinDelay = parseInt(setting.value);
                    break;
                case 'request_max_delay':
                    config.requestMaxDelay = parseInt(setting.value);
                    break;
            }
        });

        return config;
    }

    async updateScrapingDelayConfig(config: {
        rssSourceDelay: number;
        articleDelay: number;
        requestMinDelay: number;
        requestMaxDelay: number;
    }) {
        const updates = [
            { key: 'rss_source_delay', value: config.rssSourceDelay.toString() },
            { key: 'article_delay', value: config.articleDelay.toString() },
            { key: 'request_min_delay', value: config.requestMinDelay.toString() },
            { key: 'request_max_delay', value: config.requestMaxDelay.toString() }
        ];

        await Promise.all(updates.map(update =>
            prisma.system_settings.upsert({
                where: { key: update.key },
                update: { value: update.value },
                create: { key: update.key, value: update.value }
            })
        ));
    }

  // --- AI Processing ---
  async scheduleAIProcessing(articleId: number) {
    return prisma.ai_processing_queue.create({ data: { article_id: articleId, processing_type: 'analysis', priority: 0 } });
  }

  async getPendingAITasks(type: 'analysis' | 'all' = 'all', limit: number = 10) {
      return prisma.ai_processing_queue.findMany({
          where: { status: 'pending', ...(type !== 'all' && { processing_type: type }) },
          include: { article: true },
          take: limit,
          orderBy: { priority: 'desc' },
      });
  }

  async updateAIQueueTaskStatus(taskId: number, status: 'processing' | 'completed' | 'failed' | 'skipped', errorMessage?: string) {
      return prisma.ai_processing_queue.update({
          where: { id: taskId },
          data: { status, error_message: errorMessage }
      });
  }

  async updateArticleAIStatus(articleId: number, status: 'processing' | 'completed' | 'failed' | 'skipped', errorMessage?: string) {
      return prisma.articles.update({
          where: { id: articleId },
          data: { ai_processing_status: status, ai_processing_error: errorMessage, ai_processed_at: new Date() }
      });
  }

  async saveArticleAnalysis(articleId: number, analysisResult: any) {
    // 修复分析结果中的ruby标签格式
    const fixedResult = fixAnalysisResultRubyTags(analysisResult);
    const { translation, vocabulary, grammar, titleWithFurigana, contentWithFurigana, subtitleWithFurigana } = fixedResult;

    return prisma.$transaction(async (tx) => {
      // 先清理该文章的旧分析数据，避免重复插入
      await tx.article_vocabulary.deleteMany({
        where: { article_id: articleId }
      });
      await tx.article_grammar_points.deleteMany({
        where: { article_id: articleId }
      });

      await tx.articles.update({
        where: { id: articleId },
        data: {
          title_furigana_html: titleWithFurigana,
          content_furigana_html: contentWithFurigana,
          subtitle_furigana_html: subtitleWithFurigana,
        }
      });
      
      if (translation) {
        await tx.article_translations.upsert({
          where: {
            article_id_language_code: {
              article_id: articleId,
              language_code: 'zh'
            }
          },
          update: {
            translated_title: translation.translatedTitle,
            translated_subtitle: translation.translatedSubtitle,
            translated_content: translation.translatedContent
          },
          create: {
            article_id: articleId,
            language_code: 'zh',
            translated_title: translation.translatedTitle,
            translated_subtitle: translation.translatedSubtitle,
            translated_content: translation.translatedContent
          }
        });
      }

      for (const vocab of vocabulary) {
        const newVocab = await tx.vocabulary.upsert({
          where: { word_reading: { word: vocab.word, reading: vocab.reading } },
          update: {
            meaning_en: vocab.meaningEn || vocab.meaning_en, // 支持两种字段名
            explanation_ja: vocab.explanationJa, // 新增日语解释
          },
          create: {
            word: vocab.word,
            reading: vocab.reading,
            part_of_speech: vocab.partOfSpeech,
            meaning_zh: vocab.meaning,
            meaning_en: vocab.meaningEn || vocab.meaning_en, // 支持两种字段名
            jlpt_level: vocab.difficulty || 'N/A',
            extraction_method: 'ai',
            related_words_json: JSON.stringify(vocab.relatedWords || {}),
            verb_info_json: JSON.stringify(vocab.verbInfo || {}),
            common_collocations_json: JSON.stringify(vocab.commonCollocations || []),
            explanation_ja: vocab.explanationJa, // 新增日语解释
          }
        });
        // 由于我们已经清理了旧数据，直接创建新记录是安全的
        await tx.article_vocabulary.create({
          data: {
            article_id: articleId,
            vocabulary_id: newVocab.id,
            context: Array.isArray(vocab.examples) ? vocab.examples.join('\n') : '',
            is_key_vocabulary: true,
          }
        });
      }

      for (const gram of grammar) {
        const newGrammar = await tx.grammar_points.upsert({
          where: { pattern: gram.pattern },
          update: {
            explanation_ja: gram.explanationJa, // 新增日语解释
            common_collocations_json: JSON.stringify(gram.commonCollocations || []), // 新增常见搭配
          },
          create: {
            pattern: gram.pattern,
            name: gram.pattern,
            reading: gram.reading || null,
            meaning_zh: gram.explanation,
            jlpt_level: gram.difficulty || 'N/A',
            explanation: gram.explanation,
            explanation_ja: gram.explanationJa, // 新增日语解释
            common_collocations_json: JSON.stringify(gram.commonCollocations || []), // 新增常见搭配
            extraction_method: 'ai',
            similar_grammar_json: JSON.stringify(gram.similarGrammar || []),
            examples_json: JSON.stringify(gram.examples || []),
          }
        });
        await tx.article_grammar_points.create({
          data: {
            article_id: articleId,
            grammar_point_id: newGrammar.id,
          }
        });
      }
    });
  }

    async clearAIQueue() {
        return prisma.ai_processing_queue.deleteMany({ where: { status: { in: ['pending', 'failed'] } } });
    }

  // --- Video Management ---
  async getVideosForManagement(page: number, limit: number) {
    const where = { video_url: { not: null } };
    const [videos, totalCount] = await prisma.$transaction([
        prisma.articles.findMany({
            where,
            select: { id: true, title: true, video_download_status: true, use_local_video: true, video_url: true, video_path: true },
            orderBy: { publish_time: 'desc' },
            skip: (page - 1) * limit,
            take: limit
        }),
        prisma.articles.count({ where })
    ]);
    return { videos, totalCount };
  }

  // --- AI Analysis Management ---
  async getArticlesForAIManagement(page: number, limit: number) {
    const offset = (page - 1) * limit;
    const [articles, totalCount] = await prisma.$transaction([
        prisma.articles.findMany({
            select: {
                id: true,
                title: true,
                ai_processing_status: true,
                publish_time: true,
                created_at: true,
                rss_source: { select: { name: true } }
            },
            orderBy: { publish_time: 'desc' },
            skip: offset,
            take: limit
        }),
        prisma.articles.count()
    ]);
    return {
        articles: articles.map(a => ({
            ...a,
            rss_source_name: a.rss_source?.name
        })),
        totalCount
    };
  }

  async getAllUnprocessedArticleIds() {
    // 获取所有未处理和错误状态的文章ID
    // 包括：待处理(pending)、失败(failed)
    // 排除：处理中(processing)、已完成(completed)、已跳过(skipped)
    const articles = await prisma.articles.findMany({
      where: {
        ai_processing_status: {
          in: ['pending', 'failed']
        }
      },
      select: { id: true }
    });
    return articles.map(article => article.id);
  }

  async scheduleAIAnalysisForArticles(articleIds: number[]) {
    return prisma.$transaction(async (tx) => {
      // 1. 检查哪些文章还没有在队列中或只有失败的记录
      const existingQueueItems = await tx.ai_processing_queue.findMany({
        where: {
          article_id: { in: articleIds }
        },
        select: { article_id: true, status: true }
      });

      // 按文章ID分组，找出每篇文章的最新状态
      const articleStatusMap = new Map<number, string>();
      existingQueueItems.forEach(item => {
        const currentStatus = articleStatusMap.get(item.article_id);
        // 优先级：processing > pending > completed > failed
        if (!currentStatus ||
            (item.status === 'processing') ||
            (item.status === 'pending' && currentStatus !== 'processing') ||
            (item.status === 'completed' && !['processing', 'pending'].includes(currentStatus))) {
          articleStatusMap.set(item.article_id, item.status);
        }
      });

      // 只处理没有记录或状态为failed的文章
      const newArticleIds = articleIds.filter(id => {
        const status = articleStatusMap.get(id);
        return !status || status === 'failed';
      });

      if (newArticleIds.length === 0) {
        return 0; // 所有文章都已经在队列中或已完成
      }

      // 2. 删除这些文章的旧记录（主要是失败的记录）
      await tx.ai_processing_queue.deleteMany({
        where: {
          article_id: { in: newArticleIds }
        }
      });

      // 3. 添加新文章到AI处理队列
      const queueOperations = newArticleIds.map(articleId =>
        tx.ai_processing_queue.create({
          data: {
            article_id: articleId,
            processing_type: 'analysis',
            priority: 0,
            status: 'pending'
          }
        })
      );

      // 4. 更新文章的AI处理状态
      const articleOperations = newArticleIds.map(articleId =>
        tx.articles.update({
          where: { id: articleId },
          data: { ai_processing_status: 'pending' }
        })
      );

      // 执行所有操作
      await Promise.all([...queueOperations, ...articleOperations]);

      return newArticleIds.length;
    });
  }

  async getPendingVideoDownloads(articleIds?: number[]) {
      const where: any = {
          video_download_status: { in: ['pending', 'failed'] },
          video_m3u8_content: { not: null }
      };
      if (articleIds && articleIds.length > 0) {
          where.id = { in: articleIds };
      }
      return prisma.articles.findMany({
          where,
          select: { id: true, video_metadata_json: true, video_m3u8_content: true, publish_time: true }
      });
  }
  
  async updateVideoDownloadStatus(articleId: number, status: 'downloading' | 'completed' | 'failed', errorMessage?: string) {
      return prisma.articles.update({
          where: { id: articleId },
          data: { video_download_status: status, ...(errorMessage && { ai_processing_error: errorMessage }) }
      });
  }

  async updateVideoPath(articleId: number, videoPath: string) {
      return prisma.articles.update({ where: { id: articleId }, data: { video_path: videoPath } });
  }

  async setUseLocalVideo(articleId: number, useLocal: boolean) {
      return prisma.articles.update({ where: { id: articleId }, data: { use_local_video: useLocal } });
  }

  async batchSetUseLocalVideo(articleIds: number[], useLocal: boolean) {
      return prisma.articles.updateMany({ where: { id: { in: articleIds } }, data: { use_local_video: useLocal } });
  }
  
    // --- User Learning ---


    async getLearningStats(userId: string) {
        const userIdInt = parseInt(userId);
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        // Get unique active days in the last 30 days (based on actual learning activity)
        const activeDaysData = await prisma.user_learning_records.findMany({
            where: {
                user_id: userIdInt,
                updated_at: { gte: thirtyDaysAgo },
                status: { not: 'new' }  // Only count records that have been studied
            },
            select: { updated_at: true }
        });
        const uniqueDays = new Set(activeDaysData.map(record => record.updated_at.toDateString()));
        const activeDays = uniqueDays.size;

        // Calculate consecutive learning days
        const consecutiveDays = await this.calculateConsecutiveDays(userIdInt);

        // Get basic learning statistics
        const [learnedVocab, masteredVocab, completedArticles] = await prisma.$transaction([
            prisma.user_learning_records.count({ where: { user_id: userIdInt, record_type: 'vocabulary', status: { not: 'new' } } }),
            prisma.user_learning_records.count({ where: { user_id: userIdInt, record_type: 'vocabulary', status: 'mastered' } }),
            prisma.user_learning_records.count({ where: { user_id: userIdInt, record_type: 'article', status: 'completed' } })
        ]);

        // Estimate average study time (based on actual learning activities)
        const totalLearningActivities = activeDaysData.length;
        const averageStudyTime = activeDays > 0 ? Math.round((totalLearningActivities / activeDays) * 5) : 0; // 5 minutes per learning activity

        return {
            activeDaysLast30: activeDays,
            consecutiveDays,
            learnedVocabulary: learnedVocab,
            masteredVocabulary: masteredVocab,
            completedArticles,
            averageStudyTime
        };
    }

    private async calculateConsecutiveDays(userId: number): Promise<number> {
        // Get all learning days in descending order
        const learningDays = await prisma.user_learning_records.findMany({
            where: { user_id: userId },
            select: { created_at: true },
            orderBy: { created_at: 'desc' }
        });

        if (learningDays.length === 0) return 0;

        // Get unique days
        const uniqueDays = Array.from(new Set(
            learningDays.map(record => record.created_at.toDateString())
        )).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

        let consecutiveDays = 0;
        const today = new Date().toDateString();

        // Check if user studied today or yesterday
        const latestDay = uniqueDays[0];
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);

        if (latestDay !== today && latestDay !== yesterday.toDateString()) {
            return 0; // No recent activity
        }

        // Count consecutive days
        let currentDate = new Date(latestDay);
        for (const dayStr of uniqueDays) {
            const dayDate = new Date(dayStr);
            if (dayDate.toDateString() === currentDate.toDateString()) {
                consecutiveDays++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
        }

        return consecutiveDays;
    }

    async getReviewTaskStats(userId: string) {
        const userIdInt = parseInt(userId);
        const now = new Date();

        // Calculate time boundaries
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const dayAfterTomorrow = new Date(tomorrow);
        dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);

        const weekEnd = new Date(now);
        weekEnd.setDate(weekEnd.getDate() + (7 - weekEnd.getDay()));
        weekEnd.setHours(23, 59, 59, 999);

        const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);

        const [todayTasks, tomorrowTasks, weekTasks, monthTasks, overdueTasks] = await prisma.$transaction([
            // Today's review tasks (支持新旧字段)
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: {
                        gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
                        lt: tomorrow
                    }
                }
            }),
            // Tomorrow's review tasks
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: {
                        gte: tomorrow,
                        lt: dayAfterTomorrow
                    }
                }
            }),
            // This week's review tasks
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: {
                        gte: now,
                        lte: weekEnd
                    }
                }
            }),
            // This month's review tasks
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: {
                        gte: now,
                        lte: monthEnd
                    }
                }
            }),
            // Overdue tasks
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: { lt: now }
                }
            })
        ]);

        return {
            today: todayTasks,
            tomorrow: tomorrowTasks,
            thisWeek: weekTasks,
            thisMonth: monthTasks,
            overdue: overdueTasks
        };
    }

    async getLearningEfficiencyStats(userId: string) {
        const userIdInt = parseInt(userId);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        // Get review statistics
        const reviewRecords = await prisma.user_learning_records.findMany({
            where: {
                user_id: userIdInt,
                fsrs_last_review: { gte: thirtyDaysAgo },
                status: { not: 'new' },
                fsrs_reps: { gt: 0 }
            },
            select: {
                fsrs_reps: true,
                fsrs_stability: true,
                fsrs_difficulty: true
            }
        });

        // Calculate review success rate (based on ease factor and repetitions)
        let reviewSuccessRate = 0;
        if (reviewRecords.length > 0) {
            // 使用稳定性来计算成功率：稳定性越高，成功率越高
            const avgStability = reviewRecords.reduce((sum, r) => sum + (r.fsrs_stability || 1), 0) / reviewRecords.length;
            reviewSuccessRate = Math.min(avgStability / 30, 1); // 30天稳定性对应100%成功率
        }

        // Calculate average repetitions
        const averageRepetitions = reviewRecords.length > 0
            ? reviewRecords.reduce((sum, r) => sum + (r.fsrs_reps || 0), 0) / reviewRecords.length
            : 0;

        // Calculate progress efficiency (new items learned per day)
        const newItemsCount = await prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: { in: ['learning', 'mastered'] },
                updated_at: { gte: thirtyDaysAgo }
            }
        });
        const progressEfficiency = newItemsCount / 30;

        // Calculate retention rate (items on schedule vs total learning items)
        const [onScheduleCount, totalLearningCount] = await prisma.$transaction([
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: { gte: new Date() }
                }
            }),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { not: 'new' }
                }
            })
        ]);

        const retentionRate = totalLearningCount > 0 ? onScheduleCount / totalLearningCount : 0;

        return {
            reviewSuccessRate: Math.max(0, Math.min(1, reviewSuccessRate)),
            averageRepetitions: Math.round(averageRepetitions * 10) / 10,
            progressEfficiency: Math.round(progressEfficiency * 10) / 10,
            retentionRate: Math.max(0, Math.min(1, retentionRate))
        };
    }

    async getStudyTrendData(userId: string, period: 'week' | 'month' | 'year') {
        const userIdInt = parseInt(userId);
        const today = new Date();
        const data = [];

        if (period === 'week') {
            // Past 7 days + future 7 days
            for (let i = 6; i >= -7; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const isPast = i > 0;  // 只有过去的日期才是 isPast，今天算作未来

                if (isPast) {
                    // 过去的日期：只显示已完成的学习
                    const studied = await this.getStudyCountForDate(userIdInt, date);
                    data.push({
                        label: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
                        date: date.toISOString().split('T')[0],
                        studied,
                        scheduled: 0,
                        newStudy: 0,
                        review: 0,
                        overdueReview: 0,
                        isPast
                    });
                } else {
                    // 今天和未来的日期：只显示计划学习/复习
                    const scheduledData = await this.getScheduledCountForDate(userIdInt, date);
                    data.push({
                        label: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
                        date: date.toISOString().split('T')[0],
                        studied: 0, // 今天和未来都不显示已完成数量
                        scheduled: scheduledData.newStudy + scheduledData.review + scheduledData.overdueReview, // 总计划数量（向后兼容）
                        newStudy: scheduledData.newStudy,
                        review: scheduledData.review,
                        overdueReview: scheduledData.overdueReview,
                        isPast
                    });
                }
            }
        } else if (period === 'month') {
            // Past 30 days + future 15 days
            for (let i = 29; i >= -15; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const isPast = i > 0;  // 只有过去的日期才是 isPast，今天算作未来

                if (isPast) {
                    // 过去的日期：只显示已完成的学习
                    const studied = await this.getStudyCountForDate(userIdInt, date);
                    data.push({
                        label: date.getDate().toString(),
                        date: date.toISOString().split('T')[0],
                        studied,
                        scheduled: 0,
                        newStudy: 0,
                        review: 0,
                        overdueReview: 0,
                        isPast
                    });
                } else {
                    // 今天和未来的日期：只显示计划学习/复习
                    const scheduledData = await this.getScheduledCountForDate(userIdInt, date);
                    data.push({
                        label: date.getDate().toString(),
                        date: date.toISOString().split('T')[0],
                        studied: 0, // 今天和未来都不显示已完成数量
                        scheduled: scheduledData.newStudy + scheduledData.review + scheduledData.overdueReview,
                        newStudy: scheduledData.newStudy,
                        review: scheduledData.review,
                        overdueReview: scheduledData.overdueReview,
                        isPast
                    });
                }
            }
        } else {
            // Past 52 weeks + future 12 weeks
            for (let i = 51; i >= -12; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - (i * 7));
                const isPast = i > 0;  // 只有过去的周才是 isPast，本周算作未来

                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());

                if (isPast) {
                    // 过去的周：只显示已完成的学习
                    const studied = await this.getStudyCountForWeek(userIdInt, weekStart);
                    data.push({
                        label: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
                        date: weekStart.toISOString().split('T')[0],
                        studied,
                        scheduled: 0,
                        newStudy: 0,
                        review: 0,
                        overdueReview: 0,
                        isPast
                    });
                } else {
                    // 本周和未来的周：只显示计划学习/复习
                    const scheduledData = await this.getScheduledCountForWeek(userIdInt, weekStart);
                    data.push({
                        label: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
                        date: weekStart.toISOString().split('T')[0],
                        studied: 0, // 本周和未来都不显示已完成数量
                        scheduled: scheduledData.newStudy + scheduledData.review + scheduledData.overdueReview,
                        newStudy: scheduledData.newStudy,
                        review: scheduledData.review,
                        overdueReview: scheduledData.overdueReview,
                        isPast
                    });
                }
            }
        }

        return data;
    }

    private async getStudyCountForDate(userId: number, date: Date): Promise<number> {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        return await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                updated_at: {
                    gte: startOfDay,
                    lte: endOfDay
                },
                status: { not: 'new' }  // Only count records that have been actually studied
            }
        });
    }

    private async getScheduledCountForDate(userId: number, date: Date): Promise<{ newStudy: number, review: number, overdueReview: number }> {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        // 获取今天的开始时间（用于逾期判断）
        const today = new Date();
        const todayStart = new Date(today);
        todayStart.setHours(0, 0, 0, 0);

        // 获取用户每日学习目标
        let dailyNewItemsTarget = 10;
        try {
            const userSetting = await this.getUserSetting(userId.toString(), 'study_cards_per_session');
            if (userSetting?.value) {
                dailyNewItemsTarget = parseInt(userSetting.value, 10);
            }
        } catch (error) {
            console.warn('Failed to get user study cards setting, using default:', error);
        }

        // 计算当天的正常复习数量
        const normalReviewCount = await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                fsrs_due: {
                    gte: startOfDay,
                    lte: endOfDay
                }
            }
        });

        // 计算逾期复习数量（应该在今天之前复习的，以今天为基准）
        const overdueReviewCount = await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                fsrs_due: {
                    lt: todayStart  // 小于今天开始时间的都是逾期
                }
            }
        });

        return {
            newStudy: dailyNewItemsTarget,      // 每日新学习目标数量
            review: normalReviewCount,          // 当天正常复习数量
            overdueReview: overdueReviewCount   // 逾期复习数量（以今天为基准）
        };
    }

    private async getStudyCountForWeek(userId: number, weekStart: Date): Promise<number> {
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        return await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                updated_at: {
                    gte: weekStart,
                    lte: weekEnd
                },
                status: { not: 'new' }  // Only count records that have been actually studied
            }
        });
    }

    private async getScheduledCountForWeek(userId: number, weekStart: Date): Promise<{ newStudy: number, review: number, overdueReview: number }> {
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        // 获取今天的开始时间（用于逾期判断）
        const today = new Date();
        const todayStart = new Date(today);
        todayStart.setHours(0, 0, 0, 0);

        // 获取用户每日学习目标
        let dailyNewItemsTarget = 10;
        try {
            const userSetting = await this.getUserSetting(userId.toString(), 'study_cards_per_session');
            if (userSetting?.value) {
                dailyNewItemsTarget = parseInt(userSetting.value, 10);
            }
        } catch (error) {
            console.warn('Failed to get user study cards setting, using default:', error);
        }

        // 计算一周的正常复习数量
        const normalReviewCount = await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                fsrs_due: {
                    gte: weekStart,
                    lte: weekEnd
                }
            }
        });

        // 计算逾期复习数量（应该在今天之前复习的，以今天为基准）
        const overdueReviewCount = await prisma.user_learning_records.count({
            where: {
                user_id: userId,
                status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                fsrs_due: {
                    lt: todayStart  // 小于今天开始时间的都是逾期
                }
            }
        });

        return {
            newStudy: dailyNewItemsTarget * 7,      // 一周的新学习目标数量
            review: normalReviewCount,              // 一周的正常复习数量
            overdueReview: overdueReviewCount       // 逾期复习数量（以今天为基准）
        };
    }



    async getActivityHeatmapData(userId: string) {
        const userIdInt = parseInt(userId);
        const today = new Date();
        const startDate = new Date(today);
        startDate.setDate(startDate.getDate() - 364); // 365 days ago

        // Find the start of the week (Sunday)
        const startOfWeek = new Date(startDate);
        startOfWeek.setDate(startDate.getDate() - startDate.getDay());

        // Get all actual learning activities in the past year (based on status changes)
        const activities = await prisma.user_learning_records.findMany({
            where: {
                user_id: userIdInt,
                updated_at: {
                    gte: startDate,
                    lte: today
                },
                status: { not: 'new' }  // Only count records that have been actually studied
            },
            select: {
                updated_at: true
            }
        });

        // Group activities by date
        const activityMap = new Map<string, number>();
        activities.forEach(activity => {
            const dateStr = activity.updated_at.toISOString().split('T')[0];
            activityMap.set(dateStr, (activityMap.get(dateStr) || 0) + 1);
        });

        // Generate 53 weeks of data
        const weeks = [];
        for (let week = 0; week < 53; week++) {
            const weekData = [];
            for (let day = 0; day < 7; day++) {
                const currentDate = new Date(startOfWeek);
                currentDate.setDate(startOfWeek.getDate() + (week * 7) + day);

                if (currentDate <= today && currentDate >= startDate) {
                    const dateStr = currentDate.toISOString().split('T')[0];
                    const count = activityMap.get(dateStr) || 0;

                    // 强度等级计算：按新的标准分级
                    let intensity = 0;
                    if (count === 0) {
                        intensity = 0;  // 无活动 - 灰色
                    } else if (count <= 10) {
                        intensity = 1;  // 1-10个活动 - 浅绿色
                    } else if (count <= 20) {
                        intensity = 2;  // 11-20个活动 - 中浅绿色
                    } else if (count <= 30) {
                        intensity = 3;  // 21-30个活动 - 中绿色
                    } else {
                        intensity = 4;  // 31+个活动 - 深绿色
                    }

                    weekData.push({
                        date: dateStr,
                        intensity,
                        count,
                        dayOfWeek: day
                    });
                } else {
                    weekData.push(null);
                }
            }
            weeks.push(weekData);
        }

        return weeks;
    }
    
    async getUserProgress(userId: string) {
        const userIdInt = parseInt(userId);

        // 获取整体词汇学习进度
        const [totalVocabulary, masteredVocabulary, learningVocabulary] = await prisma.$transaction([
            prisma.vocabulary.count(),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, record_type: 'vocabulary', status: 'mastered' }
            }),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, record_type: 'vocabulary', status: { not: 'new' } }
            })
        ]);

        // 获取整体语法学习进度
        const [totalGrammar, masteredGrammar, learningGrammar] = await prisma.$transaction([
            prisma.grammar_points.count(),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, record_type: 'grammar', status: 'mastered' }
            }),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, record_type: 'grammar', status: { not: 'new' } }
            })
        ]);

        // 获取文章阅读进度
        const [totalArticles, readArticles] = await prisma.$transaction([
            prisma.articles.count(),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, record_type: 'article', status: 'completed' }
            })
        ]);

        const vocabularyProgress = {
            total: totalVocabulary,
            mastered: masteredVocabulary,
            learning: learningVocabulary,
            percentage: totalVocabulary > 0 ? Math.round((masteredVocabulary / totalVocabulary) * 100) : 0
        };

        const grammarProgress = {
            total: totalGrammar,
            mastered: masteredGrammar,
            learning: learningGrammar,
            percentage: totalGrammar > 0 ? Math.round((masteredGrammar / totalGrammar) * 100) : 0
        };

        return {
            vocabularyProgress,
            grammarProgress,
            articleProgress: {
                total_articles: totalArticles,
                read_articles: readArticles,
                percentage: totalArticles > 0 ? Math.round((readArticles / totalArticles) * 100) : 0
            }
        };
    }

    async getVocabulary(filters: { userId: string, level?: string, status?: string, search?: string, page?: number, limit?: number }) {
        const userIdInt = parseInt(filters.userId);
        const page = filters.page || 1;
        const limit = filters.limit || 20;
        const offset = (page - 1) * limit;

        const where: any = {};
        if (filters.search) where.word = { contains: filters.search };
        if (filters.level && filters.level !== 'all') where.jlpt_level = filters.level.toUpperCase();

        if (filters.status && filters.status !== 'all') {
             if (filters.status === 'new') {
                where.user_learning_records = { none: { user_id: userIdInt, record_type: 'vocabulary' } };
            } else {
                where.user_learning_records = { some: { user_id: userIdInt, status: filters.status, record_type: 'vocabulary' } };
            }
        }

        const [vocabularies, totalCount] = await prisma.$transaction([
            prisma.vocabulary.findMany({
                where,
                include: {
                    user_learning_records: { where: { user_id: userIdInt, record_type: 'vocabulary' } },
                    article_vocabulary: {
                        take: 1,
                        include: {
                            article: {
                                select: { id: true, title: true }
                            }
                        }
                    }
                },
                orderBy: { created_at: 'desc' },
                skip: offset,
                take: limit
            }),
            prisma.vocabulary.count({ where })
        ]);

        const formattedVocabularies = vocabularies.map(v => {
            // Parse JSON fields
            let relatedWords = null;
            let verbInfo = null;
            let commonCollocations = null;
            let examples: string[] = [];

            try {
                if (v.related_words_json) {
                    relatedWords = JSON.parse(v.related_words_json);
                }
            } catch (e) {
                console.warn('Failed to parse related_words_json:', e);
            }

            try {
                if (v.verb_info_json) {
                    verbInfo = JSON.parse(v.verb_info_json);
                }
            } catch (e) {
                console.warn('Failed to parse verb_info_json:', e);
            }

            try {
                if ((v as any).common_collocations_json) {
                    commonCollocations = JSON.parse((v as any).common_collocations_json);
                }
            } catch (e) {
                console.warn('Failed to parse common_collocations_json:', e);
            }

            // Get examples from article context
            if (v.article_vocabulary.length > 0 && v.article_vocabulary[0].context) {
                examples = v.article_vocabulary[0].context.split('\n').filter(line => line.trim());
            }

            return {
                ...v,
                status: v.user_learning_records[0]?.status || 'new',
                addedDate: new Date(v.created_at).toLocaleDateString(),
                level: v.jlpt_level || 'N5',
                meaning: v.meaning_zh || v.meaning_en || '未知',
                relatedWords,
                verbInfo,
                commonCollocations,
                examples,
                // Add review count and article count
                review_count: v.user_learning_records[0]?.fsrs_reps || 0,
                article_count: v.article_vocabulary.length,
                // 添加日语解释字段
                explanationJa: v.explanation_ja
            };
        });

        return {
            vocabularies: formattedVocabularies,
            totalCount,
            currentPage: page,
            totalPages: Math.ceil(totalCount / limit)
        };
    }

    async getVocabularyStats(userId: string) {
        const userIdInt = parseInt(userId);
        const [total, learningCount] = await prisma.$transaction([
            prisma.vocabulary.count(),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    record_type: 'vocabulary',
                    status: { not: 'new' }
                }
            })
        ]);
        return {
            total,
            learning: learningCount,
            pending: total - learningCount
        };
    }

    async getGrammarPoints(filters: { userId: string, level?: string, status?: string, search?: string, page?: number, limit?: number }) {
        const userIdInt = parseInt(filters.userId);
        const page = filters.page || 1;
        const limit = filters.limit || 20;
        const offset = (page - 1) * limit;

        // 构建查询条件
        const whereConditions: any = {};

        if (filters.search) {
            whereConditions.OR = [
                { pattern: { contains: filters.search } },
                { meaning_zh: { contains: filters.search } },
                { explanation: { contains: filters.search } }
            ];
        }

        if (filters.level && filters.level !== 'all') {
            whereConditions.jlpt_level = filters.level;
        }

        // 获取语法点数据
        const [grammarPoints, totalCount] = await prisma.$transaction([
            prisma.grammar_points.findMany({
                where: whereConditions,
                include: {
                    user_learning_records: {
                        where: { user_id: userIdInt },
                        select: { status: true, id: true }
                    }
                },
                orderBy: { created_at: 'desc' },
                skip: offset,
                take: limit
            }),
            prisma.grammar_points.count({ where: whereConditions })
        ]);

        // 格式化数据，添加学习状态并解析JSON字段
        const formattedGrammarPoints = grammarPoints.map(point => {
            // 解析JSON字段
            let similarPatterns = null;
            let examples: string[] = [];
            let commonCollocations: string[] = [];

            try {
                if ((point as any).similar_grammar_json) {
                    similarPatterns = JSON.parse((point as any).similar_grammar_json);
                }
            } catch (e) {
                console.warn('Failed to parse similar_grammar_json for', point.pattern, ':', e);
            }

            try {
                if ((point as any).examples_json) {
                    examples = JSON.parse((point as any).examples_json);
                }
            } catch (e) {
                console.warn('Failed to parse examples_json for', point.pattern, ':', e);
            }

            try {
                if ((point as any).common_collocations_json) {
                    commonCollocations = JSON.parse((point as any).common_collocations_json);
                }
            } catch (e) {
                console.warn('Failed to parse common_collocations_json for', point.pattern, ':', e);
            }

            return {
                ...point,
                status: point.user_learning_records.length > 0 ? point.user_learning_records[0].status : 'new',
                learning_record_id: point.user_learning_records.length > 0 ? point.user_learning_records[0].id : null,
                // 添加解析后的字段
                similarPatterns,
                examples,
                commonCollocations,
                // 添加日期格式化
                addedDate: point.created_at ? new Date(point.created_at).toLocaleDateString('zh-CN') : '未知'
            };
        });

        // 如果有状态筛选，进行过滤
        let filteredGrammarPoints = formattedGrammarPoints;
        if (filters.status && filters.status !== 'all') {
            filteredGrammarPoints = formattedGrammarPoints.filter(point => point.status === filters.status);
        }

        return {
            grammarPoints: filteredGrammarPoints,
            totalCount: filters.status && filters.status !== 'all' ? filteredGrammarPoints.length : totalCount,
            currentPage: page,
            totalPages: Math.ceil((filters.status && filters.status !== 'all' ? filteredGrammarPoints.length : totalCount) / limit)
        };
    }

    async getGrammarStats(userId: string) {
        const userIdInt = parseInt(userId);
        const [total, learningCount] = await prisma.$transaction([
            prisma.grammar_points.count(),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    record_type: 'grammar',
                    status: { not: 'new' }
                }
            })
        ]);
        return {
            total,
            learning: learningCount,
            pending: total - learningCount
        };
    }

    async addVocabulary(data: { word: string, reading: string, meaning_zh: string, jlpt_level: string }) {
        const vocab = await prisma.vocabulary.create({ data: {...data, extraction_method: 'manual'} });
        return vocab.id;
    }

    async updateLearningProgress(data: any, userId: string) {
        const userIdInt = parseInt(userId);

        // 先尝试查找现有记录
        const existingRecord = await prisma.user_learning_records.findFirst({
            where: {
                user_id: userIdInt,
                record_type: data.record_type,
                article_id: data.article_id || null,
                vocabulary_id: data.vocabulary_id || null,
                grammar_point_id: data.grammar_point_id || null,
            }
        });

        if (existingRecord) {
            // 更新现有记录
            return prisma.user_learning_records.update({
                where: { id: existingRecord.id },
                data: { status: data.status, updated_at: new Date() }
            });
        } else {
            // 创建新记录
            return prisma.user_learning_records.create({
                data: {
                    user_id: userIdInt,
                    record_type: data.record_type,
                    article_id: data.article_id,
                    vocabulary_id: data.vocabulary_id,
                    grammar_point_id: data.grammar_point_id,
                    status: data.status,
                }
            });
        }
    }

    async getStudyQueue(userId: string, newItemsLimit?: number) {
        const userIdInt = parseInt(userId);
        const now = new Date();

        // 获取用户每日学习目标
        let dailyNewItemsTarget = newItemsLimit || 10;
        if (!newItemsLimit) {
            try {
                const userSetting = await this.getUserSetting(userId, 'study_cards_per_session');
                if (userSetting?.value) {
                    dailyNewItemsTarget = parseInt(userSetting.value, 10);
                }
            } catch (error) {
                console.warn('Failed to get user study cards setting, using default:', error);
            }
        }

        // 计算今天已经学习的新内容数量
        const todayStart = new Date(now);
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date(now);
        todayEnd.setHours(23, 59, 59, 999);

        // 查询今天从 'new' 状态转换为 'learning' 的记录数量
        // 这些记录的 last_reviewed_at 是今天，且状态不是 'new'
        const todayLearnedCount = await prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: { not: 'new' }, // 不是新状态，说明已经开始学习了
                fsrs_last_review: { // 最后复习时间是今天，说明今天学习过
                    gte: todayStart,
                    lte: todayEnd
                },
                fsrs_reps: 1 // 重复次数为1，说明是第一次学习（从new转换过来）
            }
        });

        // 计算今天还需要学习的新内容数量
        const remainingNewItemsToday = Math.max(0, dailyNewItemsTarget - todayLearnedCount);

        console.log(`User ${userId} daily target: ${dailyNewItemsTarget}, learned today: ${todayLearnedCount}, remaining: ${remainingNewItemsToday}`);

        // 获取用户已有的学习记录
        const existingRecords = await prisma.user_learning_records.findMany({
            where: { user_id: userIdInt },
            select: { vocabulary_id: true, grammar_point_id: true, record_type: true, status: true, fsrs_due: true, id: true }
        });

        // 创建已存在记录的映射
        const existingVocabIds = new Set(existingRecords.filter(r => r.record_type === 'vocabulary' && r.vocabulary_id).map(r => r.vocabulary_id));
        const existingGrammarIds = new Set(existingRecords.filter(r => r.record_type === 'grammar' && r.grammar_point_id).map(r => r.grammar_point_id));

        // 获取所有词汇和语法点
        const [allVocabulary, allGrammarPoints] = await prisma.$transaction([
            prisma.vocabulary.findMany({
                orderBy: { created_at: 'desc' }
            }),
            prisma.grammar_points.findMany({
                orderBy: { created_at: 'desc' }
            })
        ]);

        // 为没有学习记录的词汇和语法点自动创建学习记录
        const newVocabRecords = [];
        const newGrammarRecords = [];

        for (const vocab of allVocabulary) {
            if (!existingVocabIds.has(vocab.id)) {
                newVocabRecords.push({
                    user_id: userIdInt,
                    vocabulary_id: vocab.id,
                    record_type: 'vocabulary',
                    status: 'new'
                });
            }
        }

        for (const grammar of allGrammarPoints) {
            if (!existingGrammarIds.has(grammar.id)) {
                newGrammarRecords.push({
                    user_id: userIdInt,
                    grammar_point_id: grammar.id,
                    record_type: 'grammar',
                    status: 'new'
                });
            }
        }

        // 批量创建新的学习记录
        if (newVocabRecords.length > 0 || newGrammarRecords.length > 0) {
            try {
                await prisma.user_learning_records.createMany({
                    data: [...newVocabRecords, ...newGrammarRecords]
                });
            } catch (error) {
                // 忽略重复记录错误，因为可能存在并发创建的情况
                console.log('Some learning records may already exist, continuing...');
            }
        }

        // 获取学习队列，包含文章信息
        const [newItems, reviewItems] = await prisma.$transaction([
            prisma.user_learning_records.findMany({
                where: { user_id: userIdInt, status: 'new' },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1 // 只取第一个关联的文章
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1 // 只取第一个关联的文章
                            }
                        }
                    }
                },
                take: remainingNewItemsToday,
                orderBy: { created_at: 'asc' }
            }),
            prisma.user_learning_records.findMany({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: { lte: todayEnd } // 包括今天和之前逾期的所有复习项目
                },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    }
                },
                orderBy: { fsrs_due: 'asc' }
            })
        ]);

        const formatItem = (item: any) => {
            const content = item.vocabulary || item.grammar_point;
            let sourceArticle = null;

            // 获取关联的文章信息
            if (item.vocabulary && item.vocabulary.article_vocabulary && item.vocabulary.article_vocabulary.length > 0) {
                sourceArticle = item.vocabulary.article_vocabulary[0].article;
            } else if (item.grammar_point && item.grammar_point.article_grammar_points && item.grammar_point.article_grammar_points.length > 0) {
                sourceArticle = item.grammar_point.article_grammar_points[0].article;
            }

            // 解析JSON字段
            let formattedContent = { ...content };

            if (item.vocabulary) {
                // 解析词汇的JSON字段
                try {
                    if (content.related_words_json) {
                        formattedContent.relatedWords = JSON.parse(content.related_words_json);
                    }
                    if (content.verb_info_json) {
                        formattedContent.verbInfo = JSON.parse(content.verb_info_json);
                    }
                    if (content.common_collocations_json) {
                        formattedContent.commonCollocations = JSON.parse(content.common_collocations_json);
                    }
                } catch (error) {
                    console.warn('Failed to parse vocabulary JSON fields:', error);
                }

                // 获取例句（从article_vocabulary的context字段）
                if (item.vocabulary.article_vocabulary && item.vocabulary.article_vocabulary.length > 0) {
                    const context = item.vocabulary.article_vocabulary[0].context;
                    if (context) {
                        formattedContent.examples = context.split('\n').filter((line: string) => line.trim());
                    }
                }
            } else if (item.grammar_point) {
                // 解析语法的JSON字段
                try {
                    if (content.similar_grammar_json) {
                        formattedContent.similarPatterns = JSON.parse(content.similar_grammar_json);
                    }
                    if (content.examples_json) {
                        formattedContent.examples = JSON.parse(content.examples_json);
                    }
                    if (content.common_collocations_json) {
                        formattedContent.commonCollocations = JSON.parse(content.common_collocations_json);
                    }
                    // 添加pronunciation字段（从reading字段获取）
                    if (content.reading) {
                        formattedContent.pronunciation = content.reading;
                    }
                } catch (error) {
                    console.warn('Failed to parse grammar JSON fields:', error);
                }
            }

            return {
                id: item.id,
                type: item.record_type,
                content: formattedContent,
                sourceArticle: sourceArticle
            };
        };

        return { newItems: newItems.map(formatItem), reviewItems: reviewItems.map(formatItem) };
    }

    // 获取学习统计信息（包含总数统计和每日学习量设置）
    async getStudyStats(userId: string) {
        const userIdInt = parseInt(userId);
        const now = new Date();
        const todayEnd = new Date(now);
        todayEnd.setHours(23, 59, 59, 999);

        // 获取用户设置的每日学习数量
        let vocabularyTarget = 8;
        let grammarTarget = 5;
        let mixedTarget = 10;

        try {
            const [vocabSetting, grammarSetting, mixedSetting] = await Promise.all([
                this.getUserSetting(userId, 'study_vocabulary_per_session'),
                this.getUserSetting(userId, 'study_grammar_per_session'),
                this.getUserSetting(userId, 'study_cards_per_session')
            ]);

            if (vocabSetting?.value) vocabularyTarget = parseInt(vocabSetting.value, 10);
            if (grammarSetting?.value) grammarTarget = parseInt(grammarSetting.value, 10);
            if (mixedSetting?.value) mixedTarget = parseInt(mixedSetting.value, 10);
        } catch (error) {
            console.warn('Failed to get user study settings, using defaults:', error);
        }

        // 获取新学习项目总数统计（顶部卡片显示）
        const [totalNewVocabulary, totalNewGrammar] = await Promise.all([
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, status: 'new', record_type: 'vocabulary' }
            }),
            prisma.user_learning_records.count({
                where: { user_id: userIdInt, status: 'new', record_type: 'grammar' }
            })
        ]);

        // 获取复习项目统计
        const [reviewVocabularyCount, reviewGrammarCount] = await Promise.all([
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_due: { lte: todayEnd },
                    record_type: 'vocabulary'
                }
            }),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_due: { lte: todayEnd },
                    record_type: 'grammar'
                }
            })
        ]);

        return {
            // 顶部统计卡片数据（总数）
            totalStats: {
                newVocabulary: totalNewVocabulary,
                newGrammar: totalNewGrammar,
                reviewVocabulary: reviewVocabularyCount,
                reviewGrammar: reviewGrammarCount
            },
            // 每日学习量设置（学习区域显示）
            dailyTargets: {
                vocabularyTarget: Math.min(vocabularyTarget, totalNewVocabulary), // 不能超过可用数量
                grammarTarget: Math.min(grammarTarget, totalNewGrammar),
                mixedTarget: Math.min(mixedTarget, totalNewVocabulary + totalNewGrammar)
            }
        };
    }

    // 获取过滤的学习队列（支持内容类型过滤）
    async getFilteredStudyQueue(userId: string, mode: 'learn' | 'review', contentType: 'vocabulary' | 'grammar' | 'mixed') {
        const userIdInt = parseInt(userId);
        const now = new Date();

        // 获取用户设置
        let vocabularyLimit = 8;
        let grammarLimit = 5;
        let mixedLimit = 10;

        try {
            const [vocabSetting, grammarSetting, mixedSetting] = await Promise.all([
                this.getUserSetting(userId, 'study_vocabulary_per_session'),
                this.getUserSetting(userId, 'study_grammar_per_session'),
                this.getUserSetting(userId, 'study_cards_per_session')
            ]);

            if (vocabSetting?.value) vocabularyLimit = parseInt(vocabSetting.value, 10);
            if (grammarSetting?.value) grammarLimit = parseInt(grammarSetting.value, 10);
            if (mixedSetting?.value) mixedLimit = parseInt(mixedSetting.value, 10);
        } catch (error) {
            console.warn('Failed to get user study settings, using defaults:', error);
        }

        // 构建查询条件
        const baseWhere = { user_id: userIdInt };
        let contentFilter = {};
        let limit = mixedLimit;

        if (contentType === 'vocabulary') {
            contentFilter = { vocabulary_id: { not: null } };
            limit = vocabularyLimit;
        } else if (contentType === 'grammar') {
            contentFilter = { grammar_point_id: { not: null } };
            limit = grammarLimit;
        }

        const formatItem = (item: any) => ({
            id: item.id,
            type: item.vocabulary ? 'vocabulary' : 'grammar',
            vocabulary: item.vocabulary,
            grammar_point: item.grammar_point,
            status: item.status,
            fsrs_due: item.fsrs_due,
            article: item.vocabulary?.article_vocabulary?.[0]?.article ||
                    item.grammar_point?.article_grammar_points?.[0]?.article || null
        });

        if (mode === 'learn') {
            // 获取新学习内容
            const newItems = await prisma.user_learning_records.findMany({
                where: {
                    ...baseWhere,
                    ...contentFilter,
                    status: 'new'
                },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    }
                },
                take: limit,
                orderBy: { created_at: 'asc' }
            });

            return { newItems: newItems.map(formatItem), reviewItems: [] };
        } else {
            // 获取复习内容
            const todayEnd = new Date(now);
            todayEnd.setHours(23, 59, 59, 999);

            const reviewItems = await prisma.user_learning_records.findMany({
                where: {
                    ...baseWhere,
                    ...contentFilter,
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_due: { lte: todayEnd }
                },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    }
                },
                orderBy: { fsrs_due: 'asc' }
            });

            return { newItems: [], reviewItems: reviewItems.map(formatItem) };
        }
    }

    // 获取实时学习队列（包含刚刚需要重新学习的内容）
    // 获取今日学习进度（用于侧边栏显示）
    async getTodayLearningProgress(userId: string) {
        const userIdInt = parseInt(userId);
        const now = new Date();

        // 计算今天的时间范围
        const todayStart = new Date(now);
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date(now);
        todayEnd.setHours(23, 59, 59, 999);

        // 获取用户每日学习目标
        let vocabularyTarget = 8;
        let grammarTarget = 5;
        let dailyTarget = 10; // 保留向后兼容

        try {
            const [vocabSetting, grammarSetting, mixedSetting] = await Promise.all([
                this.getUserSetting(userId, 'study_vocabulary_per_session'),
                this.getUserSetting(userId, 'study_grammar_per_session'),
                this.getUserSetting(userId, 'study_cards_per_session')
            ]);

            if (vocabSetting?.value) vocabularyTarget = parseInt(vocabSetting.value, 10);
            if (grammarSetting?.value) grammarTarget = parseInt(grammarSetting.value, 10);
            if (mixedSetting?.value) dailyTarget = parseInt(mixedSetting.value, 10);
        } catch (error) {
            console.warn('Failed to get user study settings, using defaults:', error);
        }

        // 分别获取今天已学习的词汇和语法数量
        const [todayLearnedVocabulary, todayLearnedGrammar] = await Promise.all([
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    vocabulary_id: { not: null },
                    status: { not: 'new' },
                    fsrs_last_review: {
                        gte: todayStart,
                        lte: todayEnd
                    },
                    fsrs_reps: 1 // 第一次学习
                }
            }),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    grammar_point_id: { not: null },
                    status: { not: 'new' },
                    fsrs_last_review: {
                        gte: todayStart,
                        lte: todayEnd
                    },
                    fsrs_reps: 1 // 第一次学习
                }
            })
        ]);

        const todayLearnedCount = todayLearnedVocabulary + todayLearnedGrammar;

        // 分别获取今天的词汇和语法复习任务数量
        const [todayVocabularyReviews, todayGrammarReviews] = await Promise.all([
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    vocabulary_id: { not: null },
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_due: { lte: todayEnd }
                }
            }),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    grammar_point_id: { not: null },
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_due: { lte: todayEnd }
                }
            })
        ]);

        // 分别获取今天已完成的词汇和语法复习数量
        const [todayCompletedVocabularyReviews, todayCompletedGrammarReviews] = await Promise.all([
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    vocabulary_id: { not: null },
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_last_review: {
                        gte: todayStart,
                        lte: todayEnd
                    },
                    fsrs_reps: { gt: 1 }
                }
            }),
            prisma.user_learning_records.count({
                where: {
                    user_id: userIdInt,
                    grammar_point_id: { not: null },
                    status: { notIn: ['new', 'mastered'] },
                    fsrs_last_review: {
                        gte: todayStart,
                        lte: todayEnd
                    },
                    fsrs_reps: { gt: 1 }
                }
            })
        ]);

        const todayReviewCount = todayVocabularyReviews + todayGrammarReviews;
        const todayCompletedReviews = todayCompletedVocabularyReviews + todayCompletedGrammarReviews;

        // 计算进度百分比
        const vocabularyProgress = vocabularyTarget > 0 ? Math.min(100, (todayLearnedVocabulary / vocabularyTarget) * 100) : 0;
        const grammarProgress = grammarTarget > 0 ? Math.min(100, (todayLearnedGrammar / grammarTarget) * 100) : 0;
        const newItemsProgress = dailyTarget > 0 ? Math.min(100, (todayLearnedCount / dailyTarget) * 100) : 0;
        const reviewProgress = todayReviewCount > 0 ? (todayCompletedReviews / todayReviewCount) * 100 : 100;
        const vocabularyReviewProgress = todayVocabularyReviews > 0 ? (todayCompletedVocabularyReviews / todayVocabularyReviews) * 100 : 100;
        const grammarReviewProgress = todayGrammarReviews > 0 ? (todayCompletedGrammarReviews / todayGrammarReviews) * 100 : 100;

        return {
            // 分别的词汇统计
            vocabulary: {
                learned: todayLearnedVocabulary,
                target: vocabularyTarget,
                remaining: Math.max(0, vocabularyTarget - todayLearnedVocabulary),
                percentage: Math.round(vocabularyProgress),
                reviews: {
                    completed: todayCompletedVocabularyReviews,
                    total: todayVocabularyReviews,
                    remaining: Math.max(0, todayVocabularyReviews - todayCompletedVocabularyReviews),
                    percentage: Math.round(vocabularyReviewProgress)
                }
            },
            // 分别的语法统计
            grammar: {
                learned: todayLearnedGrammar,
                target: grammarTarget,
                remaining: Math.max(0, grammarTarget - todayLearnedGrammar),
                percentage: Math.round(grammarProgress),
                reviews: {
                    completed: todayCompletedGrammarReviews,
                    total: todayGrammarReviews,
                    remaining: Math.max(0, todayGrammarReviews - todayCompletedGrammarReviews),
                    percentage: Math.round(grammarReviewProgress)
                }
            },
            // 保留原有的统计（向后兼容）
            newItems: {
                learned: todayLearnedCount,
                target: dailyTarget,
                remaining: Math.max(0, dailyTarget - todayLearnedCount),
                percentage: Math.round(newItemsProgress)
            },
            reviews: {
                completed: todayCompletedReviews,
                total: todayReviewCount,
                remaining: Math.max(0, todayReviewCount - todayCompletedReviews),
                percentage: Math.round(reviewProgress)
            },
            overall: {
                totalTasks: dailyTarget + todayReviewCount,
                completedTasks: todayLearnedCount + todayCompletedReviews,
                percentage: Math.round(((todayLearnedCount + todayCompletedReviews) / (dailyTarget + todayReviewCount || 1)) * 100)
            }
        };
    }

    async getActiveStudyQueue(userId: string) {
        const userIdInt = parseInt(userId);
        const now = new Date();

        // 获取用户每日学习目标
        let dailyNewItemsTarget = 10;
        try {
            const userSetting = await this.getUserSetting(userId, 'study_cards_per_session');
            if (userSetting?.value) {
                dailyNewItemsTarget = parseInt(userSetting.value, 10);
            }
        } catch (error) {
            console.warn('Failed to get user study cards setting, using default:', error);
        }

        // 计算今天已经学习的新内容数量
        const todayStart = new Date(now);
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date(now);
        todayEnd.setHours(23, 59, 59, 999);

        const todayLearnedCount = await prisma.user_learning_records.count({
            where: {
                user_id: userIdInt,
                status: { not: 'new' },
                fsrs_last_review: {
                    gte: todayStart,
                    lte: todayEnd
                },
                fsrs_reps: 1
            }
        });

        const remainingNewItemsToday = Math.max(0, dailyNewItemsTarget - todayLearnedCount);

        // 获取所有需要学习的内容（新内容 + 到期复习）
        const [newItems, reviewItems] = await prisma.$transaction([
            // 新内容
            prisma.user_learning_records.findMany({
                where: { user_id: userIdInt, status: 'new' },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    }
                },
                take: remainingNewItemsToday,
                orderBy: { created_at: 'asc' }
            }),
            // 到期复习（包括短间隔复习和逾期项目）
            prisma.user_learning_records.findMany({
                where: {
                    user_id: userIdInt,
                    status: { notIn: ['new', 'mastered'] }, // 排除new和mastered状态
                    fsrs_due: { lte: todayEnd } // 包括今天和之前逾期的所有复习项目
                },
                include: {
                    vocabulary: {
                        include: {
                            article_vocabulary: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    },
                    grammar_point: {
                        include: {
                            article_grammar_points: {
                                include: {
                                    article: {
                                        select: { id: true, title: true, url: true }
                                    }
                                },
                                take: 1
                            }
                        }
                    }
                },
                orderBy: { fsrs_due: 'asc' }
            })
        ]);

        const formatItem = (item: any) => {
            const content = item.vocabulary || item.grammar_point;
            let sourceArticle = null;

            if (item.vocabulary && item.vocabulary.article_vocabulary && item.vocabulary.article_vocabulary.length > 0) {
                sourceArticle = item.vocabulary.article_vocabulary[0].article;
            } else if (item.grammar_point && item.grammar_point.article_grammar_points && item.grammar_point.article_grammar_points.length > 0) {
                sourceArticle = item.grammar_point.article_grammar_points[0].article;
            }

            // 解析JSON字段
            let formattedContent = { ...content };

            if (item.vocabulary) {
                // 解析词汇的JSON字段
                try {
                    if (content.related_words_json) {
                        formattedContent.relatedWords = JSON.parse(content.related_words_json);
                    }
                    if (content.verb_info_json) {
                        formattedContent.verbInfo = JSON.parse(content.verb_info_json);
                    }
                    if (content.common_collocations_json) {
                        formattedContent.commonCollocations = JSON.parse(content.common_collocations_json);
                    }
                } catch (error) {
                    console.warn('Failed to parse vocabulary JSON fields:', error);
                }

                // 获取例句（从article_vocabulary的context字段）
                if (item.vocabulary.article_vocabulary && item.vocabulary.article_vocabulary.length > 0) {
                    const context = item.vocabulary.article_vocabulary[0].context;
                    if (context) {
                        formattedContent.examples = context.split('\n').filter((line: string) => line.trim());
                    }
                }
            } else if (item.grammar_point) {
                // 解析语法的JSON字段
                try {
                    if (content.similar_grammar_json) {
                        formattedContent.similarPatterns = JSON.parse(content.similar_grammar_json);
                    }
                    if (content.examples_json) {
                        formattedContent.examples = JSON.parse(content.examples_json);
                    }
                    if (content.common_collocations_json) {
                        formattedContent.commonCollocations = JSON.parse(content.common_collocations_json);
                    }
                    // 添加pronunciation字段（从reading字段获取）
                    if (content.reading) {
                        formattedContent.pronunciation = content.reading;
                    }
                } catch (error) {
                    console.warn('Failed to parse grammar JSON fields:', error);
                }
            }

            return {
                id: item.id,
                type: item.record_type,
                content: formattedContent,
                sourceArticle: sourceArticle,
                isReview: item.status === 'learning' // 标记是否为复习内容
            };
        };

        return {
            newItems: newItems.map(formatItem),
            reviewItems: reviewItems.map(formatItem),
            hasActiveReviews: reviewItems.length > 0 // 是否有需要立即复习的内容
        };
    }

    async submitReview(recordId: number, quality: number, userId: string) {
        // 使用新的 FSRS 服务
        const { fsrsService } = await import('./fsrs-service');
        return fsrsService.submitReview(recordId, quality, userId);
    }

    // --- Chat Sessions & Messages ---
    async createChatSession(userId: number, sessionType: string = 'qa', title?: string, scenarioConfig?: string) {
        return prisma.chat_sessions.create({
            data: {
                user_id: userId,
                session_type: sessionType,
                title: title || `${sessionType === 'qa' ? '智能问答' : sessionType === 'grammar' ? '语法解析' : sessionType === 'scenario' ? '情景对话' : '学习指导'} - ${new Date().toLocaleString('zh-CN')}`,
                scenario_config: scenarioConfig
            }
        });
    }

    async getChatSessions(userId: number, limit: number = 50) {
        return prisma.chat_sessions.findMany({
            where: { user_id: userId },
            include: {
                chat_messages: {
                    select: {
                        id: true,
                        role: true,
                        content: true,
                        created_at: true
                    },
                    orderBy: { created_at: 'asc' },
                    take: 3  // 获取前3条消息用于预览
                },
                _count: {
                    select: { chat_messages: true }
                }
            },
            orderBy: { updated_at: 'desc' },
            take: limit
        });
    }

    async getChatSession(sessionId: number, userId: number) {
        return prisma.chat_sessions.findFirst({
            where: { id: sessionId, user_id: userId },
            include: {
                chat_messages: {
                    orderBy: { created_at: 'asc' }
                }
            }
        });
    }

    async updateChatSession(sessionId: number, userId: number, data: { title?: string; scenario_config?: string }) {
        return prisma.chat_sessions.update({
            where: { id: sessionId },
            data: {
                ...data,
                updated_at: new Date()
            }
        });
    }

    async deleteChatSession(sessionId: number, userId: number) {
        // 先删除相关消息，然后删除会话
        await prisma.chat_messages.deleteMany({
            where: { session_id: sessionId }
        });

        return prisma.chat_sessions.delete({
            where: { id: sessionId }
        });
    }

    async addChatMessage(sessionId: number, role: string, content: string, metadata?: string) {
        // 添加消息并更新会话的updated_at时间
        const message = await prisma.chat_messages.create({
            data: {
                session_id: sessionId,
                role,
                content,
                metadata
            }
        });

        // 更新会话的最后更新时间
        await prisma.chat_sessions.update({
            where: { id: sessionId },
            data: { updated_at: new Date() }
        });

        return message;
    }

    async getChatMessages(sessionId: number, limit?: number) {
        return prisma.chat_messages.findMany({
            where: { session_id: sessionId },
            orderBy: { created_at: 'asc' },
            ...(limit && { take: limit })
        });
    }

    async getChatHistory(userId: number, sessionId?: number) {
        if (sessionId) {
            return this.getChatSession(sessionId, userId);
        } else {
            return this.getChatSessions(userId);
        }
    }

    // --- Article Read Status ---
    async markArticleAsRead(userId: number, articleId: number) {
        try {
            await prisma.user_article_reads.upsert({
                where: {
                    user_id_article_id: {
                        user_id: userId,
                        article_id: articleId
                    }
                },
                update: {
                    read_at: new Date(),
                    updated_at: new Date()
                },
                create: {
                    user_id: userId,
                    article_id: articleId,
                    read_at: new Date()
                }
            });
            return { success: true };
        } catch (error) {
            console.error('Error marking article as read:', error);
            return { success: false, error: (error as Error).message };
        }
    }

    async getUserReadArticles(userId: number, articleIds?: number[]) {
        const where: any = { user_id: userId };
        if (articleIds && articleIds.length > 0) {
            where.article_id = { in: articleIds };
        }

        const readRecords = await prisma.user_article_reads.findMany({
            where,
            select: {
                article_id: true,
                read_at: true
            }
        });

        // 返回一个Map，key是article_id，value是read_at
        const readMap = new Map();
        readRecords.forEach(record => {
            readMap.set(record.article_id, record.read_at);
        });

        return readMap;
    }

    async isArticleReadByUser(userId: number, articleId: number) {
        const record = await prisma.user_article_reads.findUnique({
            where: {
                user_id_article_id: {
                    user_id: userId,
                    article_id: articleId
                }
            }
        });
        return !!record;
    }
}

export const dbManager = new DatabaseManager();
export { prisma };
