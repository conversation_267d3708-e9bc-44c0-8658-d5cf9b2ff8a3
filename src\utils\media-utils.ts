/**
 * 媒体文件路径处理工具函数
 */

/**
 * 将旧的静态路径转换为API路径
 * @param path 原始路径
 * @returns 转换后的API路径
 */
export function convertToApiPath(path: string | null): string | null {
  if (!path) return null;
  if (path.startsWith('/api/')) return path; // 已经是API路径
  if (path.startsWith('/media/')) {
    return `/api${path}`; // 转换为API路径
  }
  return path; // 其他情况保持不变
}

/**
 * 获取媒体文件的完整访问路径
 * 优先使用本地路径（通过API），回退到远程URL
 * @param localPath 本地文件路径
 * @param remoteUrl 远程文件URL
 * @returns 最终的访问路径
 */
export function getMediaSrc(localPath: string | null, remoteUrl: string | null): string | null {
  return convertToApiPath(localPath) || remoteUrl;
}

/**
 * 检查路径是否为本地媒体文件
 * @param path 文件路径
 * @returns 是否为本地媒体文件
 */
export function isLocalMediaPath(path: string | null): boolean {
  if (!path) return false;
  return path.startsWith('/media/') || path.startsWith('/api/media/');
}

/**
 * 从API路径提取原始媒体路径
 * @param apiPath API路径
 * @returns 原始媒体路径
 */
export function extractMediaPath(apiPath: string): string | null {
  if (!apiPath) return null;
  if (apiPath.startsWith('/api/media/')) {
    return apiPath.replace('/api', '');
  }
  if (apiPath.startsWith('/media/')) {
    return apiPath;
  }
  return null;
}
