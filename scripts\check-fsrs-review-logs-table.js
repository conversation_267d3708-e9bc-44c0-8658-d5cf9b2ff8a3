const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkFsrsReviewLogsTable() {
  try {
    console.log('=== 检查 fsrs_review_logs 表 ===\n');

    // 检查表是否存在
    try {
      const tableInfo = await prisma.$queryRaw`PRAGMA table_info(fsrs_review_logs)`;
      console.log('✅ fsrs_review_logs 表存在');
      console.log('表结构：');
      tableInfo.forEach(field => {
        console.log(`  - ${field.name}: ${field.type} ${field.notnull ? 'NOT NULL' : ''} ${field.dflt_value ? `DEFAULT ${field.dflt_value}` : ''}`);
      });

      // 检查表中的数据
      const count = await prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM fsrs_review_logs');
      console.log(`\n表中记录数: ${count[0].count}`);

    } catch (error) {
      console.log('❌ fsrs_review_logs 表不存在');
      console.log('错误:', error.message);

      // 检查所有表
      console.log('\n当前数据库中的所有表:');
      const tables = await prisma.$queryRaw`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `;
      tables.forEach(table => {
        console.log(`  - ${table.name}`);
      });
    }

  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkFsrsReviewLogsTable();
