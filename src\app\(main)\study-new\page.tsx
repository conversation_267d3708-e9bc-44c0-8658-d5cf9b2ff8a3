'use client';
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import EnhancedStudyModeSelector from '@/components/EnhancedStudyModeSelector';
import StudySessionWithMode from '@/components/StudySessionWithMode';
import { Loader2 } from 'lucide-react';

export default function StudyNewPage() {
  const { session, status } = useAuth();
  const [selectedMode, setSelectedMode] = useState<{
    mode: 'learn' | 'review';
    contentType: 'vocabulary' | 'grammar' | 'mixed';
  } | null>(null);

  const handleModeSelect = (mode: 'learn' | 'review', contentType: 'vocabulary' | 'grammar' | 'mixed') => {
    setSelectedMode({ mode, contentType });
  };

  const handleModeChange = () => {
    setSelectedMode(null);
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        <span className="ml-2 text-gray-600">加载中...</span>
      </div>
    );
  }

  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="flex h-full items-center justify-center py-12">
        <div className="text-center">
          <p className="text-gray-600">请先登录以开始学习</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {!selectedMode ? (
        <EnhancedStudyModeSelector onModeSelect={handleModeSelect} />
      ) : (
        <div className="container mx-auto px-4 py-6">
          <StudySessionWithMode
            mode={selectedMode.mode}
            contentType={selectedMode.contentType}
            onModeChange={handleModeChange}
          />
        </div>
      )}
    </div>
  );
}
