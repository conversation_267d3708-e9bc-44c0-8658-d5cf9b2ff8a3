import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

export async function GET() {
  try {
    const setting = await dbManager.getSystemSetting('vocabulary_per_page');
    const value = setting ? parseInt(setting.value, 10) : 20;
    return NextResponse.json({ value });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
