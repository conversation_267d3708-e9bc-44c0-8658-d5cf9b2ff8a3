import { NextRequest, NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

// 创建数据库备份
export async function POST(request: NextRequest) {
  try {
    const result = await dbManager.createDatabaseBackup();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: '数据库备份创建成功',
        filename: result.filename
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('创建数据库备份失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// 获取备份文件列表
export async function GET(request: NextRequest) {
  try {
    const backupFiles = await dbManager.getBackupFiles();
    const settings = await dbManager.getBackupSettings();
    
    return NextResponse.json({
      success: true,
      backupFiles,
      settings
    });
  } catch (error: any) {
    console.error('获取备份文件列表失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
