'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { stripRubyTags } from '@/utils/ruby-utils';

export const useTTS = () => {
  const [isSupported, setIsSupported] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  const populateVoiceList = useCallback(() => {
    if ('speechSynthesis' in window) {
      const newVoices = window.speechSynthesis.getVoices();
      setVoices(newVoices);
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setIsSupported(true);
      populateVoiceList();
      window.speechSynthesis.onvoiceschanged = populateVoiceList;
    }
    return () => {
      if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, [populateVoiceList]);

  const cancel = useCallback(() => {
    if (!isSupported) return;
    window.speechSynthesis.cancel();
    setIsPlaying(false);
  }, [isSupported]);

  useEffect(() => {
    return () => {
      cancel();
    };
  }, [cancel]);

  const speak = useCallback((text: string, lang: string = 'ja-JP') => {
    if (!isSupported || !text) return;

    if (window.speechSynthesis.speaking) {
      cancel();
    }

    // 自动清理ruby标签，只保留主要文本用于TTS发音
    const cleanText = stripRubyTags(text);
    const utterance = new SpeechSynthesisUtterance(cleanText);
    utteranceRef.current = utterance;
    utterance.lang = lang;

    // --- Enhanced Voice Selection Logic ---
    const japaneseVoices = voices.filter(voice => voice.lang === lang);

    if (japaneseVoices.length > 0) {
      const getScore = (voice: SpeechSynthesisVoice) => {
        let score = 0;
        if (voice.name.includes('Google')) score += 10;
        if (voice.name.includes('Microsoft')) score += 8;
        if (voice.name.includes('Otoya')) score += 5; // A known good Mac voice
        if (!voice.localService) score += 15; // Prefer network voices (often higher quality)
        return score;
      };

      const sortedVoices = japaneseVoices.sort((a, b) => getScore(b) - getScore(a));
      utterance.voice = sortedVoices[0];
    } else {
      // Fallback if no specific language voice is found
      const fallbackVoice = voices.find(voice => voice.lang.startsWith(lang.split('-')[0])) || voices[0];
      if (fallbackVoice) {
        utterance.voice = fallbackVoice;
      }
    }
    // --- End of Enhanced Logic ---

    utterance.onstart = () => setIsPlaying(true);
    utterance.onend = () => {
      setIsPlaying(false);
      utteranceRef.current = null;
    };
    utterance.onerror = (event: SpeechSynthesisErrorEvent) => {
      console.error('SpeechSynthesisUtterance error:', event.error);
      setIsPlaying(false);
      utteranceRef.current = null;
    };

    window.speechSynthesis.speak(utterance);
  }, [isSupported, cancel, voices]);

  return { isSupported, isPlaying, speak, cancel };
};
