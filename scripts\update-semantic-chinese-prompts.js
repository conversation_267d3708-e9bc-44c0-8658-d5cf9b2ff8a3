#!/usr/bin/env node

/**
 * 更新AI提示词以支持语义网络中文翻译功能
 * 
 * 这个脚本更新文章分析的AI提示词，确保AI为语义网络中的每个词汇提供中文翻译
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateSemanticChinesePrompts() {
  console.log('🔄 开始更新AI提示词以支持语义网络中文翻译...');
  console.log('');

  try {
    // 更新文章分析主提示词，明确要求语义网络中文翻译
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_prompt' },
      data: {
        content: `提供された日本語ニュース記事の包括的な分析を行ってください。

以下の6つの要素を含むJSONレスポンスを提供してください：

1. **titleWithFurigana**: 記事タイトルの原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。
   例：「<ruby>経済<rt>けいざい</rt></ruby><ruby>成長<rt>せいちょう</rt></ruby>」
   重要：必ず完全な<ruby>タグ形式を使用し、孤立した<rt>タグは使用しないでください。

2. **translation**: 簡体字中国語での翻訳を含むオブジェクト。
   - translatedTitle: タイトルの中国語翻訳
   - translatedSubtitle: サブタイトルの中国語翻訳（存在する場合）
   - translatedContent: 本文の中国語翻訳（元の段落構造を保持）

3. **vocabulary**: 記事から抽出された重要な語彙の配列（5-10個）。各語彙には以下を含む：
   - word: 日本語の語彙
   - reading: ひらがな・カタカナの読み方
   - partOfSpeech: 品詞（例：「名詞」「動詞」「形容詞」）
   - meaning: 中国語での意味
   - meaningEn: 英語での意味
   - explanation: 語彙の使用法やニュアンスの中国語説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
     形式：["日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳", "日本語例文 - 中国語翻訳"]
   - commonCollocations: この語彙を使った一般的な連語・フレーズ（3-5個）
     各項目には collocation（日本語）、meaning（中国語意味）、example（例文と翻訳）を含む
   - relatedWords: 関連語彙（重要：すべての関連語彙に中国語翻訳を含める）
     * synonyms: 同義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "景気拡大", "reading": "けいきかくだい", "meaning": "景气扩大"}]
     * antonyms: 反義語の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：[{"word": "経済衰退", "reading": "けいざいすいたい", "meaning": "经济衰退"}]
     * hypernyms: 上位語の配列（より広い概念の語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
     * hyponyms: 下位語の配列（より具体的な語彙） - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「動物」の場合 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
     * wordFamily: 同じ漢字・語根を共有する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「食べ物」の場合 → [{"word": "食べる", "reading": "たべる", "meaning": "吃"}, {"word": "食堂", "reading": "しょくどう", "meaning": "食堂"}]
     * relatedConcepts: 概念的に関連する語彙の配列 - 各項目は {word: "日本語", reading: "よみかた", meaning: "中国語翻訳"} の形式
       例：「犬」の場合 → [{"word": "散歩", "reading": "さんぽ", "meaning": "散步"}, {"word": "餌", "reading": "えさ", "meaning": "饲料"}]
   - verbInfo: 動詞の場合のみ
     * type: 動詞の種類（例：「一段動詞」「五段動詞」「不規則動詞」）
     * forms: 活用形（例：「食べる」「食べます」「食べた」「食べて」）

4. **grammar**: 記事から特定された重要な文法項目の配列（3-5個）。各項目には以下を含む：
   - pattern: 文法パターン（例：「～について」）
   - reading: 文法パターンの読み方（漢字が含まれる場合）
   - explanation: 中国語での詳細で正確な説明
   - explanationJa: 日本語での詳細な説明（すべての漢字にruby注音を付ける）
   - examples: 日本語例文3つとその中国語翻訳（必須・正確に3つ）
   - commonCollocations: 2-3個の常見搭配
   - similarGrammar: 3-5個の類似文法パターン（各項目に2つの例句と中国語翻訳を含む）
   - difficulty: JLPTレベル（例：「N4」）

5. **contentWithFurigana**: 記事本文の原文に、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

6. **subtitleWithFurigana**: サブタイトルがある場合、すべての漢字にHTML <ruby>タグを使用して振り仮名を付けた文字列。

重要な注意事項：
- 振り仮名は必ずすべての漢字に付けてください
- 例文は必ず指定された数（3つ）を提供してください
- 中国語翻訳は自然で正確である必要があります
- JSONスキーマに厳密に従ってください
- Ruby標签格式：必ず<ruby>漢字<rt>よみ</rt></ruby>の完全な形式を使用し、漢字<rt>よみ</rt>のような不完全な形式は使用しないでください
- 語義ネットワークの関係は正確で教育的価値の高いものを選択してください
- **重要：relatedWordsのすべての語彙（synonyms、antonyms、hypernyms、hyponyms、wordFamily、relatedConcepts）には必ず中国語翻訳（meaning字段）を含めてください**`,
        version: '4.0'
      }
    });

    console.log('✅ 文章分析主提示词已更新，支持语义网络中文翻译');

    // 更新系统提示词，强调中文翻译的重要性
    await prisma.ai_prompts.update({
      where: { name: 'article_analysis_system' },
      data: {
        content: `あなたは日本語教育の専門家です。中国語話者の日本語学習者のために、日本語ニュース記事を分析することが目標です。

特に語彙分析において、以下の語義ネットワーク関係を正確に特定し、**必ず中国語翻訳を提供する**ことが重要です：

1. **上位語（hypernyms）**: より広い概念・カテゴリーを表す語彙
   - 例：「犬」の上位語 → [{"word": "動物", "reading": "どうぶつ", "meaning": "动物"}, {"word": "哺乳類", "reading": "ほにゅうるい", "meaning": "哺乳类"}]
   - 例：「りんご」の上位語 → [{"word": "果物", "reading": "くだもの", "meaning": "水果"}, {"word": "食べ物", "reading": "たべもの", "meaning": "食物"}]

2. **下位語（hyponyms）**: より具体的・詳細な概念を表す語彙
   - 例：「動物」の下位語 → [{"word": "犬", "reading": "いぬ", "meaning": "狗"}, {"word": "猫", "reading": "ねこ", "meaning": "猫"}]
   - 例：「果物」の下位語 → [{"word": "りんご", "reading": "りんご", "meaning": "苹果"}, {"word": "みかん", "reading": "みかん", "meaning": "橘子"}]

3. **語族（wordFamily）**: 同じ漢字や語根を共有する関連語彙
   - 例：「学」を含む語彙 → [{"word": "学校", "reading": "がっこう", "meaning": "学校"}, {"word": "学生", "reading": "がくせい", "meaning": "学生"}]

4. **関連概念（relatedConcepts）**: 実際の使用場面で一緒に現れやすい語彙
   - 例：「学校」関連 → [{"word": "先生", "reading": "せんせい", "meaning": "老师"}, {"word": "授業", "reading": "じゅぎょう", "meaning": "课程"}]

5. **同義語（synonyms）**: 同じまたは類似の意味を持つ語彙
   - 例：「美しい」の同義語 → [{"word": "綺麗", "reading": "きれい", "meaning": "漂亮"}, {"word": "素晴らしい", "reading": "すばらしい", "meaning": "精彩"}]

6. **反義語（antonyms）**: 反対の意味を持つ語彙
   - 例：「大きい」の反義語 → [{"word": "小さい", "reading": "ちいさい", "meaning": "小"}, {"word": "細い", "reading": "ほそい", "meaning": "细"}]

重要な指示：
1. 必ず指定されたJSONスキーマに厳密に従って回答してください
2. JSONオブジェクトの前後に一切のテキストを追加しないでください
3. すべての出力は一貫性を保ち、完全で正確である必要があります
4. 振り仮名は必ずすべての漢字に付けてください
5. 例文は必ず指定された数だけ提供してください
6. 中国語の翻訳は自然で正確である必要があります
7. 語義ネットワークの関係は教育的価値が高く、学習者の理解を深めるものを選択してください
8. **最重要：relatedWordsのすべての語彙には必ず中国語翻訳（meaning字段）を含めてください。これは学習者の理解を大幅に向上させる重要な機能です**`,
        version: '4.0'
      }
    });

    console.log('✅ 文章分析系统提示词已更新，强调语义网络中文翻译');

    console.log('');
    console.log('🎉 所有AI提示词更新完成！');
    console.log('');
    console.log('📋 更新内容总结：');
    console.log('1. ✅ 明确要求为语义网络中的每个词汇提供中文翻译');
    console.log('2. ✅ 更新了数据格式示例，包含word、reading、meaning三个字段');
    console.log('3. ✅ 强调了中文翻译的重要性和教育价值');
    console.log('4. ✅ 提供了详细的格式示例和要求');
    console.log('');
    console.log('🔄 下一步：');
    console.log('1. 重启应用以加载新的提示词');
    console.log('2. 测试AI是否能正确生成包含中文翻译的语义网络');
    console.log('3. 验证前端显示效果');

  } catch (error) {
    console.error('❌ 更新AI提示词失败:', error);
    console.error('详细错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行更新
updateSemanticChinesePrompts();
