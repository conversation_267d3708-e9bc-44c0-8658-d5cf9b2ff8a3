# 生词本管理功能说明

## 1. 功能概述

### 1.1 主要功能
生词本管理是个人化学习工具，允许用户收藏、管理和复习在阅读过程中遇到的词汇。系统提供智能复习算法、学习状态跟踪和个性化学习建议。

### 1.2 核心特性
- **词汇收藏**: 从文章中一键收藏词汇到生词本
- **状态管理**: 跟踪词汇的学习状态（新词、学习中、已掌握、困难）
- **智能复习**: 基于遗忘曲线的复习提醒系统
- **分类筛选**: 按JLPT等级、词性、状态等维度筛选
- **学习统计**: 词汇学习进度和效果分析
- **导入导出**: 支持词汇数据的批量导入和导出

### 1.3 适用场景
- 个人词汇库的建立和维护
- 系统化的词汇复习和记忆
- 学习进度的跟踪和分析
- 考试准备的词汇整理

## 2. 使用指南

### 2.1 收藏词汇
1. 在文章阅读页面点击词汇
2. 在词汇解释弹窗中点击"收藏"按钮
3. 词汇自动添加到个人生词本
4. 系统记录收藏时间和来源文章

### 2.2 管理词汇
1. 进入生词本页面查看收藏的词汇
2. 使用筛选器按条件查找词汇
3. 点击词汇查看详细信息
4. 更新词汇的学习状态

### 2.3 复习词汇
1. 查看"今日复习"列表
2. 进行词汇复习测试
3. 根据掌握程度更新状态
4. 系统自动调整下次复习时间

### 2.4 学习分析
1. 查看词汇学习统计图表
2. 分析各等级词汇掌握情况
3. 了解学习薄弱环节
4. 制定针对性学习计划

## 3. 界面详解

### 3.1 生词本主页面 (`/vocabulary`)

#### 页面布局
- **顶部**: 搜索栏和筛选器
- **左侧**: 分类导航和统计信息
- **主体**: 词汇列表展示
- **右侧**: 词汇详情面板（桌面端）

#### 筛选器组件
| 筛选项 | 选项值 | 数据库对应 | 说明 |
|--------|--------|------------|------|
| JLPT等级 | N5-N1, 全部 | vocabulary.jlpt_level | 词汇难度等级 |
| 学习状态 | 新词、学习中、已掌握、困难 | user_learning_records.status | 当前学习状态 |
| 词性 | 名词、动词、形容词等 | vocabulary.part_of_speech | 词汇词性分类 |
| 来源文章 | 文章标题列表 | article_vocabulary.article_id | 词汇来源 |
| 添加时间 | 今天、本周、本月 | user_learning_records.created_at | 收藏时间 |

#### 词汇列表项
| 显示项 | 数据库字段 | 说明 |
|--------|------------|------|
| 词汇 | vocabulary.word | 日语原词 |
| 读音 | vocabulary.reading | 假名读音 |
| 中文释义 | vocabulary.meaning_zh | 中文解释 |
| 英文释义 | vocabulary.meaning_en | 英文解释 |
| 词性标签 | vocabulary.part_of_speech | 词性分类 |
| JLPT等级 | vocabulary.jlpt_level | 等级标识 |
| 学习状态 | user_learning_records.status | 状态标识 |
| 复习次数 | user_learning_records.review_count | 复习统计 |
| 下次复习 | user_learning_records.next_review_at | 复习提醒 |

### 3.2 词汇详情面板

#### 基本信息区域
| 显示项 | 数据库字段 | 格式说明 |
|--------|------------|----------|
| 词汇 | vocabulary.word | 大字体显示 |
| 读音 | vocabulary.reading | 平假名/片假名 |
| 词性 | vocabulary.part_of_speech | 标签形式 |
| JLPT等级 | vocabulary.jlpt_level | 颜色标识 |
| 频率排名 | vocabulary.frequency_rank | 使用频率 |
| 难度评分 | vocabulary.difficulty_score | 1-10分 |

#### 释义和例句
| 内容类型 | 数据源 | 显示方式 |
|----------|--------|----------|
| 中文释义 | vocabulary.meaning_zh | 主要释义 |
| 英文释义 | vocabulary.meaning_en | 辅助释义 |
| 相关词汇 | vocabulary.related_words_json | JSON解析显示 |
| 动词变位 | vocabulary.verb_info_json | 表格形式 |
| 例句 | 关联文章内容 | 上下文展示 |

#### 学习记录区域
| 记录项 | 数据库字段 | 说明 |
|--------|------------|------|
| 学习状态 | user_learning_records.status | 可编辑状态 |
| 收藏时间 | user_learning_records.created_at | 首次添加时间 |
| 复习次数 | user_learning_records.review_count | 累计复习次数 |
| 正确率 | user_learning_records.accuracy_rate | 复习正确率 |
| 最后复习 | user_learning_records.last_reviewed_at | 最近复习时间 |
| 下次复习 | user_learning_records.next_review_at | 计算得出 |
| 来源文章 | article_vocabulary.article_id | 关联文章链接 |

### 3.3 复习系统界面

#### 今日复习列表
- **数据源**: `next_review_at <= TODAY`的词汇
- **排序规则**: 按复习优先级和时间排序
- **显示信息**: 词汇、读音、上次复习时间

#### 复习测试界面
| 测试类型 | 显示内容 | 用户操作 | 评分标准 |
|----------|----------|----------|----------|
| 读音测试 | 显示汉字 | 输入读音 | 完全匹配 |
| 释义测试 | 显示词汇 | 选择释义 | 选项正确性 |
| 听写测试 | 播放读音 | 输入词汇 | 拼写正确性 |
| 语境测试 | 显示例句 | 填空选择 | 语法正确性 |

#### 复习结果记录
```javascript
// 复习结果数据结构
const reviewResult = {
  vocabulary_id: number,
  user_id: number,
  test_type: string,
  is_correct: boolean,
  response_time: number,
  difficulty_rating: number, // 1-5用户主观难度
  reviewed_at: Date
};
```

## 4. 技术实现

### 4.1 核心代码文件

#### 生词本组件
- **文件**: `src/components/VocabularyBank.tsx`
- **功能**: 生词本主界面组件
- **主要功能**:
  - 词汇列表展示和筛选
  - 学习状态管理
  - 复习系统集成

#### 词汇管理服务
- **文件**: `src/lib/server/database.ts`
- **相关方法**:
  - `getVocabulary(filters)`: 获取用户词汇列表
  - `updateLearningProgress(data, userId)`: 更新学习进度
  - `getStudyQueue(userId)`: 获取复习队列
  - `submitReview(recordId, quality, userId)`: 提交复习结果

#### 复习算法
- **文件**: `src/lib/spaced-repetition.ts`
- **功能**: 间隔重复算法实现
- **算法**: 基于FSRS算法的科学记忆系统

### 4.2 数据库表结构

#### vocabulary表（词汇主表）
```sql
CREATE TABLE vocabulary (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  word TEXT NOT NULL,
  reading TEXT,
  meaning_zh TEXT,
  meaning_en TEXT,
  part_of_speech TEXT,
  jlpt_level TEXT,
  frequency_rank INTEGER,
  difficulty_score REAL,
  extraction_method TEXT DEFAULT 'ai',
  related_words_json TEXT,
  verb_info_json TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(word, reading)
);
```

#### user_learning_records表（学习记录）
```sql
CREATE TABLE user_learning_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  vocabulary_id INTEGER,
  article_id INTEGER,
  grammar_point_id INTEGER,
  record_type TEXT NOT NULL, -- 'vocabulary', 'article', 'grammar'
  status TEXT DEFAULT 'new', -- 'new', 'learning', 'mastered', 'difficult'
  review_count INTEGER DEFAULT 0,
  accuracy_rate REAL DEFAULT 0.0,
  last_reviewed_at DATETIME,
  next_review_at DATETIME,
  fsrs_difficulty REAL DEFAULT 5.0, -- FSRS难度参数
  fsrs_stability REAL DEFAULT 1.0, -- FSRS稳定性参数
  fsrs_due DATETIME, -- FSRS下次复习时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (vocabulary_id) REFERENCES vocabulary(id)
);
```

#### review_history表（复习历史）
```sql
CREATE TABLE review_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_learning_record_id INTEGER NOT NULL,
  test_type TEXT NOT NULL,
  is_correct BOOLEAN NOT NULL,
  response_time INTEGER, -- 毫秒
  difficulty_rating INTEGER, -- 1-5
  reviewed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_learning_record_id) REFERENCES user_learning_records(id)
);
```

### 4.3 复习算法实现

#### FSRS算法核心逻辑
系统现在使用先进的 FSRS (Free Spaced Repetition Scheduler) 算法，替代了传统的 SM-2 算法。

```javascript
// FSRS 算法特点：
// 1. 更科学的记忆曲线建模
// 2. 基于稳定性和难度的双重评估
// 3. 自动掌握判断功能
// 4. 更准确的复习间隔预测

const FSRS_PARAMS = {
  enable_fuzz: true,           // 启用模糊化
  enable_short_term: true,     // 启用短期记忆优化
  maximum_interval: 36500,     // 最大间隔100年
  request_retention: 0.9,      // 90%记忆保持率
};

// 自动掌握判断标准
const MASTERY_THRESHOLDS = {
  MIN_STABILITY_DAYS: 30,      // 最小稳定性30天
  MIN_REVIEWS: 5,              // 最少复习5次
  MAX_DIFFICULTY: 5.0,         // 最大难度值5.0
  MIN_SUCCESS_RATE: 0.8        // 最小成功率80%
};
```
```

### 4.4 API接口

#### 获取词汇列表
- **路径**: `GET /api/vocabulary`
- **参数**: `{level?, status?, search?, page?, limit?}`
- **返回**: 分页的词汇列表

#### 更新学习状态
- **路径**: `PUT /api/vocabulary/[id]/status`
- **参数**: `{status, difficulty?}`
- **返回**: 更新结果

#### 提交复习结果
- **路径**: `POST /api/vocabulary/review`
- **参数**: `{vocabularyId, quality, responseTime}`
- **返回**: 下次复习时间

#### 获取复习队列
- **路径**: `GET /api/vocabulary/review-queue`
- **返回**: 需要复习的词汇列表

## 5. 配置说明

### 5.1 复习算法配置

#### 初始参数
```javascript
const DEFAULT_REVIEW_CONFIG = {
  initialInterval: 1, // 首次复习间隔（天）
  initialEaseFactor: 2.5, // 初始难度系数
  minEaseFactor: 1.3, // 最小难度系数
  maxInterval: 365, // 最大复习间隔（天）
  graduationInterval: 6 // 毕业间隔（天）
};
```

#### 状态转换规则
```javascript
const STATUS_TRANSITIONS = {
  'new': ['learning', 'difficult'],
  'learning': ['mastered', 'difficult'],
  'mastered': ['learning'], // 复习错误时降级
  'difficult': ['learning', 'mastered']
};
```

### 5.2 显示配置

#### 分页设置
- **每页词汇数**: 20个
- **预加载**: 下一页数据预加载
- **虚拟滚动**: 大列表性能优化

#### 筛选器默认值
- **等级**: 全部
- **状态**: 全部
- **排序**: 按添加时间倒序

## 6. 故障排除

### 6.1 常见问题

#### 词汇收藏失败
**症状**: 点击收藏按钮无响应
**解决方案**:
1. 检查用户登录状态
2. 确认词汇ID有效性
3. 检查数据库连接

#### 复习时间计算错误
**症状**: 下次复习时间不合理
**解决方案**:
1. 检查FSRS算法参数配置
2. 确认时区设置正确
3. 验证数据库FSRS字段数据

#### 学习统计不准确
**症状**: 显示的学习进度与实际不符
**解决方案**:
1. 重新计算统计数据
2. 检查数据库索引
3. 清除缓存数据

### 6.2 性能优化

#### 数据库优化
```sql
-- 创建复合索引（已更新为FSRS字段）
CREATE INDEX idx_user_learning_records_fsrs_review
ON user_learning_records(user_id, fsrs_due, status);

CREATE INDEX idx_vocabulary_search
ON vocabulary(word, reading, meaning_zh);

-- FSRS相关字段索引
CREATE INDEX idx_fsrs_stability
ON user_learning_records(fsrs_stability);
```

#### 前端优化
- 使用虚拟滚动处理大列表
- 实现词汇列表的懒加载
- 缓存常用筛选结果

---

*文档版本: v1.0*
*最后更新: 2024年12月*
