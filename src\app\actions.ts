
'use server';

import { Enhanced<PERSON><PERSON><PERSON>craper } from '@/lib/server/scraper';
import { dbManager } from '@/lib/server/database';
import { RSSFetcher } from '@/lib/server/rss-parser';
import fs from 'fs';
import { join } from 'path';
import { revalidatePath } from 'next/cache';
import { analyzeArticle } from '@/ai/flows/analyze-article-flow';
import { tutorChat, type TutorChatInput } from '@/ai/flows/tutor-chat-flow';
import { analyzeGrammarText, type GrammarAnalysisInput } from '@/ai/flows/grammar-analysis-flow';

import { GoogleGenAI } from '@google/genai';
import { apiKeyManager } from '@/lib/server/api-key-manager';
import { getSession } from '@/lib/auth';

// In-memory state for active background tasks. In a real multi-server setup, this should be in a database or cache (e.g., Redis).
let isScraping = false;
let currentSessionId: string | null = null;
let isAIProcessing = false;
let aiProcessingSessionId: string | null = null;
let videoDownloadSessions: { [sessionId: string]: boolean } = {};
// API Key轮询索引（在并行处理中不再需要）

// AI处理器状态管理
const startAIProcessor = () => {
  if (isAIProcessing) return false;

  isAIProcessing = true;
  aiProcessingSessionId = `ai_session_${Date.now()}`;

  console.log('🤖 AI处理器手动启动');

  const log = (level: string, message: string) => {
    dbManager.addLog(aiProcessingSessionId!, level, message);
  };

  const processAITasks = async () => {
    try {
      // 使用API Key管理器获取可用的keys
      const apiKeys = await apiKeyManager.getAllAvailableKeys('google_gemini');

      // 如果没有配置的keys，尝试使用环境变量
      if (apiKeys.length === 0 && process.env.GEMINI_API_KEY) {
        apiKeys.push({
          id: 0,
          name: '.env Fallback',
          api_key: process.env.GEMINI_API_KEY,
          priority: 0,
          is_active: true,
          last_error: null,
          last_used_at: null,
          created_at: new Date(),
          provider: 'google_gemini',
          quota_reset_interval_minutes: 60,
          min_usage_interval_seconds: 1
        });
      }

      if (apiKeys.length === 0) {
        log('WARNING', '没有可用的API Key，AI处理器将等待配置...');
        return;
      }

      // 获取并发设置
      const concurrencySetting = await dbManager.getSystemSetting('ai_max_concurrency');
      const userMaxConcurrency = concurrencySetting?.value ? parseInt(concurrencySetting.value) : apiKeys.length;
      const maxConcurrency = Math.min(Math.max(1, userMaxConcurrency), apiKeys.length, 10); // 最小1，最大10
      const pendingTasks = await dbManager.getPendingAITasks('analysis', maxConcurrency * 2); // 获取更多任务以支持并发

      if (pendingTasks.length === 0) {
        // 没有任务时不记录日志，避免日志过多
        return;
      }

      log('INFO', `发现 ${pendingTasks.length} 个待处理任务，开始并行处理...`);
      log('INFO', `当前可用API Key: ${apiKeys.map(k => k.name).join(', ')}`);
      log('INFO', `最大并发数: ${maxConcurrency}`);

      const defaultModelSetting = await dbManager.getSystemSetting('default_text_model');
      const modelName = defaultModelSetting?.value || 'googleai/gemini-2.5flash';
      log('INFO', `使用AI模型: ${modelName}`);

      // 实现并行处理，使用API Key管理器
      const processTask = async (task: any) => {
        if (!isAIProcessing) {
          return { success: false, reason: 'AI处理器已停止' };
        }

        const contentToAnalyze = (task.article.content_html || task.article.content || '').trim();
        const subtitleToAnalyze = (task.article.subtitle || '').trim();

        if (!contentToAnalyze && !subtitleToAnalyze) {
          log('WARNING', `文章ID ${task.article_id} 内容为空，跳过处理`);
          await dbManager.updateAIQueueTaskStatus(task.id, 'skipped', '内容为空');
          await dbManager.updateArticleAIStatus(task.article_id, 'skipped', '内容为空');
          return { success: true, reason: '内容为空，已跳过' };
        }

        await dbManager.updateAIQueueTaskStatus(task.id, 'processing');

        // 获取下一个可用的API Key
        const apiKey = await apiKeyManager.getNextAvailableKey('google_gemini');
        if (!apiKey) {
          const errorMsg = '没有可用的API Key';
          log('ERROR', errorMsg);
          await dbManager.updateAIQueueTaskStatus(task.id, 'failed', errorMsg);
          await dbManager.updateArticleAIStatus(task.article_id, 'failed', errorMsg);
          return { success: false, reason: errorMsg };
        }

        try {
          log('INFO', `[${apiKey.name}] 开始处理文章: ${task.article.title.substring(0, 50)}...`);

          // 为每个并发任务创建独立的环境变量副本
          const originalKey = process.env.GEMINI_API_KEY;
          process.env.GEMINI_API_KEY = apiKey.api_key;

          const analysisResult = await analyzeArticle({
            title: task.article.title,
            subtitle: task.article.subtitle || undefined,
            content: task.article.content_html || task.article.content || '',
            modelName: modelName,
          });

          // 恢复原始环境变量
          process.env.GEMINI_API_KEY = originalKey;

          await dbManager.saveArticleAnalysis(task.article_id, analysisResult);
          await dbManager.updateAIQueueTaskStatus(task.id, 'completed');
          await dbManager.updateArticleAIStatus(task.article_id, 'completed');
          await apiKeyManager.markKeyUsed(apiKey.id);

          log('SUCCESS', `[${apiKey.name}] 文章处理完成: ${task.article.title.substring(0, 50)}...`);
          return { success: true, reason: '处理成功' };

        } catch (error: any) {
          log('ERROR', `[${apiKey.name}] 处理失败: ${error.message}`);

          // 标记API Key使用并记录错误
          await apiKeyManager.markKeyUsed(apiKey.id, error.message);

          // 检查是否是配额错误
          if (error.message.includes('429') || error.message.includes('quota') || error.message.includes('resource has been exhausted')) {
            // 配额错误时，将任务重新设为失败状态，但标记为可重试
            await dbManager.updateAIQueueTaskStatus(task.id, 'failed', `API Key配额不足，可重试: ${error.message}`);
            await dbManager.updateArticleAIStatus(task.article_id, 'failed', `API Key配额不足: ${error.message}`);
            return { success: false, reason: `API Key配额不足: ${error.message}` };
          } else {
            await dbManager.updateAIQueueTaskStatus(task.id, 'failed', error.message);
            await dbManager.updateArticleAIStatus(task.article_id, 'failed', error.message);
            return { success: false, reason: `处理失败: ${error.message}` };
          }
        }
      };

      // 并行处理任务
      const processingPromises: Promise<any>[] = [];
      const taskQueue = [...pendingTasks];

      // 启动并发处理线程
      for (let i = 0; i < Math.min(maxConcurrency, taskQueue.length); i++) {
        const task = taskQueue.shift();

        if (task) {
          const promise = processTask(task).then(async (result) => {
            // 如果还有待处理的任务，继续处理
            while (taskQueue.length > 0 && isAIProcessing) {
              const nextTask = taskQueue.shift();
              if (nextTask) {
                await processTask(nextTask);
              }
            }
            return result;
          });

          processingPromises.push(promise);
        }
      }

      // 等待所有并行任务完成
      if (processingPromises.length > 0) {
        const results = await Promise.allSettled(processingPromises);
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        const failCount = results.filter(r => r.status === 'rejected').length;

        log('INFO', `并行处理完成 - 成功: ${successCount}, 失败: ${failCount}`);
      }
    } catch (error: any) {
      log('ERROR', `AI处理器运行错误: ${error.message}`);
    }
  };

  // 持续运行AI处理器
  const runContinuously = async () => {
    while (isAIProcessing) {
      await processAITasks();
      if (isAIProcessing) {
        // 等待10秒后检查新任务
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    process.env.GEMINI_API_KEY = '';
    log('INFO', 'AI处理器已停止');
  };

  runContinuously().catch(error => {
    console.error('AI处理器运行出错:', error);
    isAIProcessing = false;
    process.env.GEMINI_API_KEY = '';
  });

  return true;
};

// 停止AI处理器
const stopAIProcessor = () => {
  if (!isAIProcessing) return false;

  isAIProcessing = false;
  console.log('🛑 AI处理器手动停止');
  return true;
};


export async function startScrapingAction() {
  if (isScraping) {
    return { success: false, error: 'A scraping task is already in progress.', sessionId: null };
  }

  const newScraper = new EnhancedNHKScraper();
  const sessionId = newScraper.sessionId;
  isScraping = true;
  currentSessionId = sessionId;

  console.log(`Server Action: Starting scraping task with session ID: ${sessionId}`);

  // Intentionally not awaiting this. The scrape runs in the background.
  newScraper.scrapeAllActiveSources()
    .then(results => {
      console.log(`Scraping task ${sessionId} finished:`, results);
      // Note: revalidatePath is removed from here to avoid Next.js render errors
      // Pages will be revalidated when users navigate or refresh
    })
    .catch(error => {
      console.error(`Scraping task ${sessionId} failed:`, error);
    })
    .finally(() => {
      if (currentSessionId === sessionId) {
        isScraping = false;
        currentSessionId = null;
      }
    });

  return { success: true, message: 'Scraping task started successfully.', sessionId };
}

export async function stopScrapingAction() {
  if (!isScraping) {
    return { success: false, error: 'No scraping task is currently running.' };
  }
  
  console.log(`Server Action: Stopping scraping task with session ID: ${currentSessionId}`);
  
  isScraping = false;
  currentSessionId = null;
  
  revalidatePath('/scraper');
  
  return { success: true, message: 'Scraping task has been stopped.' };
}

export async function getScrapingProgressAction(sessionId: string | null) {
  if (!sessionId) {
    return {
      isRunning: false,
      logs: [],
      stats: { total: 0, success: 0, failed: 0, aiScheduled: 0 }
    };
  }

  const { logs, stats } = await dbManager.getScrapingProgress(sessionId);
  return {
    isRunning: isScraping && currentSessionId === sessionId,
    logs,
    stats
  };
}

export async function getCurrentScrapingStatusAction() {
  return {
    isRunning: isScraping,
    sessionId: currentSessionId
  };
}

export async function clearScrapedDataAction() {
  try {
      await dbManager.clearArticlesAndRelatedData();

      const projectRoot = process.cwd();
      const mediaDir = join(projectRoot, 'public', 'media');
      
      if (fs.existsSync(mediaDir)) {
          fs.rmSync(mediaDir, { recursive: true, force: true });
          // Recreate directories
          fs.mkdirSync(mediaDir, { recursive: true });
          fs.mkdirSync(join(mediaDir, 'images'), { recursive: true });
          fs.mkdirSync(join(mediaDir, 'videos'), { recursive: true });
          fs.mkdirSync(join(mediaDir, 'audios'), { recursive: true });
      }

      revalidatePath('/', 'layout');

      return { success: true, message: '所有抓取的文章和媒体文件已被成功删除。' };
  } catch (error: any) {
      console.error('Failed to clear scraped data:', error);
      return { success: false, error: '删除抓取数据失败。', details: error.message };
  }
}

// RSS Source Actions
export async function saveRSSSourceAction(sourceData: any, fieldMappings: any[]) {
    try {
        if (sourceData.id) {
            // Update
            await dbManager.updateRSSSource(sourceData.id, sourceData);
            await dbManager.updateFieldMappings(sourceData.id, fieldMappings);
        } else {
            // Create
            const newSourceId = await dbManager.createRSSSource(sourceData);
            await dbManager.updateFieldMappings(newSourceId, fieldMappings);
        }
        revalidatePath('/rss-manager');
        return { success: true };
    } catch (error: any) {
        console.error('Failed to save RSS source:', error);
        return { success: false, error: error.message };
    }
}

export async function deleteRSSSourceAction(id: number) {
    try {
        await dbManager.deleteRSSSource(id);
        revalidatePath('/rss-manager');
        return { success: true };
    } catch (error: any) {
        console.error('Failed to delete RSS source:', error);
        return { success: false, error: error.message };
    }
}

export async function toggleRSSSourceActiveAction(source: any) {
    try {
        await dbManager.updateRSSSource(source.id, {
            ...source,
            is_active: !source.is_active,
        });
        revalidatePath('/rss-manager');
        return { success: true };
    } catch (error: any) {
        console.error('Failed to toggle RSS source active state:', error);
        return { success: false, error: error.message };
    }
}

export async function testRSSSourceAction(url: string) {
    try {
        const rssFetcher = new RSSFetcher();
        const result = await rssFetcher.testRSSSource(url);
        return { success: result.success, data: result };
    } catch (error: any) {
        console.error('Failed to test RSS source:', error);
        return { success: false, error: error.message };
    }
}



export async function clearAIQueueAction() {
  try {
    await dbManager.clearAIQueue();
    revalidatePath('/scraper');
    return { success: true, message: 'AI处理队列已清空' };
  } catch (error: any) {
    return { success: false, error: 'Failed to clear AI queue.', details: error.message };
  }
}

export async function getAIProcessingLogsAction(sessionId: string | null) {
    if (!sessionId) {
      return { logs: [], isRunning: false };
    }
    const logs = await dbManager.getLogsBySessionId(sessionId);
    return { logs, isRunning: isAIProcessing };
}



// Data Management Actions
export async function exportDataAction() {
    try {
        const data = await dbManager.exportData();
        return { success: true, data: JSON.parse(JSON.stringify(data)) }; // Ensure plain object
    } catch (error: any) {
        console.error('Failed to export data:', error);
        return { success: false, error: 'Failed to export data.', details: error.message };
    }
}

// Database Backup Actions
export async function createDatabaseBackupAction() {
    try {
        const result = await dbManager.createDatabaseBackup();
        return result;
    } catch (error: any) {
        console.error('Failed to create database backup:', error);
        return { success: false, error: error.message };
    }
}

export async function getBackupFilesAction() {
    try {
        const backupFiles = await dbManager.getBackupFiles();
        const settings = await dbManager.getBackupSettings();
        return { success: true, backupFiles, settings };
    } catch (error: any) {
        console.error('Failed to get backup files:', error);
        return { success: false, error: error.message, backupFiles: [], settings: null };
    }
}

export async function deleteBackupFileAction(filename: string) {
    try {
        const result = await dbManager.deleteBackupFile(filename);
        return result;
    } catch (error: any) {
        console.error('Failed to delete backup file:', error);
        return { success: false, error: error.message };
    }
}

export async function updateBackupSettingsAction(enabled: boolean, intervalHours: number, maxFiles: number) {
    try {
        await dbManager.updateBackupSettings(enabled, intervalHours, maxFiles);
        return { success: true, message: '备份设置已更新' };
    } catch (error: any) {
        console.error('Failed to update backup settings:', error);
        return { success: false, error: error.message };
    }
}

export async function cleanupOldBackupsAction() {
    try {
        const result = await dbManager.cleanupOldBackups();
        return result;
    } catch (error: any) {
        console.error('Failed to cleanup old backups:', error);
        return { success: false, error: error.message };
    }
}

// WAL Mode Actions
export async function getWALStatusAction() {
    try {
        const walConfig = await dbManager.getWALConfiguration();
        return { success: true, walConfig };
    } catch (error: any) {
        console.error('Failed to get WAL status:', error);
        return { success: false, error: error.message, walConfig: null };
    }
}

export async function enableWALModeAction() {
    try {
        const result = await dbManager.checkAndSetWALMode();
        const walConfig = await dbManager.getWALConfiguration();
        return { success: result, walConfig, message: result ? 'WAL模式已启用' : 'WAL模式启用失败' };
    } catch (error: any) {
        console.error('Failed to enable WAL mode:', error);
        return { success: false, error: error.message };
    }
}

export async function optimizeWALConfigAction() {
    try {
        const result = await dbManager.optimizeWALConfiguration();
        const walConfig = await dbManager.getWALConfiguration();
        return { success: result, walConfig, message: result ? 'WAL配置已优化' : 'WAL配置优化失败' };
    } catch (error: any) {
        console.error('Failed to optimize WAL config:', error);
        return { success: false, error: error.message };
    }
}

// Article Actions
export async function getArticleDetailsAction(id: number) {
    try {
        const article = await dbManager.getArticleById(id);
        if (!article) {
            return { success: false, error: 'Article not found' };
        }
        // Ensure data is a plain object before passing to client component
        return { success: true, data: JSON.parse(JSON.stringify(article)) };
    } catch (error: any) {
        console.error(`Failed to get article details for id ${id}:`, error);
        return { success: false, error: 'Failed to fetch article details.', details: error.message };
    }
}

export async function markArticleAsReadAction(articleId: number) {
    try {
        const session = await getSession();
        if (!session?.user?.id) {
            return { success: false, error: 'User not authenticated' };
        }

        const userId = parseInt(session.user.id);
        const result = await dbManager.markArticleAsRead(userId, articleId);

        if (result.success) {
            revalidatePath('/');
            return { success: true };
        } else {
            return { success: false, error: result.error };
        }
    } catch (error: any) {
        console.error('Error marking article as read:', error);
        return { success: false, error: 'Failed to mark article as read' };
    }
}



// Learning Progress Actions
export async function updateLearningProgressAction(data: {
  record_type: 'article' | 'vocabulary' | 'grammar';
  status: string;
  article_id?: number;
  vocabulary_id?: number;
  grammar_point_id?: number;
}, userId: string) {
  try {
    await dbManager.updateLearningProgress(data, userId);
    
    // Revalidate paths that might be affected
    if (data.record_type === 'vocabulary') {
      revalidatePath('/vocabulary');
    }
    if (data.record_type === 'grammar') {
      revalidatePath('/grammar');
    }
    revalidatePath('/dashboard');
    if(data.article_id) {
        revalidatePath(`/`); // For article lists
    }

    return { success: true };
  } catch (error: any) {
    console.error('Failed to update learning progress:', error);
    return { success: false, error: 'Failed to update progress.', details: error.message };
  }
}

// AI Model Management Actions
export async function syncAIModelsAction() {
  try {
    const apiKeys = await dbManager.getApiKeys() as any[];
    const keyToUse = apiKeys[0]?.api_key || process.env.GEMINI_API_KEY;
    if (!keyToUse) {
      throw new Error("同步失败：未在数据库或.env文件中配置任何API密钥。");
    }
    process.env.GEMINI_API_KEY = keyToUse;
    
    const genAI = new GoogleGenAI({apiKey: keyToUse || ""});
    const result = await genAI.models.list();
    const models = [];
    for await (const m of result) {
        if ((m as any).supportedActions?.includes('generateContent')) {
            models.push({
                model_id: (m.name || '').replace('models/', 'googleai/'),
                display_name: m.displayName || '',
                description: m.description || '',
                model_type: 'text',
            });
        }
    }
    await dbManager.syncAIModels(models);
    revalidatePath('/settings');
    return { success: true, message: `成功同步 ${models.length} 个模型。` };
  } catch (error: any) {
    console.error('Failed to sync AI models:', error);
    return { success: false, error: '同步模型失败，请检查您的网络或API密钥。', details: error.stack || error.message };
  }
}

export async function getAIModelsAction() {
    try {
        const models = await dbManager.getAIModels();
        return { success: true, data: models };
    } catch (error: any) {
        return { success: false, error: 'Failed to get models', details: error.message };
    }
}

export async function setSystemDefaultModelAction(modelId: string) {
    try {
        await dbManager.setSystemSetting('default_text_model', modelId);
        revalidatePath('/settings');
        revalidatePath('/scraper');
        return { success: true, message: "设置已保存。" };
    } catch (error: any) {
        return { success: false, error: '保存失败', details: error.message };
    }
}

export async function setArticlesPerPageAction(count: number) {
  try {
    if (count < 5 || count > 50) {
        throw new Error('每页文章数必须在5到50之间。');
    }
    await dbManager.setSystemSetting('articles_per_page', count.toString());
    revalidatePath('/settings');
    revalidatePath('/'); // Revalidate news page to apply new limit
    return { success: true, message: "设置已保存。" };
  } catch (error: any) {
    return { success: false, error: '保存失败', details: error.message };
  }
}

export async function setVocabularyPerPageAction(count: number) {
  try {
    if (count < 10 || count > 100) {
        throw new Error('每页词汇数必须在10到100之间。');
    }
    await dbManager.setSystemSetting('vocabulary_per_page', count.toString());
    revalidatePath('/settings');
    revalidatePath('/vocabulary'); // Revalidate vocabulary page to apply new limit
    return { success: true, message: "单词本分页设置已保存。" };
  } catch (error: any) {
    return { success: false, error: '保存单词本分页设置失败', details: error.message };
  }
}

export async function setGrammarPerPageAction(count: number) {
  try {
    if (count < 10 || count > 100) {
        throw new Error('每页语法点数必须在10到100之间。');
    }
    await dbManager.setSystemSetting('grammar_per_page', count.toString());
    revalidatePath('/settings');
    revalidatePath('/grammar'); // Revalidate grammar page to apply new limit
    return { success: true, message: "语法本分页设置已保存。" };
  } catch (error: any) {
    return { success: false, error: '保存语法本分页设置失败', details: error.message };
  }
}

export async function setAITemperatureAction(temperature: number) {
  try {
    if (temperature < 0 || temperature > 2) {
        throw new Error('温度值必须在0到2之间。');
    }
    await dbManager.setSystemSetting('ai_temperature', temperature.toString());
    revalidatePath('/settings');
    return { success: true, message: "AI温度设置已保存。" };
  } catch (error: any) {
    return { success: false, error: '保存AI温度设置失败', details: error.message };
  }
}

// Video Management Actions
export async function getVideosForManagementAction(page: number, limit: number) {
  try {
    const { videos, totalCount } = await dbManager.getVideosForManagement(page, limit);
    return { success: true, data: { videos, totalCount } };
  } catch (error: any) {
    return { success: false, error: 'Failed to fetch videos for management.', details: error.message };
  }
}

export async function startVideoDownloadAction(articleIds?: number[]) {
  const sessionId = `video_session_${Date.now()}`;
  videoDownloadSessions[sessionId] = true;
  const newScraper = new EnhancedNHKScraper();

  try {
    const pendingArticles = await dbManager.getPendingVideoDownloads(articleIds);

    if (pendingArticles.length === 0) {
      delete videoDownloadSessions[sessionId];
      return { success: true, message: '没有待处理的视频下载任务。', sessionId };
    }

    // 启动后台下载任务，不等待完成
    setImmediate(async () => {
      try {
        for (const article of pendingArticles) {
          try {
            if (!videoDownloadSessions[sessionId]) {
              console.log("Video download session cancelled.");
              break;
            }
            await dbManager.updateVideoDownloadStatus(article.id, 'downloading');
            const metadata = JSON.parse(article.video_metadata_json!);
            const videoPath = await newScraper.downloadAndRemux(article.video_m3u8_content!, metadata.m3u8Url, metadata.title, sessionId, { pubDate: article.publish_time?.toISOString() });
            if (videoPath) {
              await dbManager.updateVideoPath(article.id, videoPath);
              await dbManager.updateVideoDownloadStatus(article.id, 'completed');
            } else {
              throw new Error('下载或转码后未返回有效路径');
            }
          } catch (e: any) {
            await dbManager.updateVideoDownloadStatus(article.id, 'failed', e.message);
          }
        }
      } finally {
        delete videoDownloadSessions[sessionId];
      }
    });

    // 立即触发页面重新验证
    revalidatePath('/scraper');

    return { success: true, message: `已启动 ${pendingArticles.length} 个视频的下载任务。`, sessionId };
  } catch (error: any) {
    delete videoDownloadSessions[sessionId];
    return { success: false, error: '启动视频下载失败。', details: error.message, sessionId: null };
  }
}

export async function setUseLocalVideoAction(articleId: number, useLocal: boolean) {
  try {
    await dbManager.setUseLocalVideo(articleId, useLocal);
    revalidatePath('/', 'layout'); // Revalidate all pages to reflect change
    return { success: true };
  } catch (error: any) {
    return { success: false, error: '切换视频源失败。', details: error.message };
  }
}

export async function batchUpdateVideoSourceAction(articleIds: number[], useLocal: boolean) {
  try {
    await dbManager.batchSetUseLocalVideo(articleIds, useLocal);
    revalidatePath('/', 'layout');
    return { success: true, message: '批量切换成功' };
  } catch (error: any) {
    return { success: false, error: '批量切换视频源失败。', details: error.message };
  }
}

export async function getVideoDownloadLogsAction(sessionId: string | null) {
  if (!sessionId) {
      return { logs: [], isRunning: false };
  }
  const logs = await dbManager.getLogsBySessionId(sessionId);
  const isRunning = !!videoDownloadSessions[sessionId];
  return { logs, isRunning };
}

// 日志管理 Actions
export async function getLogStatsAction() {
  try {
    const stats = await dbManager.getLogStats();
    return { success: true, data: stats };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function cleanupLogsAction() {
  try {
    const deletedCount = await dbManager.performLogCleanupIfNeeded();
    return {
      success: true,
      message: `已清理 ${deletedCount} 条过期日志`,
      deletedCount
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// 抓取延迟配置 Actions
export async function getScrapingDelayConfigAction() {
  try {
    const config = await dbManager.getScrapingDelayConfig();
    return { success: true, data: config };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function updateScrapingDelayConfigAction(config: {
  rssSourceDelay: number;
  articleDelay: number;
  requestMinDelay: number;
  requestMaxDelay: number;
}) {
  try {
    await dbManager.updateScrapingDelayConfig(config);
    return { success: true, message: '抓取延迟配置已更新' };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// AI Analysis Management Actions
export async function getArticlesForAIManagementAction(page: number, limit: number) {
  try {
    const result = await dbManager.getArticlesForAIManagement(page, limit);
    return { success: true, data: result };
  } catch (error: any) {
    return { success: false, error: '获取文章列表失败。', details: error.message };
  }
}

export async function getAllUnprocessedArticleIdsAction() {
  try {
    const articleIds = await dbManager.getAllUnprocessedArticleIds();
    return { success: true, data: articleIds };
  } catch (error: any) {
    return { success: false, error: '获取未处理文章列表失败。', details: error.message };
  }
}

export async function scheduleAIAnalysisAction(articleIds?: number[]) {
  try {
    if (!articleIds || articleIds.length === 0) {
      return { success: false, error: '请选择要分析的文章。' };
    }

    const addedCount = await dbManager.scheduleAIAnalysisForArticles(articleIds);

    if (addedCount === 0) {
      return {
        success: true,
        message: '选中的文章都已经在AI分析队列中或已完成分析。'
      };
    }

    // 如果AI处理器没有运行，自动启动它
    if (!isAIProcessing) {
      console.log('AI处理器未运行，自动启动以处理新添加的任务...');
      // 不等待启动结果，让它在后台运行
      startAIProcessorAction().catch(error => {
        console.error('自动启动AI处理器失败:', error);
      });
    }

    revalidatePath('/scraper');

    return {
      success: true,
      message: `已将 ${addedCount} 篇文章添加到AI分析队列${!isAIProcessing ? '，并启动AI处理器' : ''}。`
    };
  } catch (error: any) {
    return {
      success: false,
      error: '添加AI分析任务失败。',
      details: error.message
    };
  }
}

// API Key Management Actions
export async function getApiKeysAction() {
    return await dbManager.getApiKeys();
}

export async function addApiKeyAction(name: string, key: string, quotaResetInterval: number = 60, minUsageInterval: number = 1) {
    try {
        await dbManager.addApiKey(name, key, quotaResetInterval, minUsageInterval);
        revalidatePath('/settings');
        return { success: true, message: 'API Key 添加成功。' };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}

export async function deleteApiKeyAction(id: number) {
    try {
        await dbManager.deleteApiKey(id);
        revalidatePath('/settings');
        return { success: true, message: 'API Key 已删除。' };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}

export async function updateApiKeyIntervalsAction(id: number, quotaResetInterval: number, minUsageInterval: number) {
    try {
        await dbManager.updateApiKeyIntervals(id, quotaResetInterval, minUsageInterval);
        revalidatePath('/settings');
        return { success: true, message: 'API Key 间隔配置已更新。' };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}



export async function toggleApiKeyStatusAction(id: number) {
    try {
        const updatedKey = await dbManager.toggleApiKeyStatus(id);
        revalidatePath('/settings');
        return {
            success: true,
            message: `API Key 已${updatedKey.is_active ? '启用' : '禁用'}。`,
            isActive: updatedKey.is_active
        };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}

export async function updateApiKeyOrderAction(orderedIds: number[]) {
    try {
        await dbManager.updateApiKeyOrder(orderedIds);
        revalidatePath('/settings');
        return { success: true, message: 'API Key 顺序已更新。' };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}


// New actions for user-specific data
export async function getLearningStatsAction(userId: string) {
  try {
    const stats = await dbManager.getLearningStats(userId);
    return { success: true, data: JSON.parse(JSON.stringify(stats)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get stats', details: error.message };
  }
}

export async function getUserProgressAction(userId: string) {
  try {
    const progress = await dbManager.getUserProgress(userId);
    return { success: true, data: JSON.parse(JSON.stringify(progress)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get progress', details: error.message };
  }
}

export async function getVocabularyAction(filters: { userId: string, level?: string, status?: string, search?: string, page?: number, limit?: number }) {
  try {
    const vocab = await dbManager.getVocabulary(filters);
    return { success: true, data: JSON.parse(JSON.stringify(vocab)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get vocabulary', details: error.message };
  }
}

export async function getVocabularyStatsAction(userId: string) {
  try {
    const stats = await dbManager.getVocabularyStats(userId);
    return { success: true, data: JSON.parse(JSON.stringify(stats)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get vocab stats', details: error.message };
  }
}

export async function getStudyQueueAction(userId: string) {
    try {
        const queue = await dbManager.getStudyQueue(userId);
        // Ensure data is plain object
        return { success: true, data: JSON.parse(JSON.stringify(queue)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get study queue', details: error.message };
    }
}

export async function getReviewTaskStatsAction(userId: string) {
    try {
        const stats = await dbManager.getReviewTaskStats(userId);
        return { success: true, data: JSON.parse(JSON.stringify(stats)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get review task stats', details: error.message };
    }
}

export async function getLearningEfficiencyStatsAction(userId: string) {
    try {
        const stats = await dbManager.getLearningEfficiencyStats(userId);
        return { success: true, data: JSON.parse(JSON.stringify(stats)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get learning efficiency stats', details: error.message };
    }
}

export async function getStudyTrendDataAction(userId: string, period: 'week' | 'month' | 'year') {
    try {
        const data = await dbManager.getStudyTrendData(userId, period);
        return { success: true, data: JSON.parse(JSON.stringify(data)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get study trend data', details: error.message };
    }
}

export async function getActivityHeatmapDataAction(userId: string) {
    try {
        const data = await dbManager.getActivityHeatmapData(userId);
        return { success: true, data: JSON.parse(JSON.stringify(data)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get activity heatmap data', details: error.message };
    }
}

// User Settings Actions
export async function getUserSettingAction(userId: string, key: string) {
    try {
        const setting = await dbManager.getUserSetting(userId, key);
        return { success: true, data: setting };
    } catch (error: any) {
        return { success: false, error: 'Failed to get user setting', details: error.message };
    }
}

export async function setUserSettingAction(userId: string, key: string, value: string) {
    try {
        await dbManager.setUserSetting(userId, key, value);
        revalidatePath('/settings');
        revalidatePath('/vocabulary');
        revalidatePath('/grammar');
        revalidatePath('/');
        return { success: true, message: '设置已保存' };
    } catch (error: any) {
        return { success: false, error: 'Failed to save user setting', details: error.message };
    }
}

export async function getUserSettingsAction(userId: string) {
    try {
        const settings = await dbManager.getUserSettings(userId);
        return { success: true, data: settings };
    } catch (error: any) {
        return { success: false, error: 'Failed to get user settings', details: error.message };
    }
}

export async function submitReviewAction(recordId: number, quality: 1 | 2 | 3 | 4, userId: string) {
    try {
        await dbManager.submitReview(recordId, quality, userId);
        revalidatePath('/study');
        return { success: true };
    } catch (error: any) {
        return { success: false, error: 'Failed to submit review', details: error.message };
    }
}

export async function getActiveStudyQueueAction(userId: string) {
    try {
        const queue = await dbManager.getActiveStudyQueue(userId);
        return { success: true, data: JSON.parse(JSON.stringify(queue)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get active study queue', details: error.message };
    }
}

export async function getFilteredStudyQueueAction(
    userId: string,
    mode: 'learn' | 'review',
    contentType: 'vocabulary' | 'grammar'
) {
    try {
        const queue = await dbManager.getFilteredStudyQueue(userId, mode, contentType);
        return { success: true, data: JSON.parse(JSON.stringify(queue)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get filtered study queue', details: error.message };
    }
}

export async function getStudyStatsAction(userId: string) {
    try {
        const stats = await dbManager.getStudyStats(userId);
        return { success: true, data: JSON.parse(JSON.stringify(stats)) };
    } catch (error: any) {
        return { success: false, error: 'Failed to get study stats', details: error.message };
    }
}


// AI Tutor Actions
export async function aiTutorChatAction(input: TutorChatInput) {
    // 使用API Key管理器获取可用的keys
    let apiKeys = await apiKeyManager.getAllAvailableKeys('google_gemini');

    // 如果没有配置的keys，尝试使用环境变量
    if (apiKeys.length === 0 && process.env.GEMINI_API_KEY) {
        apiKeys.push({
            id: 0,
            name: '.env Fallback',
            api_key: process.env.GEMINI_API_KEY,
            priority: 0,
            is_active: true,
            last_error: null,
            last_used_at: null,
            created_at: new Date(),
            provider: 'google_gemini',
            quota_reset_interval_minutes: 60,
            min_usage_interval_seconds: 1
        });
    }

    if (apiKeys.length === 0) {
        console.error("AI Tutor Action failed: No API keys configured.");
        return { success: false, error: '错误：未配置任何API Key，AI助教无法工作。' };
    }

    const defaultModelSetting = await dbManager.getSystemSetting('default_text_model');
    const modelName = defaultModelSetting?.value || 'googleai/gemini-2.5-flash';

    for (const key of apiKeys) {
        try {
            process.env.GEMINI_API_KEY = key.api_key;

            const result = await tutorChat({
                ...input,
                modelName,
            });

            await apiKeyManager.markKeyUsed(key.id);
            process.env.GEMINI_API_KEY = ''; // Clear after use
            return { success: true, data: result };

        } catch (error: any) {
            console.error(`AI Tutor Action failed with key ${key.name}:`, error);
            const errorMessage = error.message || 'Unknown error';
            await apiKeyManager.markKeyUsed(key.id, errorMessage);

            if (errorMessage.includes('429') || errorMessage.includes('resource has been exhausted')) {
                continue;
            } else {
                process.env.GEMINI_API_KEY = ''; // Clear on failure too
                return { success: false, error: 'AI助教暂时无法回复，请稍后再试。', details: errorMessage };
            }
        }
    }

    process.env.GEMINI_API_KEY = '';
    return { success: false, error: '所有可用的API Key均已达到配额，请稍后再试或添加新的Key。' };
}

export async function analyzeGrammarAction(input: GrammarAnalysisInput) {
    // 使用API Key管理器获取可用的keys
    let apiKeys = await apiKeyManager.getAllAvailableKeys('google_gemini');

    // 如果没有配置的keys，尝试使用环境变量
    if (apiKeys.length === 0 && process.env.GEMINI_API_KEY) {
        apiKeys.push({
            id: 0,
            name: '.env Fallback',
            api_key: process.env.GEMINI_API_KEY,
            priority: 0,
            is_active: true,
            last_error: null,
            last_used_at: null,
            created_at: new Date(),
            provider: 'google_gemini',
            quota_reset_interval_minutes: 60,
            min_usage_interval_seconds: 1
        });
    }

    if (apiKeys.length === 0) {
        console.error("Grammar Analysis Action failed: No API keys configured.");
        return { success: false, error: '错误：未配置任何API Key，语法分析无法工作。' };
    }
    
    const defaultModelSetting = await dbManager.getSystemSetting('default_text_model');
    const modelName = defaultModelSetting?.value || 'googleai/gemini-2.5-flash';

    for (const key of apiKeys) {
        try {
            process.env.GEMINI_API_KEY = key.api_key;
            
            const result = await analyzeGrammarText({
                ...input,
                modelName,
            });
            
            await apiKeyManager.markKeyUsed(key.id);
            process.env.GEMINI_API_KEY = '';
            return { success: true, data: result };

        } catch (error: any) {
            console.error(`Grammar Analysis Action failed with key ${key.name}:`, error);
            const errorMessage = error.message || 'Unknown error';
            await apiKeyManager.markKeyUsed(key.id, errorMessage);
            
            if (errorMessage.includes('429') || errorMessage.includes('resource has been exhausted')) {
                continue;
            } else {
                process.env.GEMINI_API_KEY = '';
                return { success: false, error: '语法分析失败，请稍后再试。', details: errorMessage };
            }
        }
    }
    
    process.env.GEMINI_API_KEY = '';
    return { success: false, error: '所有可用的API Key均已达到配额，请稍后再试或添加新的Key。' };
}

interface VocabularyData {
    word: string;
    reading: string;
    meaning_zh: string;
    jlpt_level: string;
}
export async function addVocabularyAction(vocabData: VocabularyData) {
    try {
        const vocabId = await dbManager.addVocabulary(vocabData);
        revalidatePath('/vocabulary');
        return { success: true, id: vocabId };
    } catch (e: any) {
        return { success: false, error: e.message };
    }
}

export async function getGrammarPointsAction(filters: { userId: string, level?: string, status?: string, search?: string, page?: number, limit?: number }) {
  try {
    const grammarPoints = await dbManager.getGrammarPoints(filters);
    return { success: true, data: JSON.parse(JSON.stringify(grammarPoints)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get grammar points', details: error.message };
  }
}

export async function getGrammarStatsAction(userId: string) {
  try {
    const stats = await dbManager.getGrammarStats(userId);
    return { success: true, data: JSON.parse(JSON.stringify(stats)) };
  } catch (error: any) {
    return { success: false, error: 'Failed to get grammar stats', details: error.message };
  }
}

// AI处理器控制Actions
export async function startAIProcessorAction() {
  try {
    if (isAIProcessing) {
      return { success: false, error: 'AI处理器已在运行中' };
    }

    const started = startAIProcessor();
    if (started) {
      revalidatePath('/settings');
      return { success: true, message: 'AI处理器已启动' };
    } else {
      return { success: false, error: '启动AI处理器失败' };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function stopAIProcessorAction() {
  try {
    const stopped = stopAIProcessor();
    if (stopped) {
      revalidatePath('/settings');
      return { success: true, message: 'AI处理器已停止' };
    } else {
      return { success: false, error: 'AI处理器未在运行' };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export async function getAIProcessorStatusAction() {
  try {
    const keyStats = await apiKeyManager.getKeyStats();
    return {
      success: true,
      data: {
        isRunning: isAIProcessing,
        sessionId: aiProcessingSessionId,
        keyStats
      }
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}
