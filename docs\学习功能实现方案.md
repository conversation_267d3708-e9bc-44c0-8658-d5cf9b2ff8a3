
# 学习功能实现方案

## 1. 功能目标

本方案旨在明确定义系统中的“学习”功能，它是“学习统计”功能的数据来源和基础。其核心目标如下：

- **行为可追踪**: 用户的每一个关键学习行为（如阅读文章、掌握单词）都应被系统准确记录。
- **状态可管理**: 用户能够主动地、方便地标记和更新他们对特定知识点（单词、语法）的掌握程度。
- **数据源可靠**: 为“学习统计”仪表盘提供一个清晰、准确、可靠的数据基础。
- **扩展性强**: 为未来更高级的功能（如基于艾宾浩斯遗忘曲线的复习系统、个性化学习路径推荐）奠定坚实的数据模型基础。

## 2. 核心数据模型

学习功能的核心是 `user_learning_records` 表，它像一本精密的日志，记录了每个用户与每个学习项目的交互。

**表结构 (`user_learning_records`):**

| 字段名 | 类型 | 说明 |
| :--- | :--- | :--- |
| `id` | INTEGER | 主键 |
| `user_id` | TEXT | 关联到特定用户 |
| `article_id` | INTEGER | 关联到文章 (如果记录类型是'article') |
| `vocabulary_id` | INTEGER | 关联到词汇 (如果记录类型是'vocabulary') |
| `grammar_point_id` | INTEGER | 关联到语法点 (如果记录类型是'grammar') |
| `record_type` | TEXT | 记录类型: 'article', 'vocabulary', 'grammar' |
| `status` | TEXT | **核心字段**: 学习状态 |
| `fsrs_due` | DATETIME | FSRS 下次复习时间 |
| `fsrs_stability` | REAL | FSRS 记忆稳定性 |
| `fsrs_difficulty` | REAL | FSRS 内容难度 |
| `fsrs_reps` | INTEGER | FSRS 复习次数 |
| `fsrs_state` | TEXT | FSRS 卡片状态 |
| `fsrs_last_review` | DATETIME | FSRS 最后复习时间 |

### 学习状态 (`status`) 定义

- **对于词汇和语法 (`vocabulary`, `grammar`):**
    - `new` (默认): 用户尚未进行任何操作。
    - `learning`: 用户正在积极学习中。
    - `mastered`: 用户已完全掌握。
    - `difficult`: 用户认为这是个难点，需要特别关注。
- **对于文章 (`article`):**
    - `unread` (默认): 用户未阅读。
    - `completed`: 用户已完成阅读。

## 3. 用户交互与数据生成

用户的学习记录是在与UI交互时产生的。以下是关键的交互点设计：

### 3.1 文章阅读 (`ArticleReader.tsx`)

- **标记文章为已读**:
    - **UI**: 在文章阅读器顶部或底部，增加一个清晰的“**标记为已读**” (`<CheckCircle />`) 按钮。
    - **数据流**: 用户点击后，调用 `updateLearningProgressAction`，向 `user_learning_records` 表中插入或更新一条记录，内容为 `{ user_id, article_id, record_type: 'article', status: 'completed' }`。

- **标记单词/语法状态**:
    - **UI**: 在文章右侧的“重点词汇”和“语法解析”列表中，为每一个项目旁边增加一组小巧的状态切换按钮或一个下拉菜单。
        - **建议按钮组**: "陌生", "学习中", "已掌握", "困难"。
    - **数据流**: 用户点击后，立即调用 `updateLearningProgressAction`，更新对应词汇或语法点的 `status`。

### 3.2 集中管理 (`VocabularyBank.tsx`, `GrammarBank.tsx`)

- **UI**: 这两个页面已经有了状态筛选和管理的基本框架。我们将强化状态切换按钮的功能，确保它们能正确地更新学习状态。
- **数据流**: 用户在这些页面上对某个词汇或语法点进行状态切换时，同样调用 `updateLearningProgressAction`。

## 4. 后端实现方案

### 4.1 数据库层 (`database.ts`)

- **`updateLearningProgress(data, userId)`**: 这是核心的数据库函数。
    - **逻辑**: 它将采用**UPSERT**（更新或插入）逻辑。
    - **步骤**:
        1.  首先根据 `user_id` 和 `vocabulary_id` (或 `grammar_point_id`, `article_id`) 查询记录是否存在。
        2.  如果**存在**，则 `UPDATE` 这条记录的 `status` 字段。
        3.  如果**不存在**，则 `INSERT` 一条全新的学习记录。
    - **优点**: 这种方式确保了每个用户对每个学习项目只有一条唯一的学习记录，避免了数据冗余。

### 4.2 服务层 (`actions.ts`)

- **`updateLearningProgressAction(data, userId)`**: 这是一个统一的、类型安全的Server Action。
    - **职责**: 作为前端所有学习状态更新的唯一入口。
    - **参数**:
        - `data`: 一个包含 `{ record_type, status, article_id?, vocabulary_id?, grammar_point_id? }` 的对象。
        - `userId`: 当前操作用户的ID。
    - **流程**:
        1.  接收前端请求。
        2.  进行基本的参数验证。
        3.  调用 `dbManager.updateLearningProgress` 函数执行数据库操作。
        4.  在数据库操作成功后，调用 `revalidatePath` 来智能地刷新受影响的页面（如“学习统计”、“生词本”等），确保UI能即时反映最新状态。

## 5. 实施步骤

1.  **后端先行**:
    -   在 `src/lib/server/database.ts` 中，精确地实现 `updateLearningProgress` 函数的UPSERT逻辑。
    -   在 `src/app/actions.ts` 中，创建 `updateLearningProgressAction` Server Action。
2.  **前端UI增强**:
    -   在 `ArticleReader.tsx` 中，添加“标记为已读”按钮和词汇/语法的状态管理控件。
3.  **连接前后端**:
    -   将所有前端UI控件的 `onClick` 事件，全部连接到新创建的 `updateLearningProgressAction`。
4.  **测试与验证**:
    -   进行完整的用户操作测试：阅读文章、标记单词等。
    -   在数据库中检查 `user_learning_records` 表，确认数据是否按照预期被正确地创建和更新。
    -   这一步的成功，将为“学习统计”功能的开发铺平道路。

通过以上方案，我们将构建一个健壮、可靠的学习记录系统，为所有上层智能功能的实现提供坚实的数据支持。
