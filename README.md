# NHK日语学习平台 - AI智能日语新闻学习

基于NHK新闻的智能日语学习平台，提供AI辅助学习、词汇标注、语法解析等功能。

## 🚀 新功能特性

### 完整的RSS源管理系统
- ✅ **真实RSS测试功能** - 连接后端API进行实时RSS源测试
- 🔧 **灵活字段映射** - 支持XPath和CSS选择器的字段配置
- 🤖 **可选AI处理** - 智能内容分析和翻译功能
- 📊 **实时监控** - RSS源状态和抓取统计
- 🎯 **精确控制** - 每个RSS源独立的抓取数量设置

### 完整的抓取管理系统
- ⚡ **实时抓取控制** - 启动、停止、监控抓取任务
- 📈 **进度实时监控** - 详细的抓取进度和统计信息
- 🤖 **AI处理队列** - 异步AI内容处理系统
- 📊 **抓取历史记录** - 完整的抓取会话历史
- 💾 **数据库管理** - 数据导出、清空、统计功能

### AI智能分析
- 🌐 **内容翻译** - 自动将日语文章翻译为中文
- 📚 **词汇提取** - 智能识别和提取重点词汇
- 📝 **语法分析** - 自动分析文章中的语法点
- 📊 **难度评估** - AI评估文章的学习难度级别
- 📄 **摘要生成** - 自动生成文章摘要

## 🛠️ 技术架构

### 前端技术栈
- **Next.js 15** + **React 19** - 现代化全栈框架
- **TypeScript** - 类型安全的开发体验
- **Tailwind CSS** - 实用优先的CSS框架
- **Lucide React** - 精美的图标库

### 后端技术栈
- **Next.js Server Actions** - 服务端逻辑处理
- **Prisma ORM** - 现代化数据库访问层
- **SQLite** - 本地数据库存储
- **Cheerio** - 服务端HTML解析
- **node-fetch** - HTTP请求库

### 数据库设计
- **RSS源管理** - 灵活的RSS源配置和字段映射
- **AI处理队列** - 异步AI内容处理系统
- **学习记录** - 完整的用户学习进度跟踪
- **媒体管理** - 图片、视频、音频文件存储

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 初始化数据库
```bash
# 创建新的数据库结构
npm run init-new-db
```

### 3. 启动服务
```bash
# 启动前端开发服务器
npm run dev

# 注意：所有API功能已集成到主应用中，无需单独启动API服务器
```

### 4. 访问应用
- **应用地址**: http://localhost:3000 (Next.js全栈应用)

## 📋 可用脚本

```bash
# 开发相关
npm run dev                # 启动Next.js开发服务器
npm run build              # 构建生产版本
npm run start              # 启动生产版本

# 数据库相关
npm run init-new-db        # 初始化新数据库结构
npm run db:reset           # 重置数据库（开发环境）

### 数据库重置（适用于开发环境）
如果遇到数据库结构问题或需要一个全新的数据库，可以使用以下方法重置数据库。
**警告**: 此操作将删除所有现有数据。

#### 方法一：使用自动化脚本（推荐）

我们提供了一个自动化脚本，可以一键完成数据库重置和初始化：

```bash
# 使用自动化脚本重置数据库（包含删除数据、创建表和填充初始数据）
npm run db:reset
```

#### 方法二：手动执行各个步骤

如果需要更精细的控制，可以手动执行以下步骤：

```bash
# 第 1 步：重置数据库（会删除所有数据），使用 --skip-seed 选项跳过自动执行种子脚本
npx prisma migrate reset --skip-seed

# 第 2 步：根据 Schema 创建所有表
npx prisma db push

# 第 3 步：手动执行种子脚本填充初始数据
npx prisma db seed
```


# 抓取相关
npm run scrape             # 运行增强版抓取器

# 代码质量
npm run lint               # 运行ESLint检查
```

## 🔧 完整功能列表

### RSS源管理功能
- ✅ **RSS源CRUD操作** - 添加、编辑、删除RSS源
- ✅ **实时RSS测试** - 连接真实RSS源进行内容解析
- ✅ **字段映射配置** - 灵活的XPath和CSS选择器配置
- ✅ **AI处理配置** - 为每个RSS源配置AI处理选项
- ✅ **状态监控** - 实时显示RSS源状态和统计信息
- ✅ **批量操作** - 批量启用/禁用RSS源

### 抓取管理功能
- ✅ **抓取任务控制** - 启动、停止、监控抓取任务
- ✅ **实时进度监控** - 详细的抓取进度和日志
- ✅ **会话历史记录** - 完整的抓取会话历史
- ✅ **AI处理队列管理** - 管理AI处理任务队列
- ✅ **数据库统计** - 实时数据库统计信息
- ✅ **数据导出/清空** - 数据管理功能

### AI处理功能
- ✅ **AI处理器控制** - 启动、停止AI处理器
- ✅ **处理队列监控** - 监控AI处理队列状态
- ✅ **处理统计分析** - 详细的AI处理统计
- ✅ **错误处理和重试** - 智能的错误处理机制
- ✅ **性能监控** - AI处理性能指标

### 数据管理功能
- ✅ **数据库统计** - 实时数据库表统计
- ✅ **数据导出** - JSON格式数据导出
- ✅ **数据清空** - 安全的数据清空功能
- ✅ **媒体文件管理** - 图片、视频、音频文件管理
- ✅ **磁盘使用监控** - 存储空间使用统计

## 🌐 API架构

### Next.js Server Actions
- 所有抓取管理功能通过Server Actions实现
- 统一的TypeScript类型安全
- 直接集成在主应用中，无需独立API服务器

### 主要功能接口
- **抓取管理** - 启动/停止抓取任务，获取进度和历史
- **RSS源管理** - 添加、编辑、删除、测试RSS源
- **AI处理** - 启动/停止AI处理器，管理处理队列
- **数据库操作** - 统计、备份、清理等数据库管理功能
- **用户管理** - 学习进度、设置管理等用户相关功能

### API路由
- `POST /api/rss/test` - RSS源测试
- 其他功能通过Server Actions实现，提供更好的类型安全和性能

## 📁 项目结构

```
nhk-japanese-learning/
├── src/                          # Next.js应用源代码
│   ├── app/                      # Next.js App Router
│   │   ├── (main)/              # 主应用页面
│   │   ├── api/                 # API路由
│   │   ├── actions.ts           # Server Actions
│   │   └── layout.tsx           # 根布局
│   ├── components/               # React组件
│   │   ├── RSSSourceManager.tsx  # RSS源管理组件
│   │   ├── EnhancedScraperManager.tsx # 抓取管理组件
│   │   ├── NewsReader.tsx        # 新闻阅读器
│   │   ├── AITutor.tsx          # AI助教
│   │   └── ...
│   ├── lib/                      # 工具库
│   │   └── server/              # 服务端工具
│   └── utils/                    # 客户端工具

├── scripts/                      # 脚本文件
│   └── enhanced-scraper.js      # 抓取脚本
├── prisma/                       # Prisma ORM
│   ├── schema.prisma            # 数据库模式
│   └── migrations/              # 数据库迁移
├── data/                         # 数据库文件
└── public/media/                 # 媒体文件存储
```

## 🎯 核心功能

### 1. RSS源管理
- 添加、编辑、删除RSS源
- 实时测试RSS源有效性
- 灵活的字段映射配置
- 抓取数量和频率控制
- AI处理选项配置

### 2. 智能抓取系统
- 基于RSS源的自动抓取
- 实时进度监控和日志
- 错误处理和重试机制
- 媒体文件自动下载
- 抓取历史记录

### 3. AI内容处理
- 异步AI处理队列
- 多种AI分析类型
- 处理状态监控
- 性能统计分析
- 错误处理和重试

### 4. 数据管理
- 实时数据库统计
- 数据导出和备份
- 安全的数据清空
- 媒体文件管理
- 存储空间监控

## 🔮 使用说明

### 启动系统
1. 运行 `npm install` 安装依赖
2. 运行 `npm run init-new-db` 初始化数据库
3. 运行 `npm run dev` 启动Next.js开发服务器
4. 访问 http://localhost:3000 开始使用

### RSS源管理
1. 访问"RSS源管理"页面
2. 点击"添加RSS源"添加新的RSS源
3. 配置RSS源的基本信息和字段映射
4. 测试RSS源确保配置正确
5. 启用RSS源开始抓取

### 抓取管理
1. 访问"抓取管理"页面
2. 点击"开始抓取"启动抓取任务
3. 实时监控抓取进度和日志
4. 查看抓取历史和统计信息
5. 管理AI处理队列

### AI处理
1. 在RSS源中启用AI处理
2. 启动AI处理器
3. 监控AI处理队列状态
4. 查看AI处理统计和结果

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**: 要使用完整功能，请确保同时运行前端开发服务器和所有后端API服务器。