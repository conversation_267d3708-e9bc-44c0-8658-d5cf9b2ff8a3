import { NextResponse } from 'next/server';
import { prisma } from '@/lib/server/database';

export async function POST() {
  try {
    let cleanupResults = {
      orphanedRecordsRemoved: 0,
      duplicateRecordsRemoved: 0,
      totalRecordsRemoved: 0
    };

    // 1. 删除孤立的AI处理记录（对应的文章已被删除）
    const existingArticleIds = await prisma.articles.findMany({ 
      select: { id: true } 
    }).then(articles => articles.map(a => a.id));

    const orphanedRecords = await prisma.ai_processing_queue.deleteMany({
      where: {
        article_id: {
          notIn: existingArticleIds
        }
      }
    });
    cleanupResults.orphanedRecordsRemoved = orphanedRecords.count;

    // 2. 处理重复的AI处理记录
    // 首先找到所有有重复记录的文章ID
    const duplicateGroups = await prisma.$queryRaw<{article_id: number, count: number}[]>`
      SELECT article_id, COUNT(*) as count 
      FROM ai_processing_queue 
      GROUP BY article_id 
      HAVING COUNT(*) > 1
    `;

    let duplicatesRemoved = 0;
    for (const group of duplicateGroups) {
      // 对于每个有重复记录的文章，保留最新的一条记录，删除其他的
      const records = await prisma.ai_processing_queue.findMany({
        where: { article_id: group.article_id },
        orderBy: { created_at: 'desc' }
      });

      if (records.length > 1) {
        // 保留第一条（最新的），删除其他的
        const recordsToDelete = records.slice(1);
        for (const record of recordsToDelete) {
          await prisma.ai_processing_queue.delete({
            where: { id: record.id }
          });
          duplicatesRemoved++;
        }
      }
    }
    cleanupResults.duplicateRecordsRemoved = duplicatesRemoved;
    cleanupResults.totalRecordsRemoved = cleanupResults.orphanedRecordsRemoved + cleanupResults.duplicateRecordsRemoved;

    // 3. 获取清理后的统计信息
    const [
      totalArticles,
      aiPending,
      aiProcessing,
      aiCompleted,
      aiFailed,
      aiTotal
    ] = await Promise.all([
      prisma.articles.count(),
      prisma.ai_processing_queue.count({ where: { status: 'pending' } }),
      prisma.ai_processing_queue.count({ where: { status: 'processing' } }),
      prisma.ai_processing_queue.count({ where: { status: 'completed' } }),
      prisma.ai_processing_queue.count({ where: { status: 'failed' } }),
      prisma.ai_processing_queue.count()
    ]);

    return NextResponse.json({
      success: true,
      message: '数据清理完成',
      cleanupResults,
      afterCleanup: {
        totalArticles,
        aiStats: {
          pending: aiPending,
          processing: aiProcessing,
          completed: aiCompleted,
          failed: aiFailed,
          total: aiTotal
        }
      }
    });
  } catch (error: any) {
    console.error('数据清理失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
