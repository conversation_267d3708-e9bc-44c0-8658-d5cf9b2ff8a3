const { PrismaClient } = require('@prisma/client');
const { createEmptyCard, fsrs, generatorParameters } = require('ts-fsrs');

const prisma = new PrismaClient();

// FSRS 参数配置
const FSRS_PARAMS = generatorParameters({
  enable_fuzz: true,
  enable_short_term: true,
  maximum_interval: 36500,
  request_retention: 0.9,
});

const fsrsInstance = fsrs(FSRS_PARAMS);

async function convertDataToFsrs() {
  try {
    console.log('=== 步骤 2: 数据转换到 FSRS 格式 ===\n');

    // 1. 检查需要转换的数据
    console.log('1. 检查需要转换的数据...');
    
    const totalRecords = await prisma.user_learning_records.count();
    const newRecords = await prisma.user_learning_records.count({
      where: { status: 'new' }
    });
    const learningRecords = await prisma.user_learning_records.count({
      where: { status: 'learning' }
    });
    const alreadyConverted = await prisma.user_learning_records.count({
      where: { fsrs_due: { not: null } }
    });

    console.log(`总记录数: ${totalRecords}`);
    console.log(`new 状态: ${newRecords}`);
    console.log(`learning 状态: ${learningRecords}`);
    console.log(`已有 FSRS 数据: ${alreadyConverted}`);

    if (alreadyConverted > 0) {
      console.log('⚠️  发现已有 FSRS 数据，跳过已转换的记录');
    }

    // 2. 转换 new 状态记录
    console.log('\n2. 转换 new 状态记录...');
    const newRecordsToConvert = await prisma.user_learning_records.findMany({
      where: { 
        status: 'new',
        fsrs_due: null // 只转换还没有 FSRS 数据的记录
      }
    });

    console.log(`需要转换的 new 记录: ${newRecordsToConvert.length}`);

    let convertedNewCount = 0;
    for (const record of newRecordsToConvert) {
      try {
        // 为新记录创建空的 FSRS 卡片
        const card = createEmptyCard(record.created_at);
        
        await prisma.user_learning_records.update({
          where: { id: record.id },
          data: {
            fsrs_due: card.due,
            fsrs_stability: card.stability,
            fsrs_difficulty: card.difficulty,
            fsrs_elapsed_days: card.elapsed_days,
            fsrs_scheduled_days: card.scheduled_days,
            fsrs_learning_steps: 0,
            fsrs_reps: card.reps,
            fsrs_lapses: card.lapses,
            fsrs_state: card.state.toString(),
            fsrs_last_review: card.last_review
          }
        });
        
        convertedNewCount++;
        
        if (convertedNewCount % 500 === 0) {
          console.log(`已转换 ${convertedNewCount} 条 new 记录...`);
        }
      } catch (error) {
        console.error(`转换记录 ${record.id} 失败:`, error.message);
      }
    }

    console.log(`✅ 转换了 ${convertedNewCount} 条 new 记录`);

    // 3. 转换 learning 状态记录
    console.log('\n3. 转换 learning 状态记录...');
    const learningRecordsToConvert = await prisma.user_learning_records.findMany({
      where: { 
        status: 'learning',
        fsrs_due: null // 只转换还没有 FSRS 数据的记录
      }
    });

    console.log(`需要转换的 learning 记录: ${learningRecordsToConvert.length}`);

    let convertedLearningCount = 0;
    for (const record of learningRecordsToConvert) {
      try {
        // 为学习中的记录创建基础的 FSRS 卡片
        const card = createEmptyCard(record.created_at);
        
        // 如果有旧的复习数据，尝试估算 FSRS 参数
        let fsrsData = {
          fsrs_due: card.due,
          fsrs_stability: card.stability,
          fsrs_difficulty: card.difficulty,
          fsrs_elapsed_days: card.elapsed_days,
          fsrs_scheduled_days: card.scheduled_days,
          fsrs_learning_steps: 0,
          fsrs_reps: card.reps,
          fsrs_lapses: card.lapses,
          fsrs_state: 'Learning',
          fsrs_last_review: card.last_review
        };

        // 如果有旧的复习数据，进行简单的转换
        if (record.repetitions && record.repetitions > 0) {
          fsrsData.fsrs_reps = record.repetitions;
          fsrsData.fsrs_state = 'Review';
          
          // 估算稳定性（基于旧的间隔）
          if (record.interval && record.interval > 0) {
            fsrsData.fsrs_stability = Math.max(1, record.interval);
            fsrsData.fsrs_scheduled_days = record.interval;
          }
          
          // 估算难度（基于旧的 ease_factor）
          if (record.ease_factor) {
            // ease_factor 通常在 1.3-3.0 之间，转换为 FSRS 难度 1-10
            fsrsData.fsrs_difficulty = Math.max(1, Math.min(10, 10 - (record.ease_factor - 1.3) * 4));
          }
          
          // 设置复习时间
          if (record.next_review_at) {
            fsrsData.fsrs_due = record.next_review_at;
          }
          
          if (record.last_reviewed_at) {
            fsrsData.fsrs_last_review = record.last_reviewed_at;
          }
        }
        
        await prisma.user_learning_records.update({
          where: { id: record.id },
          data: fsrsData
        });
        
        convertedLearningCount++;
      } catch (error) {
        console.error(`转换 learning 记录 ${record.id} 失败:`, error.message);
      }
    }

    console.log(`✅ 转换了 ${convertedLearningCount} 条 learning 记录`);

    console.log('\n=== 步骤 2 完成：数据转换成功 ===');
    console.log(`✅ 总共转换了 ${convertedNewCount + convertedLearningCount} 条记录`);
    console.log('✅ 所有旧数据保持不变');

  } catch (error) {
    console.error('数据转换失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

convertDataToFsrs();
