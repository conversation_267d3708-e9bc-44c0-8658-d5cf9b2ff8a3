
import { z } from 'zod';

export const MessageSchema = z.object({
  type: z.enum(['ai', 'user']).describe('The role of the speaker.'),
  content: z.string().describe('The text content of the message.'),
});

export const TutorChatInputSchema = z.object({
  history: z.array(MessageSchema).describe('The conversation history so far, excluding the latest message.'),
  message: z.string().describe('The latest message from the user.'),
  modelName: z.string().optional().describe('The AI model to use for the chat.'),
});
export type TutorChatInput = z.infer<typeof TutorChatInputSchema>;

export const TutorChatOutputSchema = z.object({
  response: z.string().describe("The AI tutor's response."),
});
export type TutorChatOutput = z.infer<typeof TutorChatOutputSchema>;
