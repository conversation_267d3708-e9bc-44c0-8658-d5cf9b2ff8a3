const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addApiKeyIntervals() {
  try {
    console.log('开始添加API Key间隔字段...');
    
    // 检查字段是否已存在
    const result = await prisma.$queryRaw`PRAGMA table_info(api_keys)`;
    const columns = result.map(col => col.name);
    
    if (!columns.includes('quota_reset_interval_minutes')) {
      console.log('添加 quota_reset_interval_minutes 字段...');
      await prisma.$executeRaw`ALTER TABLE api_keys ADD COLUMN quota_reset_interval_minutes INTEGER DEFAULT 60`;
    } else {
      console.log('quota_reset_interval_minutes 字段已存在');
    }
    
    if (!columns.includes('min_usage_interval_seconds')) {
      console.log('添加 min_usage_interval_seconds 字段...');
      await prisma.$executeRaw`ALTER TABLE api_keys ADD COLUMN min_usage_interval_seconds INTEGER DEFAULT 1`;
    } else {
      console.log('min_usage_interval_seconds 字段已存在');
    }
    
    console.log('API Key间隔字段添加完成！');
  } catch (error) {
    console.error('添加字段时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addApiKeyIntervals();
