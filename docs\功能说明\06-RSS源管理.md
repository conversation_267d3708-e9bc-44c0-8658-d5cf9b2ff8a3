# RSS源管理功能说明

## 1. 功能概述

### 1.1 主要功能
RSS源管理是系统数据来源的配置中心，负责管理新闻订阅源、配置数据提取规则、设置抓取参数等。支持灵活的字段映射和实时测试验证功能。

### 1.2 核心特性
- **RSS源配置**: 添加、编辑、删除RSS订阅源
- **字段映射**: 灵活配置数据提取规则
- **实时测试**: 验证RSS源有效性和数据提取
- **抓取控制**: 设置抓取频率和数量限制
- **AI处理配置**: 控制AI功能的启用状态
- **批量管理**: 支持批量导入和导出配置

### 1.3 适用场景
- 添加新的新闻数据源
- 调整现有源的配置参数
- 测试和验证数据提取规则
- 管理系统的数据获取策略

## 2. 使用指南

### 2.1 添加RSS源
1. 进入RSS源管理页面
2. 点击"添加RSS源"按钮
3. 填写基本信息（名称、URL、描述等）
4. 配置字段映射规则
5. 测试RSS源有效性
6. 保存配置

### 2.2 配置字段映射
1. 选择要编辑的RSS源
2. 进入字段映射配置界面
3. 为每个字段设置提取规则
4. 使用XPath或CSS选择器
5. 测试映射规则的效果
6. 保存映射配置

### 2.3 测试RSS源
1. 在RSS源列表中选择要测试的源
2. 点击"测试"按钮
3. 查看测试结果和提取的数据
4. 根据结果调整配置
5. 确认测试通过后启用源

### 2.4 管理抓取参数
1. 设置抓取频率（分钟为单位）
2. 配置最大文章数量
3. 启用或禁用AI处理功能
4. 设置分类和语言标签

## 3. 界面详解

### 3.1 RSS源管理主页面 (`/rss-manager`)

#### 页面布局
- **顶部**: 操作按钮和搜索栏
- **主体**: RSS源列表表格
- **右侧**: 源详情和配置面板

#### 操作按钮区域
| 按钮名称 | 功能 | 权限要求 | 说明 |
|----------|------|----------|------|
| 添加RSS源 | 创建新的RSS源 | 管理员 | 打开添加表单 |
| 批量导入 | 从文件导入配置 | 管理员 | 支持JSON/CSV格式 |
| 批量导出 | 导出当前配置 | 管理员 | 生成配置文件 |
| 全部测试 | 测试所有活跃源 | 管理员 | 批量验证功能 |

#### RSS源列表表格
| 列名 | 数据库字段 | 显示格式 | 说明 |
|------|------------|----------|------|
| 名称 | rss_sources.name | 文本链接 | 点击查看详情 |
| URL | rss_sources.url | 截断显示 | 悬停显示完整URL |
| 分类 | rss_sources.category | 标签形式 | 颜色区分不同分类 |
| 语言 | rss_sources.language | 国旗图标 | 支持的语言标识 |
| 状态 | rss_sources.is_active | 开关按钮 | 启用/禁用状态 |
| 最后抓取 | rss_sources.last_fetch_time | 相对时间 | "2小时前"格式 |
| 成功率 | rss_sources.success_rate | 百分比 | 抓取成功率统计 |
| 操作 | - | 按钮组 | 编辑/测试/删除 |

### 3.2 RSS源配置表单

#### 基本信息区域
| 字段名 | 数据库字段 | 类型 | 验证规则 | 说明 |
|--------|------------|------|----------|------|
| 源名称 | rss_sources.name | 文本 | 必填，1-100字符 | RSS源的显示名称 |
| RSS URL | rss_sources.url | URL | 必填，有效URL格式 | RSS订阅地址 |
| 描述 | rss_sources.description | 文本域 | 可选，最大500字符 | 源的详细说明 |
| 分类 | rss_sources.category | 选择 | 必填 | 新闻分类标签 |
| 语言 | rss_sources.language | 选择 | 必填 | 内容语言设置 |

#### 抓取配置区域
| 字段名 | 数据库字段 | 类型 | 默认值 | 说明 |
|--------|------------|------|--------|------|
| 最大文章数 | rss_sources.max_articles | 数字 | 10篇 | 每次抓取的文章上限 |
| 启用AI处理 | rss_sources.enable_ai_processing | 开关 | 开启 | 是否进行AI分析 |
| 内容选择器 | rss_sources.content_selector | 文本 | 可选 | 全文提取的CSS选择器 |

> **注意**: 抓取延迟配置已统一移至系统设置页面，通过"抓取延迟配置"进行全局管理。

### 3.3 字段映射配置

#### 标准RSS字段映射
| RSS字段 | 目标字段 | 数据库表.字段 | 映射类型 | 说明 |
|---------|----------|---------------|----------|------|
| title | 标题 | articles.title | 直接映射 | 文章标题 |
| link | 链接 | articles.url | 直接映射 | 文章URL |
| description | 描述 | articles.content | 直接映射 | 文章摘要 |
| pubDate | 发布时间 | articles.publish_time | 时间转换 | 发布时间戳 |
| category | 分类 | articles.category | 直接映射 | 文章分类 |

#### 自定义字段映射
| 字段名 | 数据库字段 | 配置项 | 说明 |
|--------|------------|--------|------|
| 字段名称 | rss_field_mappings.field_name | 必填 | 目标字段名 |
| XPath选择器 | rss_field_mappings.xpath_selector | 可选 | XPath提取规则 |
| CSS选择器 | rss_field_mappings.css_selector | 可选 | CSS选择器规则 |
| 属性名称 | rss_field_mappings.attribute_name | 可选 | 提取的属性名 |
| 默认值 | rss_field_mappings.default_value | 可选 | 提取失败时的默认值 |
| 是否必需 | rss_field_mappings.is_required | 开关 | 是否为必需字段 |
| 转换规则 | rss_field_mappings.transform_rule | 可选 | 数据转换规则 |

### 3.4 测试结果界面

#### 测试状态显示
| 状态 | 图标 | 颜色 | 说明 |
|------|------|------|------|
| 测试中 | 🔄 | 蓝色 | 正在进行测试 |
| 成功 | ✅ | 绿色 | 测试通过 |
| 失败 | ❌ | 红色 | 测试失败 |
| 警告 | ⚠️ | 黄色 | 部分字段提取失败 |

#### 测试结果详情
| 信息类型 | 显示内容 | 数据来源 | 说明 |
|----------|----------|----------|------|
| 响应状态 | HTTP状态码 | 网络请求结果 | 200表示成功 |
| 响应时间 | 毫秒数 | 请求耗时统计 | 性能指标 |
| 文章数量 | 提取的条目数 | RSS解析结果 | 可用文章数量 |
| 字段完整性 | 各字段提取状态 | 映射规则验证 | 数据质量评估 |
| 示例数据 | 前3条文章预览 | 实际提取内容 | 数据格式验证 |

## 4. 技术实现

### 4.1 核心代码文件

#### RSS源管理组件
- **文件**: `src/components/RSSSourceManager.tsx`
- **功能**: RSS源管理主界面
- **主要功能**:
  - RSS源列表展示
  - 配置表单处理
  - 测试功能集成

#### RSS解析服务
- **文件**: `src/lib/server/rss-parser.ts`
- **功能**: RSS内容解析和数据提取
- **主要功能**:
  - RSS XML解析
  - 字段映射应用
  - 数据格式转换

#### 字段映射引擎
- **文件**: `src/lib/server/field-mapper.ts`
- **功能**: 灵活的数据提取规则引擎
- **主要功能**:
  - XPath选择器执行
  - CSS选择器解析
  - 数据转换和验证

#### 数据库管理服务
- **文件**: `src/lib/server/database.ts`
- **相关方法**:
  - `getAllRSSSources()`: 获取RSS源列表
  - `createRSSSource(data)`: 创建RSS源
  - `updateRSSSource(id, data)`: 更新RSS源
  - `deleteRSSSource(id)`: 删除RSS源
  - `testRSSSource(url)`: 测试RSS源

### 4.2 数据库表结构

#### rss_sources表（RSS源主表）
```sql
CREATE TABLE rss_sources (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  url TEXT UNIQUE NOT NULL,
  description TEXT,
  category TEXT,
  language TEXT DEFAULT 'ja',
  is_active BOOLEAN DEFAULT true,
  max_articles INTEGER DEFAULT 10,
  enable_ai_processing BOOLEAN DEFAULT true,
  content_selector TEXT, -- CSS选择器
  last_fetch_time DATETIME,
  last_fetch_count INTEGER DEFAULT 0,
  total_fetched INTEGER DEFAULT 0,
  success_rate REAL DEFAULT 0.0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### rss_field_mappings表（字段映射配置）
```sql
CREATE TABLE rss_field_mappings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER NOT NULL,
  field_name TEXT NOT NULL, -- 目标字段名
  xpath_selector TEXT, -- XPath选择器
  css_selector TEXT, -- CSS选择器
  attribute_name TEXT, -- 属性名
  default_value TEXT, -- 默认值
  is_required BOOLEAN DEFAULT false,
  transform_rule TEXT, -- 转换规则JSON
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources(id) ON DELETE CASCADE
);
```

#### rss_test_results表（测试结果记录）
```sql
CREATE TABLE rss_test_results (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  rss_source_id INTEGER NOT NULL,
  test_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  status TEXT NOT NULL, -- 'success', 'failed', 'warning'
  response_time INTEGER, -- 毫秒
  articles_found INTEGER DEFAULT 0,
  error_message TEXT,
  sample_data TEXT, -- JSON格式的示例数据
  FOREIGN KEY (rss_source_id) REFERENCES rss_sources(id)
);
```

### 4.3 RSS解析流程

#### 解析器架构
```typescript
class RSSParser {
  async parseRSS(url: string, mappings: FieldMapping[]): Promise<Article[]> {
    // 1. 获取RSS内容
    const response = await fetch(url);
    const xmlContent = await response.text();
    
    // 2. 解析XML
    const doc = new DOMParser().parseFromString(xmlContent, 'text/xml');
    const items = doc.querySelectorAll('item');
    
    // 3. 应用字段映射
    const articles = [];
    for (const item of items) {
      const article = await this.applyMappings(item, mappings);
      articles.push(article);
    }
    
    return articles;
  }
  
  private async applyMappings(item: Element, mappings: FieldMapping[]): Promise<Partial<Article>> {
    const result: any = {};
    
    for (const mapping of mappings) {
      try {
        let value = this.extractValue(item, mapping);
        value = this.transformValue(value, mapping.transform_rule);
        result[mapping.field_name] = value;
      } catch (error) {
        if (mapping.is_required) {
          throw new Error(`Required field ${mapping.field_name} extraction failed`);
        }
        result[mapping.field_name] = mapping.default_value;
      }
    }
    
    return result;
  }
}
```

#### 字段提取逻辑
```typescript
private extractValue(element: Element, mapping: FieldMapping): string {
  let value: string | null = null;
  
  // 优先使用XPath选择器
  if (mapping.xpath_selector) {
    const result = document.evaluate(
      mapping.xpath_selector,
      element,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    value = result.singleNodeValue?.textContent || null;
  }
  
  // 备用CSS选择器
  if (!value && mapping.css_selector) {
    const selected = element.querySelector(mapping.css_selector);
    if (mapping.attribute_name) {
      value = selected?.getAttribute(mapping.attribute_name) || null;
    } else {
      value = selected?.textContent || null;
    }
  }
  
  // 直接从元素获取
  if (!value) {
    value = element.querySelector(mapping.field_name)?.textContent || null;
  }
  
  return value || mapping.default_value || '';
}
```

### 4.4 API接口

#### 获取RSS源列表
- **路径**: `GET /api/rss/sources`
- **返回**: RSS源列表，包含统计信息

#### 创建RSS源
- **路径**: `POST /api/rss/sources`
- **参数**: RSS源配置对象
- **返回**: 创建结果和源ID

#### 更新RSS源
- **路径**: `PUT /api/rss/sources/[id]`
- **参数**: 更新的配置数据
- **返回**: 更新结果

#### 删除RSS源
- **路径**: `DELETE /api/rss/sources/[id]`
- **返回**: 删除结果

#### 测试RSS源
- **路径**: `POST /api/rss/sources/[id]/test`
- **返回**: 测试结果和示例数据

#### 获取字段映射
- **路径**: `GET /api/rss/sources/[id]/mappings`
- **返回**: 字段映射配置列表

#### 更新字段映射
- **路径**: `PUT /api/rss/sources/[id]/mappings`
- **参数**: 映射配置数组
- **返回**: 更新结果

## 5. 配置说明

### 5.1 默认配置

#### RSS源默认参数
```javascript
const DEFAULT_RSS_CONFIG = {
  max_articles: 10,
  enable_ai_processing: true,
  language: 'ja',
  category: 'general'
};
```

#### 抓取延迟配置
抓取延迟现在通过系统设置统一管理，可在设置页面的"抓取延迟配置"部分进行调整：
- RSS源间延迟：处理完一个RSS源后的等待时间
- 文章间延迟：处理完一篇文章后的等待时间
- 智能延迟范围：最小和最大请求延迟时间

#### 标准字段映射
```javascript
const STANDARD_MAPPINGS = [
  { field_name: 'title', css_selector: 'title' },
  { field_name: 'url', css_selector: 'link' },
  { field_name: 'content', css_selector: 'description' },
  { field_name: 'publish_time', css_selector: 'pubDate' },
  { field_name: 'category', css_selector: 'category' }
];
```

### 5.2 验证规则

#### URL验证
- 必须是有效的HTTP/HTTPS URL
- 响应Content-Type必须包含XML
- 必须包含有效的RSS结构

#### 字段映射验证
- XPath和CSS选择器语法检查
- 必需字段的存在性验证
- 数据类型转换验证

## 6. 故障排除

### 6.1 常见问题

#### RSS源测试失败
**症状**: 测试时显示连接错误或解析失败
**解决方案**:
1. 检查RSS URL是否可访问
2. 验证RSS XML格式是否正确
3. 检查网络防火墙设置
4. 确认RSS源是否需要认证

#### 字段映射不生效
**症状**: 提取的数据为空或不正确
**解决方案**:
1. 验证选择器语法是否正确
2. 检查RSS XML结构是否变化
3. 测试XPath/CSS选择器
4. 确认属性名称是否正确

#### 抓取频率过高导致被封
**症状**: RSS源返回403或429错误
**解决方案**:
1. 增加抓取间隔时间
2. 添加User-Agent头部
3. 实现请求限流机制
4. 联系RSS源提供方

### 6.2 性能优化

#### 并发控制
```typescript
// 限制并发请求数量
const semaphore = new Semaphore(5);

async function testAllSources(sources: RSSSource[]) {
  const results = await Promise.allSettled(
    sources.map(source => 
      semaphore.acquire().then(async (release) => {
        try {
          return await testRSSSource(source);
        } finally {
          release();
        }
      })
    )
  );
  return results;
}
```

#### 缓存策略
```typescript
// RSS内容缓存
const cacheKey = `rss_${sourceId}_${Math.floor(Date.now() / (5 * 60 * 1000))}`;
const cached = await redis.get(cacheKey);
if (cached) {
  return JSON.parse(cached);
}

const result = await parseRSS(source);
await redis.setex(cacheKey, 300, JSON.stringify(result));
return result;
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
