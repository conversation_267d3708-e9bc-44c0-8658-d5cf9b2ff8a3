import { NextResponse } from 'next/server';
import { analyzeArticle } from '@/ai/flows/analyze-article-flow';
import { ArticleAnalysisInput } from '@/ai/schemas/analyze-article-schema';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // 验证输入
    if (!body.title || typeof body.title !== 'string') {
      return NextResponse.json(
        { error: '标题是必需的' },
        { status: 400 }
      );
    }

    // 构建分析输入
    const input: ArticleAnalysisInput = {
      title: body.title,
      subtitle: body.subtitle || '',
      content: body.content || '',
      modelName: body.modelName || 'googleai/gemini-2.0-flash'
    };

    console.log('开始AI文章分析测试:', {
      title: input.title,
      hasSubtitle: !!input.subtitle,
      contentLength: input.content.length,
      modelName: input.modelName
    });

    const startTime = Date.now();
    
    // 调用AI分析
    const result = await analyzeArticle(input);
    
    const processingTime = Date.now() - startTime;
    
    console.log('AI文章分析测试完成:', {
      processingTime,
      hasResult: !!result,
      vocabularyCount: result.vocabulary?.length || 0,
      grammarCount: result.grammar?.length || 0
    });

    // 添加测试元数据
    const response = {
      ...result,
      _testMetadata: {
        processingTime,
        timestamp: new Date().toISOString(),
        modelUsed: input.modelName,
        inputStats: {
          titleLength: input.title.length,
          subtitleLength: input.subtitle?.length || 0,
          contentLength: input.content.length
        },
        outputStats: {
          vocabularyCount: result.vocabulary?.length || 0,
          grammarCount: result.grammar?.length || 0,
          hasTitleWithFurigana: !!result.titleWithFurigana,
          hasContentWithFurigana: !!result.contentWithFurigana,
          hasSubtitleWithFurigana: !!result.subtitleWithFurigana
        }
      }
    };

    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('AI文章分析测试失败:', error);
    
    return NextResponse.json(
      { 
        error: '分析失败', 
        details: error.message,
        _testMetadata: {
          timestamp: new Date().toISOString(),
          errorType: error.constructor.name
        }
      },
      { status: 500 }
    );
  }
}
