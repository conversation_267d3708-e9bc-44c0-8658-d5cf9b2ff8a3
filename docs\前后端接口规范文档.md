# 前后端接口规范文档

## 文档概述

本文档详细描述了NHK日语学习平台前后端之间的完整接口规范，为后端Python重写提供准确的接口定义。所有接口均采用RESTful API设计，使用JSON格式进行数据交换。

## 服务器架构

### API服务器配置
- **Next.js主应用**: `http://localhost:3000` (集成RSS测试、数据库操作等功能)
- **抓取管理API服务器**: `http://localhost:3002` (专门负责数据抓取管理)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **CORS**: 支持跨域请求

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息",
  "details": {}
}
```

## Next.js主应用 (端口3000)

### 基础信息
- **基础URL**: `http://localhost:3000/api`
- **用途**: RSS源测试、用户管理、学习功能等
- **架构**: Next.js全栈应用，集成前后端功能

### 1. RSS源测试

**接口**: `POST /rss/test`

**描述**: 测试RSS源的有效性和内容解析

**请求参数**:
```json
{
  "url": "https://example.com/rss.xml",
  "fieldMappings": {
    "title": "title",
    "link": "link",
    "description": "description"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "itemCount": 15,
  "sampleItems": [...],
  "feedTitle": "RSS源标题",
  "feedDescription": "RSS源描述",
  "message": "成功解析 15 个RSS条目"
}
```

**错误响应**:
```json
{
  "status": "error",
  "message": "服务器内部错误"
}
```

### 2. 测试单个RSS源

**接口**: `POST /rss/test`

**描述**: 测试单个RSS源的有效性和内容解析

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
  "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
  "fieldMappings": {
    "title": {
      "xpath_selector": "//item/title/text()",
      "css_selector": "item > title",
      "attribute_name": null,
      "is_required": true,
      "transform_rule": {
        "type": "text",
        "trim": true,
        "maxLength": 200
      }
    },
    "link": {
      "xpath_selector": "//item/link/text()",
      "css_selector": "item > link",
      "attribute_name": null,
      "is_required": true,
      "transform_rule": {
        "type": "url",
        "makeAbsolute": true
      }
    }
  }
}
```

**参数说明**:
- `url` (string, required): RSS源URL
- `fieldMappings` (object, optional): 字段映射配置

**成功响应**:
```json
{
  "success": true,
  "itemCount": 15,
  "sampleItems": [
    {
      "title": "新技術の開発について",
      "link": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
      "description": "最新の技術開発に関するニュース...",
      "pub_date": "2024-12-01T10:00:00Z",
      "guid": "k10001234567000",
      "image_url": "https://www3.nhk.or.jp/news/image.jpg"
    }
  ],
  "message": "✅ RSS源测试成功！解析到 15 个条目",
  "details": {
    "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
    "responseTime": "2.3s",
    "feedTitle": "NHKニュース",
    "feedDescription": "NHKのニュースサイト",
    "encoding": "UTF-8",
    "contentType": "application/rss+xml"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "error": "RSS_FETCH_FAILED",
  "message": "❌ RSS源测试失败: 连接超时",
  "details": {
    "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
    "responseTime": "15.0s",
    "errorType": "TIMEOUT"
  }
}
```

### 3. 批量测试RSS源

**接口**: `POST /rss/test-batch`

**描述**: 批量测试多个RSS源

**请求参数**:
```json
{
  "urls": [
    "https://www3.nhk.or.jp/rss/news/cat0.xml",
    "https://www3.nhk.or.jp/rss/news/cat5.xml"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "results": [
    {
      "url": "https://www3.nhk.or.jp/rss/news/cat0.xml",
      "success": true,
      "itemCount": 15,
      "responseTime": "2.1s"
    },
    {
      "url": "https://www3.nhk.or.jp/rss/news/cat5.xml",
      "success": false,
      "error": "连接超时",
      "responseTime": "15.0s"
    }
  ],
  "message": "批量测试完成，共测试 2 个RSS源"
}
```

## 抓取管理API服务器 (端口3002)

### 基础信息
- **基础URL**: `http://localhost:3002/api`
- **用途**: 数据抓取控制和管理
- **数据库**: SQLite
- **说明**: 数据库统计、AI处理统计等功能已集成到Next.js主应用中

### 3. 获取AI处理统计

**接口**: `GET /ai/stats`

**响应示例**:
```json
{
  "pending": 5,
  "processing": 2,
  "completed": 156,
  "failed": 3,
  "totalProcessed": 166
}
```

## 文章管理API

### 4. 获取文章列表

**接口**: `GET /articles`

**查询参数**:
- `level` (string, optional): 难度级别 (N5, N4, N3, N2, N1)
- `category` (string, optional): 文章分类
- `search` (string, optional): 搜索关键词
- `limit` (integer, optional): 返回数量限制 (默认50)

**请求示例**:
```
GET /articles?level=N3&category=科技&limit=20&search=技術
```

**响应示例**:
```json
[
  {
    "id": 1,
    "title": "新しい技術の開発について",
    "subtitle": "最新技術の動向を解説",
    "content": "技術開発に関する詳細な内容...",
    "url": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
    "category": "科技",
    "level": "N3",
    "readTime": 5,
    "vocabularyCount": 25,
    "grammarPoints": 8,
    "publishTime": "2024-12-01",
    "publish_time": "2024-12-01T10:00:00Z",
    "fetch_time": "2024-12-01T11:00:00Z",
    "created_at": "2024-12-01T11:00:00Z",
    "rss_source_name": "NHK科技新闻",
    "rss_source_id": 1,
    "ai_processing_status": "completed",
    "processing_status": "completed",
    "featured_image_url": "https://example.com/image.jpg",
    "featured_image_path": "/media/images/article_123.jpg",
    "video_url": null,
    "video_path": null,
    "audio_url": null,
    "audio_path": null
  }
]
```

### 5. 获取文章详情

**接口**: `GET /articles/:id`

**路径参数**:
- `id` (integer, required): 文章ID

**响应示例**:
```json
{
  "id": 1,
  "title": "新しい技術の開発について",
  "subtitle": "最新技術の動向を解説",
  "content": "详细的文章内容...",
  "content_html": "<p>原始HTML内容...</p>",
  "url": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
  "category": "科技",
  "level": "N3",
  "readTime": 5,
  "vocabularyCount": 25,
  "grammarPoints": 8,
  "publishTime": "2024-12-01",
  "rss_source_name": "NHK科技新闻",
  "ai_processing_status": "completed",
  "vocabulary": [
    {
      "id": 1,
      "word": "技術",
      "reading": "ぎじゅつ",
      "meaning_zh": "技术",
      "meaning_en": "technology",
      "part_of_speech": "名词",
      "jlpt_level": "N3",
      "context": "新しい技術の開発",
      "is_key_vocabulary": true,
      "extraction_confidence": 0.95
    }
  ],
  "grammarPoints": [
    {
      "id": 1,
      "pattern": "について",
      "name": "について",
      "meaning_zh": "关于...",
      "meaning_en": "about, concerning",
      "explanation": "表示话题或对象的助词",
      "jlpt_level": "N4",
      "example_sentence": "技術について話す",
      "context": "技術の開発について",
      "extraction_confidence": 0.92
    }
  ],
  "translation": "关于新技术开发的中文翻译内容..."
}
```

## 词汇管理API

### 6. 获取词汇列表

**接口**: `GET /vocabulary`

**查询参数**:
- `level` (string, optional): JLPT级别 (N5, N4, N3, N2, N1)
- `status` (string, optional): 学习状态 (new, learning, mastered, difficult)
- `search` (string, optional): 搜索关键词

**响应示例**:
```json
[
  {
    "id": 1,
    "word": "技術",
    "reading": "ぎじゅつ",
    "meaning_zh": "技术",
    "meaning_en": "technology",
    "part_of_speech": "名词",
    "jlpt_level": "N3",
    "frequency_rank": 1250,
    "difficulty_score": 3.2,
    "extraction_method": "ai",
    "level": "N3",
    "meaning": "技术",
    "status": "learning",
    "article_count": 5,
    "review_count": 3,
    "addedDate": "2024-11-15",
    "created_at": "2024-11-15T10:00:00Z",
    "examples": [
      "新しい技術 - 新技术",
      "技術者 - 技术人员",
      "技術について - 关于技术"
    ]
  }
]
```

### 7. 获取词汇统计

**接口**: `GET /vocabulary/stats`

**响应示例**:
```json
{
  "total": 2847,
  "byLevel": {
    "N5": 456,
    "N4": 623,
    "N3": 789,
    "N2": 567,
    "N1": 412
  },
  "mastered": 234,
  "learning": 567,
  "difficult": 89,
  "new": 1957
}
```

### 8. 添加词汇

**接口**: `POST /vocabulary`

**请求参数**:
```json
{
  "word": "技術",
  "reading": "ぎじゅつ",
  "meaning_zh": "技术",
  "meaning_en": "technology",
  "part_of_speech": "名词",
  "jlpt_level": "N3",
  "difficulty_score": 3.2,
  "extraction_method": "manual"
}
```

**响应示例**:
```json
{
  "success": true,
  "id": 123,
  "message": "词汇添加成功"
}
```

### 9. 更新词汇状态

**接口**: `PUT /vocabulary/:id/status`

**路径参数**:
- `id` (integer, required): 词汇ID

**请求参数**:
```json
{
  "status": "mastered"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "词汇状态更新成功"
}
```

## 学习统计API

### 10. 获取学习统计

**接口**: `GET /learning/stats`

**响应示例**:
```json
{
  "continuousDays": 15,
  "totalVocabulary": 2847,
  "totalArticles": 156,
  "masteredItems": 234,
  "readArticles": 45,
  "averageLevel": 3,
  "weeklyActivity": [45, 60, 30, 75, 20, 0, 40],
  "totalStudyTime": 270
}
```

### 11. 获取用户进度

**接口**: `GET /learning/progress`

**响应示例**:
```json
{
  "vocabularyProgress": {
    "N5": {
      "total": 456,
      "mastered": 234,
      "percentage": 51
    },
    "N4": {
      "total": 623,
      "mastered": 156,
      "percentage": 25
    }
  },
  "articleProgress": {
    "total_articles": 156,
    "read_articles": 45
  }
}
```

### 12. 更新学习进度

**接口**: `POST /learning/progress`

**请求参数**:
```json
{
  "article_id": 1,
  "vocabulary_id": 123,
  "grammar_point_id": 45,
  "record_type": "vocabulary",
  "status": "mastered",
  "proficiency_level": 5
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "学习进度更新成功"
}
```

## 抓取控制API

### 13. 启动抓取任务

**接口**: `POST /scraping/start`

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "sessionId": "scrape_1701432000_abc123",
  "message": "抓取任务已启动",
  "totalExpected": 50
}
```

### 14. 停止抓取任务

**接口**: `POST /scraping/stop`

**请求参数**:
```json
{
  "sessionId": "scrape_1701432000_abc123"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "抓取任务已停止"
}
```

### 15. 获取抓取进度

**接口**: `GET /scraping/progress/:sessionId`

**路径参数**:
- `sessionId` (string, required): 抓取会话ID

**响应示例**:
```json
{
  "isRunning": true,
  "processedArticles": 25,
  "successCount": 23,
  "failCount": 2,
  "aiProcessingScheduled": 23,
  "currentStep": "处理中 (25/50)",
  "currentUrl": "https://www3.nhk.or.jp/news/html/20241201/k10001234567000.html",
  "logs": [
    "[12:00:15] 开始抓取任务",
    "[12:00:16] 正在处理第 1 篇文章...",
    "[12:00:18] 文章处理成功: 新技術について"
  ]
}
```

### 16. 获取抓取会话历史

**接口**: `GET /scraping/sessions`

**响应示例**:
```json
[
  {
    "id": "scrape_1701432000_abc123",
    "startTime": "2024-12-01T12:00:00Z",
    "endTime": "2024-12-01T12:15:00Z",
    "status": "completed",
    "totalSources": 3,
    "totalArticles": 50,
    "successfulArticles": 47,
    "failedArticles": 3,
    "aiProcessingScheduled": 47
  }
]
```

## AI处理API

### 17. 启动AI处理器

**接口**: `POST /ai/start`

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理器已启动"
}
```

### 18. 停止AI处理器

**接口**: `POST /ai/stop`

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理器已停止"
}
```

### 19. 清空AI处理队列

**接口**: `DELETE /ai/queue/clear`

**响应示例**:
```json
{
  "success": true,
  "message": "AI处理队列已清空"
}
```

## RSS源管理API

### 20. 获取RSS源列表

**接口**: `GET /rss/sources`

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "NHK科技新闻",
    "url": "https://www3.nhk.or.jp/rss/news/cat5.xml",
    "description": "NHK官方科技新闻RSS源",
    "category": "科技",
    "language": "ja",
    "is_active": true,
    "max_articles": 15,
    "enable_ai_processing": true,
    "content_selector": ".article-body, .content-body, article, .news-detail-content",
    "last_fetch_time": "2024-12-01T11:00:00Z",
    "last_fetch_count": 12,
    "total_fetched": 156,
    "success_rate": 0.95,
    "created_at": "2024-11-01T10:00:00Z",
    "updated_at": "2024-12-01T11:00:00Z"
  }
]
```

### 21. 添加RSS源

**接口**: `POST /rss/sources`

**请求参数**:
```json
{
  "name": "新RSS源",
  "url": "https://example.com/rss.xml",
  "description": "RSS源描述",
  "category": "科技",
  "language": "ja",
  "max_articles": 20,
  "enable_ai_processing": true,
  "content_selector": ".article-body, .content-body"
}
```

**注意**: 抓取延迟配置已移至系统设置，不再在RSS源级别配置。

**响应示例**:
```json
{
  "success": true,
  "sourceId": 2,
  "message": "RSS源添加成功"
}
```

### 22. 更新RSS源

**接口**: `PUT /rss/sources/:id`

**路径参数**:
- `id` (integer, required): RSS源ID

**请求参数**: 同添加RSS源

**响应示例**:
```json
{
  "success": true,
  "message": "RSS源更新成功"
}
```

### 23. 删除RSS源

**接口**: `DELETE /rss/sources/:id`

**路径参数**:
- `id` (integer, required): RSS源ID

**响应示例**:
```json
{
  "success": true,
  "message": "RSS源删除成功"
}
```

## 数据管理API

### 24. 清空数据库

**接口**: `DELETE /database/clear`

**描述**: 清空所有数据库内容（危险操作）

**响应示例**:
```json
{
  "success": true,
  "message": "数据库已清空"
}
```

### 25. 导出数据

**接口**: `GET /database/export`

**响应示例**:
```json
{
  "exportTime": "2024-12-01T12:00:00Z",
  "version": "2.0.0",
  "data": {
    "articles": [...],
    "vocabulary": [...],
    "grammar_points": [...],
    "rss_sources": [...]
  }
}
```

## 错误处理规范

### HTTP状态码
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
- `INVALID_PARAMS`: 参数验证失败
- `RSS_FETCH_FAILED`: RSS源获取失败
- `DATABASE_ERROR`: 数据库操作失败
- `AI_PROCESSING_ERROR`: AI处理失败
- `SCRAPING_IN_PROGRESS`: 抓取任务正在进行中
- `RESOURCE_NOT_FOUND`: 资源不存在
- `DUPLICATE_RESOURCE`: 资源重复

### 错误响应格式
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "用户友好的错误信息",
  "details": {
    "field": "具体的错误字段",
    "code": "详细错误代码",
    "timestamp": "2024-12-01T12:00:00Z"
  }
}
```

## 数据类型定义

### 文章对象 (Article)
```typescript
interface Article {
  id: number;
  rss_source_id: number;
  guid: string;
  title: string;
  subtitle?: string;
  content: string;
  content_html?: string;
  url: string;
  category: string;
  tags?: string[];
  publish_time?: string;
  fetch_time: string;
  featured_image_url?: string;
  featured_image_path?: string;
  video_url?: string;
  video_path?: string;
  audio_url?: string;
  audio_path?: string;
  ai_processing_status: 'pending' | 'processing' | 'completed' | 'failed' | 'disabled';
  ai_processed_at?: string;
  ai_processing_error?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  
  // 计算字段
  level?: string;
  readTime?: number;
  vocabularyCount?: number;
  grammarPoints?: number;
  publishTime?: string;
  rss_source_name?: string;
}
```

### 词汇对象 (Vocabulary)
```typescript
interface Vocabulary {
  id: number;
  word: string;
  reading?: string;
  meaning_zh?: string;
  meaning_en?: string;
  part_of_speech?: string;
  jlpt_level?: string;
  frequency_rank?: number;
  difficulty_score?: number;
  extraction_method: 'ai' | 'manual';
  created_at: string;
  
  // 关联信息
  context?: string;
  is_key_vocabulary?: boolean;
  extraction_confidence?: number;
  
  // 学习状态
  status?: 'new' | 'learning' | 'mastered' | 'difficult';
  article_count?: number;
  review_count?: number;
  addedDate?: string;
  examples?: string[];
}
```

### RSS源对象 (RSSSource)
```typescript
interface RSSSource {
  id: number;
  name: string;
  url: string;
  description?: string;
  category: string;
  language: string;
  is_active: boolean;
  max_articles: number;
  enable_ai_processing: boolean;
  fetch_interval: number;
  content_selector?: string;
  last_fetch_time?: string;
  last_fetch_count: number;
  total_fetched: number;
  success_rate: number;
  created_at: string;
  updated_at: string;
}
```

### 语法点对象 (GrammarPoint)
```typescript
interface GrammarPoint {
  id: number;
  pattern: string;
  name?: string;
  meaning_zh?: string;
  meaning_en?: string;
  explanation?: string;
  jlpt_level?: string;
  difficulty_score?: number;
  usage_frequency: number;
  extraction_method: 'ai' | 'manual';
  created_at: string;
}
```

## 前端调用示例

### JavaScript/TypeScript 客户端示例

```typescript
class APIClient {
  private baseUrl: string;
  
  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }
  
  async request(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    return await response.json();
  }
  
  // 获取文章列表
  async getArticles(filters: {
    level?: string;
    category?: string;
    search?: string;
    limit?: number;
  } = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value.toString());
    });
    
    const queryString = params.toString();
    const endpoint = queryString ? `/articles?${queryString}` : '/articles';
    
    return await this.request(endpoint);
  }
  
  // 测试RSS源
  async testRSSSource(url: string, fieldMappings?: any) {
    return await this.request('/rss/test', {
      method: 'POST',
      body: JSON.stringify({ url, fieldMappings })
    });
  }
  
  // 启动抓取任务
  async startScraping() {
    return await this.request('/scraping/start', {
      method: 'POST'
    });
  }
}

// 使用示例
const rssApi = new APIClient('http://localhost:3001/api');
const scrapingApi = new APIClient('http://localhost:3002/api');

// 测试RSS源
const testResult = await rssApi.testRSSSource('https://example.com/rss.xml');

// 获取文章
const articles = await scrapingApi.getArticles({
  level: 'N3',
  category: '科技',
  limit: 20
});

// 启动抓取
const scrapingResult = await scrapingApi.startScraping();
```

### Python 客户端示例

```python
import requests
import json
from typing import Dict, List, Optional

class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def request(self, endpoint: str, method: str = 'GET', data: Dict = None, params: Dict = None):
        url = f"{self.base_url}{endpoint}"
        
        response = self.session.request(
            method=method,
            url=url,
            json=data,
            params=params
        )
        
        response.raise_for_status()
        return response.json()
    
    def get_articles(self, level: str = None, category: str = None, 
                    search: str = None, limit: int = None) -> List[Dict]:
        """获取文章列表"""
        params = {}
        if level: params['level'] = level
        if category: params['category'] = category
        if search: params['search'] = search
        if limit: params['limit'] = limit
        
        return self.request('/articles', params=params)
    
    def test_rss_source(self, url: str, field_mappings: Dict = None) -> Dict:
        """测试RSS源"""
        data = {'url': url}
        if field_mappings:
            data['fieldMappings'] = field_mappings
            
        return self.request('/rss/test', method='POST', data=data)
    
    def start_scraping(self) -> Dict:
        """启动抓取任务"""
        return self.request('/scraping/start', method='POST')

# 使用示例
rss_api = APIClient('http://localhost:3001/api')
scraping_api = APIClient('http://localhost:3002/api')

# 测试RSS源
test_result = rss_api.test_rss_source('https://example.com/rss.xml')
print(f"RSS测试结果: {test_result}")

# 获取文章
articles = scraping_api.get_articles(level='N3', category='科技', limit=20)
print(f"找到 {len(articles)} 篇文章")

# 启动抓取
scraping_result = scraping_api.start_scraping()
print(f"抓取任务启动: {scraping_result}")
```

## 部署和配置说明

### 环境变量
```bash
# 服务器配置
RSS_API_PORT=3001
SCRAPING_API_PORT=3002

# 数据库配置
DATABASE_PATH=./data/nhk_news_new.db

# 媒体文件配置
MEDIA_PATH=./public/media

# CORS配置
CORS_ORIGIN=http://localhost:5173

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/api.log
```

### 数据库连接
```python
# Python SQLite连接示例
import sqlite3
from contextlib import contextmanager

@contextmanager
def get_db_connection():
    conn = sqlite3.connect('./data/nhk_news_new.db')
    conn.row_factory = sqlite3.Row  # 启用字典式访问
    try:
        yield conn
    finally:
        conn.close()

# 使用示例
with get_db_connection() as conn:
    cursor = conn.execute('SELECT * FROM articles LIMIT 10')
    articles = [dict(row) for row in cursor.fetchall()]
```

## 注意事项

### 1. 数据一致性
- 所有时间字段使用ISO 8601格式 (`YYYY-MM-DDTHH:mm:ss.sssZ`)
- 布尔值使用 `true`/`false` (JSON) 或 `1`/`0` (数据库)
- 文本编码统一使用UTF-8

### 2. 性能考虑
- 文章列表API支持分页，建议单次请求不超过100条
- 大文件上传需要分块处理
- 长时间运行的任务（如抓取）使用异步处理

### 3. 安全要求
- 所有用户输入需要验证和清理
- SQL查询使用参数化查询防止注入
- 文件上传需要类型和大小限制

### 4. 错误处理
- 网络错误需要重试机制
- 数据库操作需要事务支持
- 关键操作需要详细日志记录

这份接口文档为Python后端重写提供了完整的规范，确保前后端接口的一致性和兼容性。