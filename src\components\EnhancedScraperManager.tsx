
'use client';
import React, { useState, useEffect, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Play, 
  Pause, 
  RefreshCw, 
  Database, 
  Download, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  FileText, 
  Image, 
  Video,
  BarChart3,
  Settings,
  Trash2,
  Eye,
  Bot,
  Zap,
  List,
  Activity,
  Loader,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Wifi,
  WifiOff,
  BrainCircuit,
  Film
} from 'lucide-react';
import {
  startScrapingAction,
  stopScrapingAction,
  getScrapingProgressAction,
  getCurrentScrapingStatusAction,
  clearScrapedDataAction,
  startAIProcessorAction,
  stopAIProcessorAction,
  clearAIQueueAction,
  exportDataAction,
  getAIProcessingLogsAction,
  getAIProcessorStatusAction,
  getVideosForManagementAction,
  startVideoDownloadAction,
  setUseLocalVideoAction,
  getVideoDownloadLogsAction,
  getArticlesForAIManagementAction,
  scheduleAIAnalysisAction,
  getAllUnprocessedArticleIdsAction,
  createDatabaseBackupAction,
  getBackupFilesAction,
  deleteBackupFileAction,
  updateBackupSettingsAction,
  cleanupOldBackupsAction,
  getWALStatusAction,
  enableWALModeAction,
  optimizeWALConfigAction
} from '@/app/actions';

interface ScrapingProgress {
  isRunning: boolean;
  currentUrl: string;
  totalArticles: number;
  processedArticles: number;
  successCount: number;
  failCount: number;
  currentStep: string;
  logs: string[];
  startTime: Date | null;
  estimatedTimeRemaining: number;
  aiProcessingScheduled: number;
  currentSource?: string;
  sessionId?: string | null;
}

interface DatabaseStats {
  totalArticles: number;
  totalVocabulary: number;
  totalGrammarPoints: number;
  totalTranslations: number;
  aiProcessingQueue: number;
  mediaFiles: {
    images: number;
    videos: number;
    audios: number;
  };
  lastUpdate: string;
  diskUsage: string;
  rssSourcesCount: number;
  activeSources: number;
}

interface AIProcessingStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  totalProcessed: number;
}

interface ScrapingSession {
  id: string;
  startTime: Date | null;
  endTime?: Date | null;
  status?: 'running' | 'completed' | 'failed' | 'stopped';
  totalSources: number;
  processedSources?: number;
  totalArticles: number;
  successfulArticles: number;
  failedArticles: number;
  aiProcessingScheduled: number;
}

interface BackupFile {
  filename: string;
  size: number;
  createdAt: Date;
  formattedSize: string;
}

interface BackupSettings {
  enabled: boolean;
  intervalHours: number;
  maxFiles: number;
}

interface WALConfig {
  journalMode: any;
  walAutocheckpoint: any;
  synchronous: any;
  cacheSize: any;
}

interface VideoForManagement {
    id: number;
    title: string;
    video_download_status: string | null;
    use_local_video: boolean | null;
    video_url: string | null;
    video_path: string | null;
}

interface ArticleForAIManagement {
    id: number;
    title: string;
    ai_processing_status: string | null;
    publish_time: Date | null;
    created_at: Date;
    rss_source_name: string | null | undefined;
}

interface EnhancedScraperManagerProps {
    initialDbStats: DatabaseStats;
    initialAiStats: AIProcessingStats;
    initialScrapingSessions: ScrapingSession[];
    defaultModelId: string;
}

const EnhancedScraperManager: React.FC<EnhancedScraperManagerProps> = ({
    initialDbStats,
    initialAiStats,
    initialScrapingSessions,
    defaultModelId,
}) => {
  const [progress, setProgress] = useState<ScrapingProgress>({
    isRunning: false,
    currentUrl: '',
    totalArticles: 0,
    processedArticles: 0,
    successCount: 0,
    failCount: 0,
    currentStep: '准备就绪',
    logs: [],
    startTime: null,
    estimatedTimeRemaining: 0,
    aiProcessingScheduled: 0,
    sessionId: null
  });
  const [aiLogs, setAiLogs] = useState<any[]>([]);
  const [aiSessionId, setAiSessionId] = useState<string | null>(null);
  const [isAIProcRunning, setIsAIProcRunning] = useState(false);
  const [autoRefreshAI, setAutoRefreshAI] = useState(true);
  
  const [videos, setVideos] = useState<VideoForManagement[]>([]);
  const [videoTotalCount, setVideoTotalCount] = useState(0);
  const [videoCurrentPage, setVideoCurrentPage] = useState(1);
  const [selectedVideos, setSelectedVideos] = useState<number[]>([]);
  const videoLimit = 10;

  const [aiArticles, setAiArticles] = useState<ArticleForAIManagement[]>([]);
  const [aiArticlesTotalCount, setAiArticlesTotalCount] = useState(0);
  const [aiArticlesCurrentPage, setAiArticlesCurrentPage] = useState(1);
  const [selectedAiArticles, setSelectedAiArticles] = useState<number[]>([]);
  const [totalUnprocessedCount, setTotalUnprocessedCount] = useState<number | null>(null);
  const aiArticlesLimit = 10;
  
  const [videoDownloadSessionId, setVideoDownloadSessionId] = useState<string | null>(null);
  const [videoDownloadLogs, setVideoDownloadLogs] = useState<any[]>([]);
  const [isVideoDownloading, setIsVideoDownloading] = useState(false);


  const [selectedTab, setSelectedTab] = useState('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isPending, startTransition] = useTransition();
  const [backupFiles, setBackupFiles] = useState<BackupFile[]>([]);
  const [backupSettings, setBackupSettings] = useState<BackupSettings>({ enabled: false, intervalHours: 24, maxFiles: 10 });
  const [walConfig, setWalConfig] = useState<WALConfig | null>(null);
  const router = useRouter();

  // 将统计数据改为状态变量，以便可以更新
  const [dbStats, setDbStats] = useState<DatabaseStats>(initialDbStats);
  const [aiStats, setAiStats] = useState<AIProcessingStats>(initialAiStats);
  const scrapingSessions = initialScrapingSessions;

  // 刷新AI统计数据的函数
  const refreshAIStats = async () => {
    try {
      const response = await fetch('/api/ai/stats');
      const result = await response.json();
      if (result.success) {
        setAiStats(result.data.aiStats);
        setDbStats(prev => ({
          ...prev,
          aiProcessingQueue: result.data.aiProcessingQueue
        }));
      }
    } catch (error) {
      console.error('刷新AI统计失败:', error);
    }
  };

  // 刷新数据库统计数据的函数
  const refreshDbStats = async () => {
    try {
      const response = await fetch('/api/database/stats');
      const result = await response.json();
      if (result.success) {
        setDbStats(result.data);
      }
    } catch (error) {
      console.error('刷新数据库统计失败:', error);
    }
  };

  // 刷新所有统计数据
  const refreshAllStats = async () => {
    await Promise.all([
      refreshAIStats(),
      refreshDbStats()
    ]);
  };

  useEffect(() => {
    if (selectedTab === 'videos') {
      loadVideosForManagement(videoCurrentPage);
    }
  }, [selectedTab, videoCurrentPage]);

  useEffect(() => {
    if (selectedTab === 'ai-analysis') {
      loadArticlesForAIManagement(aiArticlesCurrentPage);
      loadTotalUnprocessedCount();
    }
  }, [selectedTab, aiArticlesCurrentPage]);

  useEffect(() => {
    if (selectedTab === 'database') {
      loadBackupFiles();
      loadWALStatus();
    }
  }, [selectedTab]);

  useEffect(() => {
    console.log('selectedTab changed to:', selectedTab);
    if (selectedTab === 'scraping') {
      console.log('切换到抓取标签页，检查当前状态...');
      checkCurrentScrapingStatus();
    }
  }, [selectedTab]);

  // 组件挂载时也检查一次状态（以防用户直接访问抓取页面）
  useEffect(() => {
    console.log('组件挂载，初始selectedTab:', selectedTab);
    if (selectedTab === 'scraping') {
      console.log('组件挂载时就在抓取标签页，检查状态...');
      checkCurrentScrapingStatus();
    }
  }, []); // 只在组件挂载时执行一次

  // 同步AI处理器状态并启动自动刷新
  useEffect(() => {
    const syncAIProcessorStatus = async () => {
      try {
        const status = await getAIProcessorStatusAction();
        console.log('AI处理器状态同步:', status);

        if (status.success && status.data) {
          setIsAIProcRunning(status.data.isRunning);

          if (status.data.isRunning && status.data.sessionId) {
            setAiSessionId(status.data.sessionId);
            setAutoRefreshAI(true);
          } else {
            // 如果没有session ID，创建一个用于监控
            setAiSessionId('ai_session_auto');
            setAutoRefreshAI(true);
          }
        }
      } catch (error) {
        console.error('同步AI处理器状态失败:', error);
      }
    };

    syncAIProcessorStatus();
  }, []);

  const loadVideosForManagement = async (page: number) => {
    startTransition(async () => {
      const result = await getVideosForManagementAction(page, videoLimit);
      if (result.success && result.data) {
        setVideos(result.data.videos);
        setVideoTotalCount(result.data.totalCount);
      }
    });
  };

  const loadArticlesForAIManagement = async (page: number) => {
    startTransition(async () => {
      const result = await getArticlesForAIManagementAction(page, aiArticlesLimit);
      if (result.success && result.data) {
        setAiArticles(result.data.articles);
        setAiArticlesTotalCount(result.data.totalCount);
      }
    });
  };

  const loadTotalUnprocessedCount = async () => {
    try {
      console.log('开始加载未处理文章数量...');
      const result = await getAllUnprocessedArticleIdsAction();
      console.log('获取未处理文章数量结果:', result);
      if (result.success && result.data) {
        console.log('未处理文章数量:', result.data.length);
        setTotalUnprocessedCount(result.data.length);
      } else {
        console.error('获取未处理文章数量失败:', result.error, result.details);
        setTotalUnprocessedCount(0); // 失败时设为0
      }
    } catch (error) {
      console.error('加载未处理文章数量时出错:', error);
      setTotalUnprocessedCount(0); // 出错时设为0
    }
  };

  const checkCurrentScrapingStatus = async () => {
    try {
      console.log('开始检查当前抓取状态...');
      const result = await getCurrentScrapingStatusAction();
      console.log('抓取状态检查结果:', result);
      if (result.isRunning && result.sessionId) {
        console.log('发现正在运行的抓取任务，sessionId:', result.sessionId);
        // 如果有正在运行的抓取任务，恢复状态
        setProgress(prev => ({
          ...prev,
          isRunning: true,
          sessionId: result.sessionId,
          currentStep: '抓取进行中...'
        }));
        setAutoRefresh(true);

        // 立即获取一次进度信息
        const progressResult = await getScrapingProgressAction(result.sessionId);
        if (progressResult.logs.length > 0) {
          const latestLog = progressResult.logs[progressResult.logs.length - 1];
          setProgress(prev => ({
            ...prev,
            currentStep: latestLog ? latestLog.message : '抓取进行中...',
            currentUrl: latestLog ? latestLog.url || '' : '',
            processedArticles: progressResult.stats.total,
            successCount: progressResult.stats.success,
            failCount: progressResult.stats.failed,
            aiProcessingScheduled: progressResult.stats.aiScheduled,
            logs: progressResult.logs.map(log => `[${new Date(log.created_at).toLocaleTimeString()}] ${log.message}`)
          }));
        }
      } else {
        // 确保状态是停止的
        setProgress(prev => ({
          ...prev,
          isRunning: false,
          currentStep: '准备就绪'
        }));
        setAutoRefresh(false);
      }
    } catch (error) {
      console.error('检查抓取状态失败:', error);
    }
  };

  // 抓取进度自动刷新
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (autoRefresh && progress.isRunning) {
      interval = setInterval(checkScrapingProgress, 5000);
    }
    return () => clearInterval(interval);
  }, [autoRefresh, progress.isRunning, progress.sessionId]);

  // 统计数据自动刷新
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (autoRefresh) {
      // 每30秒自动刷新统计数据
      interval = setInterval(async () => {
        await refreshAllStats();
      }, 30000);
    }
    return () => clearInterval(interval);
  }, [autoRefresh]);

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (autoRefreshAI && isAIProcRunning && aiSessionId) {
      interval = setInterval(async () => {
        const result = await getAIProcessingLogsAction(aiSessionId);
        setAiLogs(result.logs);
        if (!result.isRunning) {
            setIsAIProcRunning(false);
            setAutoRefreshAI(false);
            // 刷新所有统计数据
            await refreshAllStats();
            router.refresh();
        }
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [autoRefreshAI, isAIProcRunning, aiSessionId]);

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isVideoDownloading && videoDownloadSessionId) {
      const pollLogs = async () => {
        const result = await getVideoDownloadLogsAction(videoDownloadSessionId);
        if (result) {
            setVideoDownloadLogs(result.logs);
            if (!result.isRunning) {
                setIsVideoDownloading(false);
                loadVideosForManagement(videoCurrentPage); // Refresh video list
            }
        }
      };
      interval = setInterval(pollLogs, 2000);
      pollLogs(); // Initial call
    }
    return () => clearInterval(interval);
  }, [isVideoDownloading, videoDownloadSessionId]);

  const handleRefresh = () => {
      startTransition(async () => {
          // 刷新所有统计数据
          await refreshAllStats();
          // 刷新服务器端数据
          router.refresh();
      });
  };

  const checkScrapingProgress = async () => {
    if (!progress.sessionId) return;
    
    const { isRunning, logs, stats } = await getScrapingProgressAction(progress.sessionId);
    const latestLog = logs[logs.length - 1];

    setProgress(prev => ({
        ...prev,
        isRunning,
        currentStep: latestLog ? latestLog.message : (isRunning ? '抓取进行中...' : '已完成'),
        currentUrl: latestLog ? latestLog.url || '' : '',
        processedArticles: stats.total,
        successCount: stats.success,
        failCount: stats.failed,
        aiProcessingScheduled: stats.aiScheduled,
        logs: logs.map(log => `[${new Date(log.created_at).toLocaleTimeString()}] ${log.message}`),
    }));

    if (!isRunning) {
        setAutoRefresh(false);
    }
  };

  const startScraping = () => {
    startTransition(async () => {
        setProgress(prev => ({
            ...prev,
            isRunning: true,
            currentStep: '正在启动抓取器...',
            startTime: new Date(),
            logs: [`[${new Date().toLocaleTimeString()}] 发送启动抓取任务请求...`]
        }));

        const result = await startScrapingAction();
        
        if (result.success) {
            setProgress(prev => ({
                ...prev,
                sessionId: result.sessionId,
                currentStep: '抓取任务已启动，正在初始化...'
            }));
            setAutoRefresh(true);
        } else {
            setProgress(prev => ({
                ...prev,
                isRunning: false,
                currentStep: `启动失败: ${result.error}`,
                logs: [...prev.logs, `[${new Date().toLocaleTimeString()}] 启动失败: ${result.error}`]
            }));
        }
    });
  };

  const stopScraping = () => {
      startTransition(async () => {
          const result = await stopScrapingAction();
          if(result.success) {
            setProgress(prev => ({
              ...prev,
              isRunning: false,
              currentStep: '已停止',
              logs: [...prev.logs, `[${new Date().toLocaleTimeString()}] 抓取任务已停止`]
            }));
            setAutoRefresh(false);
          } else {
            alert(`停止抓取失败: ${result.error}`);
          }
      });
  };

  const handleClearScrapedData = () => {
    startTransition(async () => {
        const result = await clearScrapedDataAction();
        if (result.success) {
            // 刷新所有统计数据
            await refreshAllStats();
            router.refresh();
        } else {
            alert(`删除数据失败: ${result.error}`);
        }
    });
  };

  const exportData = () => {
    startTransition(async () => {
        const result = await exportDataAction();
        if (result.success && result.data) {
            const blob = new Blob([JSON.stringify(result.data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `nhk_news_export_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        } else {
            alert(`导出数据失败: ${result.error}`);
        }
    });
  };

  // 备份相关函数
  const loadBackupFiles = async () => {
    const result = await getBackupFilesAction();
    if (result.success) {
      setBackupFiles(result.backupFiles);
      if (result.settings) {
        setBackupSettings(result.settings);
      }
    }
  };

  const createBackup = () => {
    startTransition(async () => {
      const result = await createDatabaseBackupAction();
      if (result.success) {
        alert(`数据库备份创建成功: ${(result as any).filename || '未知文件名'}`);
        await loadBackupFiles();
      } else {
        alert(`创建备份失败: ${result.error}`);
      }
    });
  };

  const deleteBackup = (filename: string) => {
    if (confirm(`确定要删除备份文件 ${filename} 吗？`)) {
      startTransition(async () => {
        const result = await deleteBackupFileAction(filename);
        if (result.success) {
          alert('备份文件删除成功');
          await loadBackupFiles();
        } else {
          alert(`删除备份文件失败: ${result.error}`);
        }
      });
    }
  };

  const downloadBackup = (filename: string) => {
    const url = `/api/database/backup/${filename}`;
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const downloadDatabase = () => {
    const url = '/api/database/download';
    const a = document.createElement('a');
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const updateBackupSettingsHandler = () => {
    startTransition(async () => {
      const result = await updateBackupSettingsAction(
        backupSettings.enabled,
        backupSettings.intervalHours,
        backupSettings.maxFiles
      );
      if (result.success) {
        alert('备份设置已更新');
      } else {
        alert(`更新备份设置失败: ${result.error}`);
      }
    });
  };

  const cleanupOldBackups = () => {
    startTransition(async () => {
      const result = await cleanupOldBackupsAction();
      if (result.success) {
        alert(`清理完成，删除了 ${(result as any).deletedCount || 0} 个旧备份文件`);
        await loadBackupFiles();
      } else {
        alert(`清理旧备份失败: ${result.error}`);
      }
    });
  };

  // WAL相关函数
  const loadWALStatus = async () => {
    const result = await getWALStatusAction();
    if (result.success) {
      setWalConfig(result.walConfig);
    }
  };

  const enableWALMode = () => {
    startTransition(async () => {
      const result = await enableWALModeAction();
      if (result.success) {
        alert('WAL模式已启用');
        await loadWALStatus();
      } else {
        alert(`启用WAL模式失败: ${result.error}`);
      }
    });
  };

  const optimizeWALConfig = () => {
    startTransition(async () => {
      const result = await optimizeWALConfigAction();
      if (result.success) {
        alert('WAL配置已优化');
        await loadWALStatus();
      } else {
        alert(`优化WAL配置失败: ${result.error}`);
      }
    });
  };

  // AI处理器控制函数
  const startAIProcessing = () => {
    startTransition(async () => {
      const result = await startAIProcessorAction();
      if (result.success) {
        setIsAIProcRunning(true);
        await refreshAllStats();
      } else {
        alert(`启动AI处理器失败: ${result.error}`);
      }
    });
  };

  const stopAIProcessing = () => {
    startTransition(async () => {
      const result = await stopAIProcessorAction();
      if (result.success) {
        setIsAIProcRunning(false);
        await refreshAllStats();
      } else {
        alert(`停止AI处理器失败: ${result.error}`);
      }
    });
  };

  const clearAIQueue = () => {
    startTransition(async () => {
      await clearAIQueueAction();
      // 刷新所有统计数据
      await refreshAllStats();
    });
  };

  const handleDownloadVideos = (allPending: boolean = false) => {
    startTransition(async () => {
        setVideoDownloadLogs([]);
        setVideoDownloadSessionId(null);
        setIsVideoDownloading(true);

        const idsToDownload = allPending ? undefined : selectedVideos;
        const result = await startVideoDownloadAction(idsToDownload);

        if (result.success && result.sessionId) {
            setVideoDownloadSessionId(result.sessionId);
        } else {
            setIsVideoDownloading(false);
            alert(`启动视频下载失败: ${result.error}`);
        }
        setSelectedVideos([]);
    });
  };
  
  const handleToggleUseLocalVideo = (articleId: number, useLocal: boolean) => {
    startTransition(async () => {
      await setUseLocalVideoAction(articleId, useLocal);
      loadVideosForManagement(videoCurrentPage);
    });
  };
  
  const handleSelectVideo = (id: number) => {
    setSelectedVideos(prev => 
      prev.includes(id) ? prev.filter(vid => vid !== id) : [...prev, id]
    );
  };

  const handleSelectAllVideos = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedVideos(videos.map(v => v.id));
    } else {
      setSelectedVideos([]);
    }
  };

  const handleSelectAiArticle = (id: number) => {
    setSelectedAiArticles(prev =>
      prev.includes(id)
        ? prev.filter(articleId => articleId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAllAiArticles = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedAiArticles(aiArticles.map(a => a.id));
    } else {
      setSelectedAiArticles([]);
    }
  };

  // 判断文章是否可以重新进行AI分析
  // 包括：待处理(pending)、失败(failed)
  // 排除：处理中(processing)、已完成(completed)、已跳过(skipped)
  const canReprocessArticle = (status: string | null | undefined) => {
    return status === 'failed' || status === 'pending';
  };

  const handleScheduleAIAnalysis = (allPending: boolean) => {
    startTransition(async () => {
      let articleIds: number[];

      if (allPending) {
        // 获取所有未处理的文章ID，而不仅仅是当前分页的
        const allUnprocessedResult = await getAllUnprocessedArticleIdsAction();
        if (!allUnprocessedResult.success || !allUnprocessedResult.data) {
          alert(`获取未处理文章列表失败: ${allUnprocessedResult.error}`);
          return;
        }
        articleIds = allUnprocessedResult.data;
      } else {
        articleIds = selectedAiArticles;
      }

      // 前端验证
      if (!allPending && articleIds.length === 0) {
        alert('请先选择要分析的文章');
        return;
      }

      if (allPending && articleIds.length === 0) {
        alert('没有找到需要分析的文章');
        return;
      }

      const result = await scheduleAIAnalysisAction(articleIds);
      if (result.success) {
        setSelectedAiArticles([]);
        loadArticlesForAIManagement(aiArticlesCurrentPage);
        // 刷新AI统计数据
        await refreshAIStats();
        // 如果是处理全部未处理的，需要更新总数
        if (allPending) {
          loadTotalUnprocessedCount();
        }
        // 显示成功消息
        if (result.message) {
          alert(result.message);
        } else {
          alert(`成功添加 ${articleIds.length} 篇文章到AI分析队列`);
        }
      } else {
        alert(`添加AI分析任务失败: ${result.error}`);
      }
    });
  };

  const progressPercentage = progress.totalArticles > 0 
    ? (progress.processedArticles / progress.totalArticles) * 100 
    : 0;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (startTime: Date | null, endTime?: Date | null) => {
    if (!startTime) return '未知';
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000);
    return formatTime(duration);
  };
  
  const renderVideoStatus = (status: VideoForManagement['video_download_status']) => {
    const statusMap = {
        pending: { label: '待下载', color: 'bg-yellow-100 text-yellow-800' },
        downloading: { label: '下载中', color: 'bg-blue-100 text-blue-800' },
        completed: { label: '已完成', color: 'bg-green-100 text-green-800' },
        failed: { label: '失败', color: 'bg-red-100 text-red-800' },
        disabled: { label: '无视频', color: 'bg-gray-100 text-gray-800' }
    };
    const current = statusMap[status as keyof typeof statusMap] || statusMap.disabled;
    return <span className={`px-2 py-1 text-xs font-medium rounded-full ${current.color}`}>{current.label}</span>;
  };

  const renderAIProcessingStatus = (status: ArticleForAIManagement['ai_processing_status']) => {
    const statusMap = {
        pending: { label: '待处理', color: 'bg-yellow-100 text-yellow-800' },
        processing: { label: '处理中', color: 'bg-blue-100 text-blue-800' },
        completed: { label: '已完成', color: 'bg-green-100 text-green-800' },
        failed: { label: '失败', color: 'bg-red-100 text-red-800' },
        skipped: { label: '已跳过', color: 'bg-gray-100 text-gray-800' },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: '未处理', color: 'bg-gray-100 text-gray-800' };

    return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
            {statusInfo.label}
        </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">增强版抓取管理</h2>
          <p className="text-gray-600 mt-1">基于RSS源的智能新闻抓取系统，支持AI内容处理</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2 text-sm text-gray-600">
            <input type="checkbox" checked={autoRefresh} onChange={(e) => setAutoRefresh(e.target.checked)} className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
            <span>自动刷新</span>
          </label>
          
          <button onClick={handleRefresh} disabled={isPending} className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all">
            {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
            <span>刷新</span>
          </button>
        </div>
      </div>

      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: '概览', icon: BarChart3 },
            { id: 'scraping', label: '抓取控制', icon: Activity },
            { id: 'ai-processing', label: 'AI处理', icon: Bot },
            { id: 'ai-analysis', label: 'AI分析管理', icon: BrainCircuit },
            { id: 'videos', label: '视频管理', icon: Film },
            { id: 'sessions', label: '抓取历史', icon: Calendar },
            { id: 'database', label: '数据管理', icon: Database }
          ].map((tab) => (
            <button key={tab.id} onClick={() => setSelectedTab(tab.id)} className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${selectedTab === tab.id ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {selectedTab === 'overview' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-gray-600">文章总数</p><p className="text-2xl font-bold text-gray-900">{dbStats.totalArticles}</p></div><div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center"><FileText className="h-6 w-6 text-blue-600" /></div></div><div className="mt-2 text-xs text-gray-500">来自 {dbStats.activeSources} 个活跃RSS源</div></div>
            <div className="bg-white rounded-lg border border-gray-200 p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-gray-600">AI处理队列</p><p className="text-2xl font-bold text-gray-900">{dbStats.aiProcessingQueue}</p></div><div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center"><Bot className="h-6 w-6 text-purple-600" /></div></div><div className="mt-2 text-xs text-gray-500">已完成 {aiStats.completed} 个任务</div></div>
            <div className="bg-white rounded-lg border border-gray-200 p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-gray-600">词汇总数</p><p className="text-2xl font-bold text-gray-900">{dbStats.totalVocabulary}</p></div><div className="h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center"><Database className="h-6 w-6 text-emerald-600" /></div></div><div className="mt-2 text-xs text-gray-500">语法 {dbStats.totalGrammarPoints} 条</div></div>
            <div className="bg-white rounded-lg border border-gray-200 p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-gray-600">存储空间</p><p className="text-2xl font-bold text-gray-900">{dbStats.diskUsage}</p></div><div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center"><BarChart3 className="h-6 w-6 text-orange-600" /></div></div><div className="mt-2 text-xs text-gray-500">媒体文件: {dbStats.mediaFiles.images + dbStats.mediaFiles.videos + dbStats.mediaFiles.audios}</div></div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-6"><h3 className="text-lg font-semibold text-gray-900 mb-4">AI处理统计</h3><div className="grid grid-cols-2 md:grid-cols-4 gap-4"><div className="text-center p-3 bg-yellow-50 rounded-lg"><div className="text-2xl font-bold text-yellow-600">{aiStats.pending}</div><div className="text-sm text-yellow-700">待处理</div></div><div className="text-center p-3 bg-blue-50 rounded-lg"><div className="text-2xl font-bold text-blue-600">{aiStats.processing}</div><div className="text-sm text-blue-700">处理中</div></div><div className="text-center p-3 bg-green-50 rounded-lg"><div className="text-2xl font-bold text-green-600">{aiStats.completed}</div><div className="text-sm text-green-700">已完成</div></div><div className="text-center p-3 bg-red-50 rounded-lg"><div className="text-2xl font-bold text-red-600">{aiStats.failed}</div><div className="text-sm text-red-700">失败</div></div></div></div>
          <div className="bg-white rounded-lg border border-gray-200 p-6"><h3 className="text-lg font-semibold text-gray-900 mb-4">系统状态</h3><div className="grid grid-cols-1 md:grid-cols-3 gap-4"><div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg"><Globe className="h-8 w-8 text-blue-600" /><div><div className="font-medium text-blue-900">RSS源状态</div><div className="text-sm text-blue-700">{dbStats.activeSources}/{dbStats.rssSourcesCount} 活跃</div></div></div><div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg"><Bot className="h-8 w-8 text-purple-600" /><div><div className="font-medium text-purple-900">AI处理器</div><div className="text-sm text-purple-700">{isAIProcRunning ? '运行中' : (aiStats.processing > 0 ? '运行中' : '空闲')}</div></div></div><div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg"><Database className="h-8 w-8 text-green-600" /><div><div className="font-medium text-green-900">数据库</div><div className="text-sm text-green-700">正常运行</div></div></div></div></div>
        </div>
      )}

      {selectedTab === 'scraping' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6"><h3 className="text-lg font-semibold text-gray-900">抓取控制</h3><div className="flex items-center space-x-3">{!progress.isRunning ? (<button onClick={startScraping} disabled={isPending} className="flex items-center space-x-2 px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">{isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}<span>{isPending ? '启动中...' : '开始抓取'}</span></button>) : (<button onClick={stopScraping} disabled={isPending} className="flex items-center space-x-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400">{isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Pause className="h-4 w-4" />}<span>{isPending ? '处理中...' : '停止抓取'}</span></button>)}</div></div>
            <div className="space-y-4"><div className="flex items-center justify-between text-sm"><span className="text-gray-600">当前状态</span><span className={`font-medium ${progress.isRunning ? 'text-blue-600' : 'text-gray-900'}`}>{progress.currentStep}</span></div>{progress.totalArticles > 0 && (<><div className="w-full bg-gray-200 rounded-full h-3"><div className="bg-indigo-600 h-3 rounded-full transition-all duration-300" style={{ width: `${progressPercentage}%` }}></div></div><div className="flex justify-between text-sm text-gray-600"><span>进度: {progress.processedArticles}/{progress.totalArticles}</span><span>{progressPercentage.toFixed(1)}%</span></div></>)}{progress.currentUrl && (<div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg"><span className="font-medium">当前处理:</span> <span className="break-all ml-1">{progress.currentUrl}</span></div>)}{progress.isRunning && (<div className="grid grid-cols-2 md:grid-cols-5 gap-4 pt-4 border-t border-gray-200"><div className="text-center"><div className="text-2xl font-bold text-green-600">{progress.successCount}</div><div className="text-sm text-gray-600">成功</div></div><div className="text-center"><div className="text-2xl font-bold text-red-600">{progress.failCount}</div><div className="text-sm text-gray-600">失败</div></div><div className="text-center"><div className="text-2xl font-bold text-purple-600">{progress.aiProcessingScheduled}</div><div className="text-sm text-gray-600">AI队列</div></div><div className="text-center"><div className="text-2xl font-bold text-blue-600">{progress.startTime ? formatTime(Math.floor((Date.now() - progress.startTime.getTime()) / 1000)) : '0:00'}</div><div className="text-sm text-gray-600">已用时间</div></div><div className="text-center"><div className="text-2xl font-bold text-orange-600">{formatTime(progress.estimatedTimeRemaining)}</div><div className="text-sm text-gray-600">预计剩余</div></div></div>)}</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200"><div className="p-4 border-b border-gray-200"><h3 className="font-semibold text-gray-900">实时日志</h3></div><div className="p-4"><div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">{progress.logs.length > 0 ? (progress.logs.slice().reverse().map((log, index) => (<div key={index} className="mb-1">{log}</div>))) : (<div className="text-gray-500">暂无日志记录</div>)}</div></div></div>
        </div>
      )}

      {selectedTab === 'ai-processing' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">AI处理监控</h3>
                <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                        <BrainCircuit className="h-4 w-4 text-gray-600" />
                        <span className="text-sm text-gray-700">
                          当前模型: <span className="font-medium text-indigo-600">{defaultModelId}</span>
                        </span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className={`h-3 w-3 rounded-full ${isAIProcRunning ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                        <span className="text-sm text-gray-700">
                          状态: <span className={`font-medium ${isAIProcRunning ? 'text-green-600' : 'text-gray-600'}`}>
                            {isAIProcRunning ? '运行中' : '空闲'}
                          </span>
                        </span>
                    </div>
                    <div className="flex items-center space-x-3">
                        <button onClick={clearAIQueue} disabled={isPending} className="flex items-center space-x-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"><Trash2 className="h-4 w-4" /><span>清空队列</span></button>
                    </div>
                </div>
            </div>
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <BrainCircuit className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">AI处理器自动运行</p>
                  <p className="mt-1">AI处理器现在会自动在后台运行，持续处理队列中的任务。无需手动启动或停止。</p>
                </div>
              </div>
            </div>
            <div className="p-4 bg-gray-900 text-green-400 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                {aiLogs.length > 0 ? (
                    aiLogs.map((log, index) => {
                        const logColor = log.log_level === 'ERROR' ? 'text-red-400' :
                                       log.log_level === 'WARNING' ? 'text-yellow-400' :
                                       log.log_level === 'SUCCESS' ? 'text-green-400' : 'text-blue-400';
                        return (
                            <div key={index} className="mb-1">
                                <span className={logColor}>{`[${new Date(log.created_at).toLocaleTimeString()}] ${log.message}`}</span>
                            </div>
                        );
                    })
                ) : (
                    <div className="text-gray-500">暂无AI处理日志。点击“启动AI处理器”开始。</div>
                )}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'ai-analysis' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">AI分析管理</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => handleScheduleAIAnalysis(true)}
                  disabled={isPending || (totalUnprocessedCount !== null && totalUnprocessedCount === 0)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                >
                  <BrainCircuit className="h-4 w-4"/>
                  <span>分析全部未处理 ({totalUnprocessedCount ?? '加载中...'})</span>
                </button>
                <button
                  onClick={() => handleScheduleAIAnalysis(false)}
                  disabled={isPending || selectedAiArticles.length === 0}
                  className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400"
                >
                  <BrainCircuit className="h-4 w-4"/>
                  <span>分析选中 ({selectedAiArticles.length})</span>
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="p-4 text-left">
                      <input
                        type="checkbox"
                        onChange={handleSelectAllAiArticles}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文章标题</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AI处理状态</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">来源</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {aiArticles.map(article => (
                    <tr key={article.id}>
                      <td className="p-4">
                        <input
                          type="checkbox"
                          checked={selectedAiArticles.includes(article.id)}
                          onChange={() => handleSelectAiArticle(article.id)}
                          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {article.title}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {renderAIProcessingStatus(article.ai_processing_status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {article.rss_source_name || '未知'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {article.publish_time ? new Date(article.publish_time).toLocaleDateString('zh-CN') : '未知'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页控件 */}
            {aiArticlesTotalCount > aiArticlesLimit && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                  显示第 {(aiArticlesCurrentPage - 1) * aiArticlesLimit + 1} 到 {Math.min(aiArticlesCurrentPage * aiArticlesLimit, aiArticlesTotalCount)} 条，共 {aiArticlesTotalCount} 条
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setAiArticlesCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={aiArticlesCurrentPage === 1}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <span className="text-sm text-gray-700">
                    第 {aiArticlesCurrentPage} 页，共 {Math.ceil(aiArticlesTotalCount / aiArticlesLimit)} 页
                  </span>
                  <button
                    onClick={() => setAiArticlesCurrentPage(prev => Math.min(Math.ceil(aiArticlesTotalCount / aiArticlesLimit), prev + 1))}
                    disabled={aiArticlesCurrentPage >= Math.ceil(aiArticlesTotalCount / aiArticlesLimit)}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {selectedTab === 'videos' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">视频管理</h3>
              <div className="flex items-center space-x-3">
                <button onClick={() => handleDownloadVideos(true)} disabled={isPending || isVideoDownloading} className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400">
                  <Download className="h-4 w-4"/><span>下载全部待处理</span>
                </button>
                <button onClick={() => handleDownloadVideos(false)} disabled={isPending || isVideoDownloading || selectedVideos.length === 0} className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400">
                  <Download className="h-4 w-4"/><span>下载选中 ({selectedVideos.length})</span>
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="p-4 text-left"><input type="checkbox" onChange={handleSelectAllVideos} className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"/></th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文章标题</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下载状态</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">播放源</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {videos.map(video => (
                    <tr key={video.id}>
                      <td className="p-4"><input type="checkbox" checked={selectedVideos.includes(video.id)} onChange={() => handleSelectVideo(video.id)} className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"/></td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{video.title}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{renderVideoStatus(video.video_download_status)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{video.use_local_video ? '本地视频' : '远程内嵌'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onClick={() => handleToggleUseLocalVideo(video.id, !video.use_local_video)} disabled={video.video_download_status !== 'completed'} className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400">切换</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {isVideoDownloading && (
              <div className="mt-6">
                <h4 className="text-md font-semibold text-gray-800 mb-2">视频下载日志</h4>
                <div className="bg-gray-900 p-4 rounded-lg font-mono text-xs max-h-60 overflow-y-auto">
                  {videoDownloadLogs.length > 0 ? (
                    videoDownloadLogs.map((log, index) => {
                      const logColor = log.log_level === 'ERROR' ? 'text-red-400' : log.log_level === 'WARN' ? 'text-yellow-400' : 'text-green-400';
                      return (
                        <div key={index} className="mb-1 leading-relaxed">
                          <span className="text-gray-500 mr-2">{`[${new Date(log.created_at).toLocaleTimeString()}]`}</span>
                          <span className={logColor}>{log.message}</span>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-gray-500 flex items-center space-x-2">
                      <Loader className="h-4 w-4 animate-spin" />
                      <span>等待日志...</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {selectedTab === 'sessions' && (
        <div className="space-y-6"><div className="bg-white rounded-lg border border-gray-200 p-6"><h3 className="text-lg font-semibold text-gray-900 mb-4">抓取会话历史</h3>{scrapingSessions.length > 0 ? (<div className="space-y-4">{scrapingSessions.map((session) => (<div key={session.id} className="border border-gray-200 rounded-lg p-4"><div className="flex items-center justify-between mb-3"><div className="flex items-center space-x-3"><span className={`px-2 py-1 rounded-full text-xs font-medium ${session.status === 'completed' ? 'bg-green-100 text-green-800' : session.status === 'running' ? 'bg-blue-100 text-blue-800' : session.status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>{session.status === 'completed' ? '已完成' : session.status === 'running' ? '运行中' : session.status === 'failed' ? '失败' : '已停止'}</span><span className="text-sm text-gray-600">会话ID: {session.id}</span></div><span className="text-sm text-gray-500">{formatDuration(session.startTime, session.endTime)}</span></div><div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm"><div><span className="text-gray-600">RSS源数:</span><span className="font-medium ml-1">{session.totalSources}</span></div><div><span className="text-gray-600">总文章数:</span><span className="font-medium ml-1">{session.totalArticles}</span></div><div><span className="text-gray-600">成功:</span><span className="font-medium ml-1 text-green-600">{session.successfulArticles}</span></div><div><span className="text-gray-600">失败:</span><span className="font-medium ml-1 text-red-600">{session.failedArticles}</span></div><div><span className="text-gray-600">AI队列:</span><span className="font-medium ml-1 text-purple-600">{session.aiProcessingScheduled}</span></div></div><div className="mt-3 text-xs text-gray-500">开始时间: {session.startTime ? new Date(session.startTime).toLocaleString('zh-CN') : '未知'}{session.endTime && (<span className="ml-4">结束时间: {new Date(session.endTime).toLocaleString('zh-CN')}</span>)}</div></div>))}</div>) : (<div className="text-center py-8 text-gray-500"><Calendar className="h-12 w-12 mx-auto mb-3 text-gray-400" /><p>暂无抓取历史记录</p></div>)}</div></div>
      )}

      {selectedTab === 'database' && (
        <div className="space-y-6">
          {/* 数据管理 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">数据管理</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <button
                onClick={exportData}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Download className="h-4 w-4" />
                <span>导出数据</span>
              </button>
              <button
                onClick={downloadDatabase}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Download className="h-4 w-4" />
                <span>下载数据库</span>
              </button>
              <button
                disabled={true}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Eye className="h-4 w-4" />
                <span>查看数据 (待开发)</span>
              </button>
              <button
                onClick={handleClearScrapedData}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                <span>{isPending ? '删除中...' : '删除已抓取文章'}</span>
              </button>
            </div>
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">系统特性：</p>
                  <ul className="mt-1 list-disc list-inside space-y-1">
                    <li>基于RSS源的智能抓取，支持灵活的字段映射配置</li>
                    <li>AI处理功能可选，支持翻译、词汇提取、语法分析等</li>
                    <li>数据库结构已优化，分离原始内容和AI处理结果</li>
                    <li>支持异步AI处理队列，提升系统响应性</li>
                    <li>完整的抓取历史记录和性能监控</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* 数据库备份 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">数据库备份</h3>

            {/* 备份设置 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">自动备份设置</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="backup-enabled"
                    checked={backupSettings.enabled}
                    onChange={(e) => setBackupSettings(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor="backup-enabled" className="text-sm text-gray-700">启用自动备份</label>
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">备份间隔（小时）</label>
                  <input
                    type="number"
                    min="1"
                    max="168"
                    value={backupSettings.intervalHours}
                    onChange={(e) => setBackupSettings(prev => ({ ...prev, intervalHours: parseInt(e.target.value) || 24 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">保留文件数</label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={backupSettings.maxFiles}
                    onChange={(e) => setBackupSettings(prev => ({ ...prev, maxFiles: parseInt(e.target.value) || 10 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <button
                  onClick={updateBackupSettingsHandler}
                  disabled={isPending}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400 transition-colors"
                >
                  保存设置
                </button>
              </div>
            </div>

            {/* 备份操作 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <button
                onClick={createBackup}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isPending ? <Loader className="h-4 w-4 animate-spin" /> : <Database className="h-4 w-4" />}
                <span>{isPending ? '创建中...' : '立即备份'}</span>
              </button>
              <button
                onClick={cleanupOldBackups}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span>清理旧备份</span>
              </button>
              <button
                onClick={loadBackupFiles}
                disabled={isPending}
                className="flex items-center justify-center space-x-2 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span>刷新列表</span>
              </button>
            </div>

            {/* 备份文件列表 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">备份文件列表</h4>
              {backupFiles.length > 0 ? (
                <div className="space-y-2">
                  {backupFiles.map((file) => (
                    <div key={file.filename} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{file.filename}</div>
                        <div className="text-sm text-gray-500">
                          大小: {file.formattedSize} | 创建时间: {new Date(file.createdAt).toLocaleString('zh-CN')}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => downloadBackup(file.filename)}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                        >
                          下载
                        </button>
                        <button
                          onClick={() => deleteBackup(file.filename)}
                          disabled={isPending}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:bg-gray-400 transition-colors"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>暂无备份文件</p>
                </div>
              )}
            </div>
          </div>

          {/* SQLite WAL模式状态 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">SQLite并发性能优化 (WAL模式)</h3>

            {walConfig ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">当前配置</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Journal模式:</span>
                        <span className={`font-medium ${
                          walConfig.journalMode?.[0]?.journal_mode === 'wal'
                            ? 'text-green-600'
                            : 'text-orange-600'
                        }`}>
                          {walConfig.journalMode?.[0]?.journal_mode || 'unknown'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">WAL自动检查点:</span>
                        <span className="font-medium text-gray-900">
                          {walConfig.walAutocheckpoint?.[0]?.wal_autocheckpoint || 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">同步模式:</span>
                        <span className="font-medium text-gray-900">
                          {walConfig.synchronous?.[0]?.synchronous || 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">缓存大小:</span>
                        <span className="font-medium text-gray-900">
                          {walConfig.cacheSize?.[0]?.cache_size || 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">WAL模式优势</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• 读写操作可以并发执行</li>
                      <li>• 写操作不会阻塞读操作</li>
                      <li>• 提高并发性能</li>
                      <li>• 减少锁竞争</li>
                      <li>• 更好的崩溃恢复</li>
                    </ul>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={enableWALMode}
                    disabled={isPending || walConfig.journalMode?.[0]?.journal_mode === 'wal'}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                  >
                    {walConfig.journalMode?.[0]?.journal_mode === 'wal' ? 'WAL已启用' : '启用WAL模式'}
                  </button>
                  <button
                    onClick={optimizeWALConfig}
                    disabled={isPending}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                  >
                    优化WAL配置
                  </button>
                  <button
                    onClick={loadWALStatus}
                    disabled={isPending}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-400 transition-colors"
                  >
                    刷新状态
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <div className="text-gray-500">正在加载WAL状态...</div>
                <button
                  onClick={loadWALStatus}
                  className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  重新加载
                </button>
              </div>
            )}
          </div>

          {/* 数据库表统计 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">数据库表统计</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { name: 'articles', count: dbStats.totalArticles, label: '文章' },
                { name: 'vocabulary', count: dbStats.totalVocabulary, label: '词汇' },
                { name: 'grammar_points', count: dbStats.totalGrammarPoints, label: '语法点' },
                { name: 'translations', count: dbStats.totalTranslations, label: '翻译' },
                { name: 'rss_sources', count: dbStats.rssSourcesCount, label: 'RSS源' },
                { name: 'ai_queue', count: dbStats.aiProcessingQueue, label: 'AI队列' },
                { name: 'media_files', count: dbStats.mediaFiles.images + dbStats.mediaFiles.videos + dbStats.mediaFiles.audios, label: '媒体文件' },
                { name: 'sessions', count: scrapingSessions.length, label: '抓取会话' }
              ].map((table) => (
                <div key={table.name} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900">{table.count}</div>
                  <div className="text-sm text-gray-600">{table.label}</div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-xs text-gray-500">最后更新: {dbStats.lastUpdate}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedScraperManager;
