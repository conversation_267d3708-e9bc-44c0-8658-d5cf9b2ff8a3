# 数据库切换方案1: 直接切换到 PostgreSQL - 详细实施步骤

## 1. 概述

本方案旨在提供一个一次性、永久性地将应用数据库从 SQLite 迁移到 PostgreSQL 的详细技术实现路线图。

**核心目标**: 彻底替换数据访问层的实现，使应用完全基于 PostgreSQL 运行。

## 2. 实施步骤

### 步骤一: 依赖与环境配置

1.  **修改依赖 (`package.json`)**:
    *   移除 `sqlite3`: `npm uninstall sqlite3 @types/sqlite3`
    *   添加 `pg` (node-postgres): `npm install pg @types/pg`

2.  **配置环境变量 (`.env`)**:
    *   在项目根目录的 `.env` 文件中，添加数据库连接字符串：
      ```
      DATABASE_URL="postgresql://YOUR_USER:YOUR_PASSWORD@YOUR_HOST:YOUR_PORT/YOUR_DATABASE"
      ```

3.  **安装 PostgreSQL**:
    *   在开发环境中，需要安装并运行一个 PostgreSQL 实例。推荐使用 Docker 来快速启动一个数据库服务。

### 步骤二: 重写数据库初始化脚本

**目标文件**: `scripts/init-new-db-optimized.js`

**改动内容**:
*   将所有 `CREATE TABLE` 语句中的 SQLite 特定语法修改为 PostgreSQL 语法。
*   主要改动点：
    *   `INTEGER PRIMARY KEY AUTOINCREMENT` -> `SERIAL PRIMARY KEY` 或 `INTEGER GENERATED BY DEFAULT AS IDENTITY`
    *   `DATETIME DEFAULT CURRENT_TIMESTAMP` -> `TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP`
    *   `BOOLEAN` -> `BOOLEAN` (PostgreSQL原生支持)
*   **示例**:
    ```javascript
    // 原 SQLite 写法
    // const db = new sqlite3.Database(dbPath);
    // db.run('CREATE TABLE articles (...)');

    // 新 PostgreSQL 写法
    const { Client } = require('pg');
    const client = new Client({ connectionString: process.env.DATABASE_URL });
    await client.connect();
    await client.query('CREATE TABLE articles (...)'); // 使用 PostgreSQL 语法
    await client.end();
    ```

### 步骤三: 重构数据访问层 (`DatabaseManager`)

**目标文件**: `src/lib/server/database.ts`

**改动内容**: 这是本次重构的核心工作。需要彻底替换所有 `sqlite3` 的实现。

1.  **引入 `pg` 连接池**:
    *   在文件顶部，将 `import sqlite3 from 'sqlite3'` 替换为 `import { Pool } from 'pg'`。
    *   创建一个全局的 `Pool` 实例，它会从环境变量 `DATABASE_URL` 自动读取配置。
      ```typescript
      import { Pool } from 'pg';
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
      });
      ```

2.  **重写查询方法**:
    *   **`query` 方法 (获取多行)**:
        *   **原逻辑**: `db.all(sql, params, callback)`
        *   **新逻辑**: 使用连接池执行查询 `const result = await pool.query(sql, params); return result.rows;`
    *   **`get` 方法 (获取单行)**:
        *   **原逻辑**: `db.get(sql, params, callback)`
        *   **新逻辑**: 同样使用 `pool.query`，但取返回结果的 `rows[0]`。
    *   **`run` 方法 (执行操作)**:
        *   **原逻辑**: `db.run(sql, params, callback)`
        *   **新逻辑**: 使用 `pool.query(sql, params)`。`pg` 的 `run` 操作与 `query` 使用同一个方法。返回的结果对象中可以获取 `rowCount` 等信息。

*   **示例改动 (`query` 方法)**:
    ```typescript
    // 原有方法
    // async query<T>(sql: string, params: any[] = []): Promise<T[]> {
    //   return new Promise((resolve, reject) => {
    //     const db = this.getConnection(); // sqlite3 connection
    //     db.all(sql, params, (err, rows: T[]) => { /* ... */ });
    //   });
    // }

    // 新方法 (假设 dbManager 整个类被重构)
    import { Pool } from 'pg';

    class DatabaseManager {
        private pool: Pool;

        constructor() {
            this.pool = new Pool({ connectionString: process.env.DATABASE_URL });
        }

        async query<T>(sql: string, params: any[] = []): Promise<T[]> {
            try {
                const result = await this.pool.query(sql, params);
                return result.rows;
            } catch (error) {
                console.error("PostgreSQL query failed:", error);
                throw error;
            }
        }
        // ... 其他方法的重写 ...
    }
    ```

3.  **处理事务**:
    *   PostgreSQL 的事务需要从连接池中获取一个**单独的客户端连接**来执行。
    *   **逻辑**:
        1.  `const client = await pool.connect();`
        2.  `await client.query('BEGIN');`
        3.  `// 执行所有事务内操作`
        4.  `await client.query('COMMIT');`
        5.  `catch (e) { await client.query('ROLLBACK'); }`
        6.  `finally { client.release(); }`

### 步骤四: 全面测试

*   在完成代码重构后，必须对应用的所有功能进行端到端的测试，确保：
    1.  数据库初始化脚本能正确执行。
    2.  所有 Server Action 能够正确地读写数据库。
    3.  数据格式和类型在 PostgreSQL 中被正确处理。

## 3. 总结

直接切换到 PostgreSQL 是一个目标明确的重构任务。虽然需要重写整个数据访问层，但由于不需要处理复杂的抽象和多数据库兼容问题，其复杂度和风险都是可控的。