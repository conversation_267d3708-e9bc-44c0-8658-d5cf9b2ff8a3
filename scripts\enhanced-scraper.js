import { EnhancedNHKScraper } from '../src/lib/server/scraper.js';

// 运行增强版抓取器
const scraper = new EnhancedNHKScraper();

console.log('🚀 启动增强版NHK新闻抓取器...');
console.log('📊 新功能:');
console.log('  - 基于RSS源的抓取');
console.log('  - 灵活的字段映射配置');
console.log('  - 可选的AI内容处理');
console.log('  - 异步AI处理队列');
console.log('  - 详细的抓取日志');
console.log('');

scraper.scrapeAllActiveSources().then((results) => {
  console.log('\n✅ 抓取任务完成！');
  console.log('📈 总结报告:');
  
  let totalSuccess = 0;
  let totalFailed = 0;
  
  results.forEach(result => {
    if (result.error) {
      console.log(`❌ RSS源 ${result.rssSourceId}: ${result.error}`);
    } else {
      console.log(`✅ RSS源 ${result.rssSourceId}: 成功 ${result.success}, 失败 ${result.failed}`);
      totalSuccess += result.success;
      totalFailed += result.failed;
    }
  });
  
  console.log('');
  console.log(`📊 总计: 成功 ${totalSuccess} 篇, 失败 ${totalFailed} 篇`);
  console.log('🎯 下一步: 运行 npm run dev 查看抓取结果');
  
  process.exit(0);
}).catch(error => {
  console.error('❌ 抓取任务失败:', error);
  process.exit(1);
});