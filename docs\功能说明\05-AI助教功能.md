# AI助教功能说明

## 1. 功能概述

### 1.1 主要功能
AI助教是智能学习辅助系统，基于Google Gemini大语言模型，为用户提供个性化的日语学习指导。包括智能问答、语法解析、情景对话练习等功能。

### 1.2 核心特性
- **智能问答**: 回答日语学习相关问题
- **语法解析**: 分析句子结构和语法点
- **情景对话**: 模拟真实场景的对话练习
- **个性化指导**: 基于用户水平的学习建议
- **多模态交互**: 支持文本和语音交互
- **学习记录**: 保存对话历史和学习轨迹

### 1.3 适用场景
- 日语学习过程中的疑问解答
- 语法点的深入理解和练习
- 口语对话能力的提升
- 个性化学习计划的制定

## 2. 使用指南

### 2.1 智能问答
1. 在AI助教页面输入日语学习相关问题
2. 选择问题类型（词汇、语法、文化等）
3. 查看AI的详细解答和例句
4. 进行追问或深入讨论

### 2.2 语法解析
1. 输入需要分析的日语句子
2. 选择"语法解析"功能
3. 查看句子结构分析结果
4. 了解语法点的用法和变化

### 2.3 情景对话
1. 选择对话场景（餐厅、机场、购物等）
2. 设定难度等级（N5-N1）
3. 开始与AI进行角色扮演对话
4. 获得发音和表达的反馈

### 2.4 学习建议
1. 描述当前学习状况和目标
2. AI分析学习数据和进度
3. 获得个性化的学习建议
4. 制定具体的学习计划

## 3. 界面详解

### 3.1 AI助教主页面 (`/ai-tutor`)

#### 页面布局
- **左侧**: 功能选择和历史对话
- **中部**: 对话区域和输入框
- **右侧**: 学习资源和快捷操作

#### 功能选择面板
| 功能类型 | 图标 | 说明 | 对应AI提示 |
|----------|------|------|------------|
| 智能问答 | 💬 | 一般性日语学习问题 | 通用助教模式 |
| 语法解析 | 📝 | 句子结构和语法分析 | 语法专家模式 |
| 情景对话 | 🎭 | 角色扮演对话练习 | 对话伙伴模式 |
| 学习指导 | 🎯 | 个性化学习建议 | 学习顾问模式 |

#### 对话历史侧栏
| 显示项 | 数据源 | 说明 |
|--------|--------|------|
| 对话标题 | 自动生成或用户命名 | 对话主题摘要 |
| 创建时间 | chat_sessions.created_at | 对话开始时间 |
| 消息数量 | chat_messages计数 | 对话轮次统计 |
| 功能类型 | chat_sessions.session_type | 对话类型标识 |

### 3.2 对话界面

#### 消息显示区域
| 消息类型 | 显示样式 | 数据库字段 | 说明 |
|----------|----------|------------|------|
| 用户消息 | 右侧气泡，蓝色 | chat_messages.content | 用户输入内容 |
| AI回复 | 左侧气泡，灰色 | chat_messages.content | AI生成回复 |
| 系统提示 | 居中，小字体 | chat_messages.metadata | 功能切换提示 |
| 错误信息 | 红色边框 | - | 错误状态显示 |

#### 输入区域
| 组件 | 功能 | 实现方式 |
|------|------|----------|
| 文本输入框 | 用户消息输入 | Textarea自动调整高度 |
| 发送按钮 | 提交消息 | 支持Enter快捷键 |
| 语音按钮 | 语音输入 | Web Speech API |
| 附件按钮 | 图片上传 | 文件选择器 |
| 清空按钮 | 清除输入 | 重置输入状态 |

### 3.3 情景对话设置

#### 场景选择
| 场景名称 | 难度等级 | 角色设定 | 学习目标 |
|----------|----------|----------|----------|
| 餐厅点餐 | N5-N4 | 顾客/服务员 | 基础日常对话 |
| 机场办理登机 | N4-N3 | 乘客/工作人员 | 旅行相关词汇 |
| 购物消费 | N4-N3 | 顾客/店员 | 商务交流 |
| 医院就诊 | N3-N2 | 患者/医生 | 专业词汇 |
| 求职面试 | N2-N1 | 求职者/面试官 | 正式敬语 |
| 商务会议 | N2-N1 | 参会者 | 商务日语 |

#### 对话参数设置
| 参数 | 选项 | 说明 | 影响 |
|------|------|------|------|
| 难度等级 | N5-N1 | 词汇和语法复杂度 | AI回复的语言难度 |
| 对话长度 | 短/中/长 | 预期对话轮次 | 5/10/20轮 |
| 纠错模式 | 实时/延后 | 错误纠正时机 | 学习体验 |
| 文化背景 | 开启/关闭 | 是否包含文化解释 | 回复内容丰富度 |

### 3.4 语法解析界面

#### 输入区域
- **句子输入框**: 支持日语输入法
- **分析按钮**: 触发语法分析
- **示例按钮**: 加载预设例句

#### 分析结果展示
| 分析维度 | 显示内容 | 数据来源 | 说明 |
|----------|----------|----------|------|
| 词汇分解 | 词汇+读音+词性 | AI分析结果 | 逐词解析 |
| 语法结构 | 句子成分标注 | AI分析结果 | 语法树形图 |
| 语法点 | 关键语法说明 | grammar_points表 | 相关语法点 |
| 翻译 | 中文/英文翻译 | AI生成 | 整句理解 |
| 变化形式 | 动词变位等 | AI分析 | 语法变化 |

## 4. 技术实现

### 4.1 核心代码文件

#### AI助教组件
- **文件**: `src/components/AITutor.tsx`
- **功能**: AI助教主界面组件
- **主要功能**:
  - 对话界面渲染
  - 消息发送和接收
  - 功能模式切换

#### AI服务集成
- **文件**: `src/ai/flows/tutor-chat-flow.ts`
- **功能**: AI对话流程管理
- **主要功能**:
  - Genkit框架集成
  - 对话上下文管理
  - 提示词模板管理

#### 语法分析流程
- **文件**: `src/ai/flows/grammar-analysis-flow.ts`
- **功能**: 语法分析专用流程
- **主要功能**:
  - 句子结构分析
  - 语法点识别
  - 分析结果格式化

#### 对话管理服务
- **文件**: `src/lib/server/chat-service.ts`
- **功能**: 对话数据管理
- **主要方法**:
  - `createChatSession()`: 创建对话会话
  - `addMessage()`: 添加消息记录
  - `getChatHistory()`: 获取对话历史

### 4.2 数据库表结构

#### chat_sessions表（对话会话）
```sql
CREATE TABLE chat_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  session_type TEXT NOT NULL, -- 'qa', 'grammar', 'scenario', 'guidance'
  title TEXT,
  scenario_config TEXT, -- JSON格式的场景配置
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### chat_messages表（对话消息）
```sql
CREATE TABLE chat_messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id INTEGER NOT NULL,
  role TEXT NOT NULL, -- 'user', 'assistant', 'system'
  content TEXT NOT NULL,
  metadata TEXT, -- JSON格式的额外信息
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
);
```

#### ai_analysis_cache表（分析结果缓存）
```sql
CREATE TABLE ai_analysis_cache (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  input_text TEXT NOT NULL,
  analysis_type TEXT NOT NULL, -- 'grammar', 'vocabulary', 'translation'
  result_json TEXT NOT NULL,
  model_version TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(input_text, analysis_type)
);
```

### 4.3 AI集成架构

#### Genkit配置
```typescript
// src/ai/genkit.ts
export const ai = genkit({
  plugins: [
    googleAI({ apiKey: process.env.GEMINI_API_KEY }),
  ],
});
```

#### 对话流程定义
```typescript
// 助教对话流程
export const tutorChatFlow = ai.defineFlow(
  {
    name: 'tutorChat',
    inputSchema: TutorChatInputSchema,
    outputSchema: TutorChatOutputSchema,
  },
  async (input) => {
    const { output } = await ai.generate({
      model: input.modelName || 'googleai/gemini-2.5-flash',
      messages: formatMessages(input.history, input.message),
      system: getTutorSystemPrompt(input.mode),
    });
    
    return { response: output };
  }
);
```

#### 系统提示词模板
```typescript
const SYSTEM_PROMPTS = {
  qa: `你是Sakura（小樱），一位友善的日语学习助教AI。
       你正在帮助中文用户学习日语。
       请用简体中文回答问题，保持鼓励和清晰的语调。`,
       
  grammar: `你是日语语法专家。请分析用户提供的日语句子，
           包括词汇分解、语法结构、语法点说明等。
           用中文详细解释，并提供相关例句。`,
           
  scenario: `你正在进行日语情景对话练习。
            根据设定的场景和角色，用自然的日语对话。
            适当纠正用户的错误，并给出改进建议。`
};
```

### 4.4 API接口

#### 发送消息
- **路径**: `POST /api/ai/chat`
- **参数**: 
```json
{
  "sessionId": "string",
  "message": "string",
  "mode": "qa|grammar|scenario|guidance",
  "config": {}
}
```
- **返回**: AI回复内容

#### 语法分析
- **路径**: `POST /api/ai/analyze-grammar`
- **参数**: `{text: string}`
- **返回**: 语法分析结果

#### 获取对话历史
- **路径**: `GET /api/ai/chat/[sessionId]`
- **返回**: 对话消息列表

#### 创建对话会话
- **路径**: `POST /api/ai/chat/session`
- **参数**: `{type: string, title?: string, config?: object}`
- **返回**: 新会话ID

## 5. 配置说明

### 5.1 AI模型配置

#### 模型选择
```env
# 默认使用的AI模型
DEFAULT_AI_MODEL="googleai/gemini-2.5-flash"

# API密钥配置
GEMINI_API_KEY="your-api-key"
```

#### 生成参数
```typescript
const GENERATION_CONFIG = {
  temperature: 0.7, // 创造性程度
  maxOutputTokens: 2048, // 最大输出长度
  topP: 0.8, // 核采样参数
  topK: 40 // Top-K采样参数
};
```

### 5.2 对话配置

#### 上下文管理
- **最大历史消息数**: 20条
- **上下文窗口**: 4000 tokens
- **会话超时**: 30分钟无活动自动结束

#### 安全过滤
- **内容过滤**: 启用Google AI安全过滤
- **敏感词检测**: 自定义敏感词列表
- **输出长度限制**: 最大2048字符

## 6. 故障排除

### 6.1 常见问题

#### AI回复缓慢或超时
**症状**: 发送消息后长时间无响应
**解决方案**:
1. 检查API密钥是否有效
2. 确认网络连接稳定
3. 检查API配额是否用完
4. 减少输入文本长度

#### 语法分析结果不准确
**症状**: 分析结果与预期不符
**解决方案**:
1. 检查输入文本格式
2. 尝试更换AI模型
3. 调整提示词模板
4. 增加示例训练数据

#### 对话历史丢失
**症状**: 刷新页面后对话记录消失
**解决方案**:
1. 检查会话ID是否正确保存
2. 确认数据库连接正常
3. 检查用户认证状态
4. 清除浏览器缓存重试

### 6.2 性能优化

#### 响应速度优化
```typescript
// 实现流式响应
export async function streamChatResponse(input: ChatInput) {
  const stream = await ai.generateStream({
    model: input.model,
    messages: input.messages,
    system: input.systemPrompt
  });
  
  return new ReadableStream({
    async start(controller) {
      for await (const chunk of stream) {
        controller.enqueue(chunk);
      }
      controller.close();
    }
  });
}
```

#### 缓存策略
```typescript
// 语法分析结果缓存
const cacheKey = `grammar_${hash(inputText)}`;
const cached = await redis.get(cacheKey);
if (cached) {
  return JSON.parse(cached);
}

const result = await analyzeGrammar(inputText);
await redis.setex(cacheKey, 3600, JSON.stringify(result));
return result;
```

---

*文档版本: v1.0*
*最后更新: 2024年12月*
