# 项目结构说明

## 根目录结构

```
nhk-japanese-learning/
├── docs/                     # 项目文档目录
├── src/                      # Next.js应用源代码
├── server/                   # 遗留后端工具
├── scripts/                  # 脚本文件
├── prisma/                   # Prisma ORM配置
├── data/                     # 数据库文件
├── public/                   # 静态资源
├── package.json              # 项目配置和依赖
├── next.config.mjs          # Next.js构建配置
├── tailwind.config.js       # Tailwind CSS配置
├── tsconfig.json            # TypeScript配置
└── README.md                # 项目说明文档
```

## Next.js应用结构 (src/)

### App Router结构
- **app/layout.tsx** - 根布局组件，全局样式和提供者
- **app/(main)/layout.tsx** - 主应用布局，包含侧边栏和头部
- **app/actions.ts** - Server Actions，处理服务端逻辑
- **app/globals.css** - 全局样式文件，引入Tailwind CSS

### 组件目录 (src/components/)

#### 核心组件
- **Header.tsx** - 顶部导航栏
  - 搜索功能
  - 用户信息显示
  - 登录/登出菜单
  - 响应式设计

- **Sidebar.tsx** - 侧边栏导航
  - 功能模块导航
  - 学习级别选择
  - 学习进度显示
  - 可折叠设计

- **LoginPage.tsx** - 用户登录页面
  - 用户认证表单
  - 测试账号提示
  - 错误处理显示

#### 功能模块组件

**新闻阅读模块**
- **NewsReader.tsx** - 新闻列表页面
  - 文章列表展示
  - 搜索和筛选功能
  - 分页和加载状态
  
- **NewsCard.tsx** - 新闻卡片组件
  - 文章信息展示
  - 交互按钮（收藏、播放等）
  - 响应式布局
  
- **ArticleReader.tsx** - 文章阅读器
  - 文章详细内容
  - 词汇标注和解释
  - 语法点分析
  - 翻译功能

**学习管理模块**
- **Dashboard.tsx** - 学习统计面板
  - 学习进度可视化
  - 成就展示
  - 活动统计

- **VocabularyBank.tsx** - 生词本管理
  - 词汇列表展示
  - 学习状态管理
  - 搜索和筛选

- **AITutor.tsx** - AI助教功能
  - 智能问答界面
  - 情景对话练习
  - 语法解析工具

**系统管理模块**
- **RSSSourceManager.tsx** - RSS源管理
  - RSS源配置
  - 字段映射设置
  - 测试和验证功能

- **EnhancedScraperManager.tsx** - 抓取管理
  - 抓取任务控制
  - 进度监控
  - AI处理队列管理

- **Settings.tsx** - 系统设置
  - 用户偏好设置
  - 显示选项配置
  - 数据管理功能

### 工具目录 (src/utils/)

- **auth.js** - 用户认证管理
  - 登录状态管理
  - 用户信息存储
  - 权限验证

- **database-api.js** - 数据库API客户端
  - 统一的数据访问接口
  - HTTP请求封装
  - 错误处理

- **rss-api.js** - RSS测试API客户端
  - RSS源测试功能
  - 批量测试支持

- **scraping-api.js** - 抓取管理API客户端
  - 抓取任务管理
  - 进度查询接口

## 后端源代码结构 (src/lib/server/)

### 服务端工具库

- **scraper.ts** - 增强版抓取器
  - RSS内容抓取
  - 媒体文件下载
  - AI处理调度
  - 反爬虫机制
  - HLS视频处理
  - 使用TypeScript和Prisma ORM

- **rss-parser.ts** - RSS解析器
  - XML内容解析
  - 字段映射处理
  - 数据转换规则
  - XPath和CSS选择器支持
  - TypeScript类型安全

- **anti-crawler.ts** - 反爬虫管理器
  - 请求频率控制
  - 用户代理轮换
  - 智能延迟机制
  - 错误重试策略
  - TypeScript实现

- **database.ts** - 数据库管理器
  - 统一的Prisma数据库访问层
  - 完整的CRUD操作
  - 事务处理
  - 备份和恢复功能

## 脚本目录 (scripts/)

### 数据库脚本
- **init-new-db-optimized.js** - 优化版数据库初始化
  - 创建完整的数据库结构
  - 插入默认配置数据
  - 性能优化设置

- **init-db.js** - 基础数据库初始化
  - 创建基本表结构
  - 兼容性支持

### 抓取脚本
- **enhanced-scraper.js** - 命令行抓取工具
  - 独立运行的抓取器
  - 支持批量抓取
  - 详细日志输出

## 数据目录 (data/)

- **nhk_news_new.db** - 主数据库文件
- **nhk_news_backup_*.db** - 数据库备份文件
- **nhk_news.db** - 旧版数据库文件（兼容性）

## 静态资源目录 (public/)

```
public/
├── media/                    # 媒体文件存储
│   ├── images/              # 图片文件
│   ├── videos/              # 视频文件
│   └── audios/              # 音频文件
└── favicon.ico              # 网站图标
```

## 配置文件说明

### package.json
- 项目元信息和依赖管理
- npm脚本定义
- 开发和生产依赖配置

### next.config.mjs
- Next.js构建配置
- 服务器配置
- 媒体文件处理设置

### tailwind.config.js
- Tailwind CSS配置
- 自定义主题设置
- 内容路径配置

### tsconfig.json
- TypeScript编译配置
- 类型检查规则
- 模块解析设置

### eslint.config.js
- ESLint代码检查配置
- React和TypeScript规则
- 代码质量标准

## 文件命名规范

### 组件文件
- 使用PascalCase命名（如：NewsReader.tsx）
- 文件名与组件名保持一致
- 使用.tsx扩展名（包含JSX的TypeScript文件）

### 工具文件
- 使用kebab-case命名（如：database-api.js）
- 使用.js扩展名（纯JavaScript文件）
- 功能相关的文件归类到同一目录

### 配置文件
- 使用标准的配置文件名
- 遵循相应工具的命名约定
- 包含必要的配置注释

## 模块依赖关系

```
app/layout.tsx (根布局)
├── app/(main)/layout.tsx (主布局)
│   ├── Header.tsx
│   ├── Sidebar.tsx
│   └── 页面组件
│       ├── NewsReader.tsx
│       │   ├── NewsCard.tsx
│       │   └── ArticleReader.tsx
│       ├── Dashboard.tsx
│       ├── VocabularyBank.tsx
│       ├── AITutor.tsx
│       ├── RSSSourceManager.tsx
│       ├── EnhancedScraperManager.tsx
│       └── UnifiedSettings.tsx
└── app/actions.ts (Server Actions)

工具模块
├── lib/server/database.ts (Prisma数据库)
├── lib/server/scraper.ts (抓取器)
├── lib/auth.ts (认证)
└── utils/ (客户端工具)
```

这种模块化的结构设计使得：
- 代码易于维护和扩展
- 功能模块相对独立
- 便于团队协作开发
- 支持渐进式功能添加