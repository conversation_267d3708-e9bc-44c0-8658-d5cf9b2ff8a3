
# 视频异步下载与手动切换方案

## 1. 需求背景

当前系统的视频下载存在以下痛点：
- **耗时过长**：视频下载和转码是I/O密集型任务，严重拖慢了主新闻抓取流程的整体速度。
- **可靠性问题**：网络波动或视频源问题可能导致下载失败，影响整个抓取任务的成功率。
- **缺乏验证**：系统自动下载的视频文件可能存在损坏或无法播放的情况，用户无法在切换前进行验证。
- **灵活性不足**：用户无法控制何时执行下载，也无法在下载失败后轻松重试。

为解决以上问题，我们提出将视频下载功能从主抓取流程中解耦，改造为一个用户可控的、类似AI处理的异步后台任务。

## 2. 核心设计思想

- **解耦 (Decoupling)**：将“视频元数据提取”与“视频文件下载”两个阶段完全分离。抓取阶段只负责快速提取信息，下载阶段则在后台独立执行。
- **异步化 (Asynchronization)**：视频下载将作为一个独立的、非阻塞的后台任务队列存在，用户可以在系统空闲时手动启动。
- **用户控制 (User Control)**：赋予用户最终的控制权。用户可以选择下载全部或部分视频，并在下载后先预览，再手动确认是否将播放源从“远程内嵌网页”切换为“本地视频文件”。

## 3. 数据库结构变更

为了支持此方案，我们需要对 `articles` 表进行扩展。

### `articles` 表 - 新增/修改字段

```sql
CREATE TABLE articles (
  -- ... (现有字段保持不变) ...
  
  -- 原有视频字段的用途变更
  video_url TEXT,                        -- 将始终存储原始的、可内嵌的播放器页面URL (e.g., .../movie.html)
  video_path TEXT,                       -- 保持不变，用于存储本地视频文件的路径 (初始为NULL)

  -- 新增字段
  video_metadata_json TEXT,              -- [新增] 用于存储视频下载所需的元数据 (如M3U8 URL、视频标题等)，以JSON格式保存
  video_m3u8_content TEXT,               -- [新增] 用于存储抓取到的M3U8播放列表的完整文本内容
  video_download_status TEXT DEFAULT 'disabled', -- [新增] 视频下载状态 (disabled, pending, downloading, completed, failed)
  use_local_video BOOLEAN DEFAULT 0,     -- [新增] 是否使用本地视频文件进行播放的标志 (0: 否, 1: 是)
  
  -- ... (其他现有字段保持不变) ...
);
```

**字段说明:**
- `video_metadata_json`: 存储视频下载所需的轻量级元数据，如M3U8的URL和视频标题。
- `video_m3u8_content`: [核心优化] 存储M3U8播放列表的完整文本，确保了即使原始URL失效，下载任务依然可以基于此内容执行。
- `video_download_status`: 追踪每个视频的下载进度，方便UI展示和后台任务筛选。
- `use_local_video`: 这是用户控制的核心。只有当用户手动确认为`1`后，前端播放器才会使用`video_path`中的本地文件。

## 4. 抓取流程变更 (`EnhancedNHKScraper`)

新闻抓取器的逻辑将进行以下调整：

1.  **停止立即下载**：在 `processRSSItem` 或 `scrapeFullArticle` 方法中，当检测到视频时，**不再**调用视频下载函数 (`downloadAndRemux`)。
2.  **专注于信息提取**：抓取器将完整执行视频元数据的提取流程：
    -   从新闻页找到内嵌播放器页面的URL (`.../movie.html`)。
    -   访问播放器页面，找到视频信息JSON文件的URL。
    -   访问JSON文件，解析出真实的M3U8播放列表URL和视频标题。
    -   **新增**：访问M3U8播放列表URL，获取其**文件内容**。
3.  **保存元数据 (分离存储)**：
    -   将原始的“内嵌播放器页面URL”保存到 `video_url` 字段。
    -   将提取到的“M3U8播放列表URL”和“视频标题”等信息，作为一个JSON对象存入 `video_metadata_json` 字段。
    -   将获取到的**M3U8文件内容**，作为一个独立的文本字符串存入新增的 `video_m3u8_content` 字段。
    -   将 `video_download_status` 设置为 `'pending'` (如果视频存在)，否则为`'disabled'`。
    -   `video_path` 字段保持为 `NULL`。

这样改造后，抓取新闻的流程将变得非常迅速，因为它不再需要等待任何视频文件下载完成。

## 5. 后端逻辑变更 - 视频下载器

我们将创建一个新的后台服务逻辑 (Server Action)，专门负责处理视频下载任务。

1.  **任务查询**：下载器启动后，会查询 `articles` 表，可以筛选出所有 `video_download_status` 为 `'pending'` 或 `'failed'` 的记录，也可以根据用户指定的ID列表进行筛选。
2.  **执行下载**：对每一个任务，下载器会：
    -   将该文章的 `video_download_status` 更新为 `'downloading'`。
    -   从 `video_m3u8_content` 和 `video_metadata_json` 字段中读取所需信息。
    -   调用 `downloadAndRemux` 函数，基于M3U8内容执行下载和转码。
    -   **成功时**：将生成的本地文件路径更新到 `video_path` 字段，并将 `video_download_status` 更新为 `'completed'`。
    -   **失败时**：将 `video_download_status` 更新为 `'failed'`，并将错误信息记录到日志系统。

## 6. 前端UI变更

### 抓取管理页面 (`EnhancedScraperManager.tsx`) - 视频下载标签页

-   增加一个新的管理标签页，名为“**视频下载**”。
-   **上部操作区**:
    -   一个“**下载全部待处理**”按钮，用于触发所有待处理视频的后台下载任务。
    -   一个“**下载选中项**”按钮。
-   **下部列表区**:
    -   一个**分页**的表格或列表，显示所有包含视频的文章。
    -   每一行包含：**多选框**、文章标题、视频下载状态（待处理/下载中/已完成/失败）。
    -   用户勾选后，点击“下载选中项”按钮，即可启动对特定视频的下载。

### 文章阅读器页面 (`ArticleReader.tsx`)

这是用户体验的核心部分，需要精细设计：

1.  **默认播放方式**：播放器将首先检查 `use_local_video` 字段。
    -   如果为 `0` (默认值)，则使用 `<iframe>` 标签，src指向 `video_url` 字段，播放原始的内嵌网页视频。
2.  **本地视频选项**：
    -   当 `video_download_status` 为 `'completed'` 且 `video_path` 有值时，在视频播放器旁边或下方，显示一个新的UI区域，标题为“**本地视频已就绪**”。
    -   此区域包含：
        -   一个使用 `<video>` 标签的 **预览播放器**，src指向 `video_path`，让用户可以先检查下载的文件是否能正常播放。
        -   一个醒目的确认按钮：“**✅ 确认可用，切换为本地播放**”。
3.  **手动切换逻辑**：
    -   当用户点击确认按钮后，会调用一个新的Server Action。
    -   该Action将对应文章的 `use_local_video` 字段更新为 `1`。
    -   页面刷新或数据重新获取后，由于 `use_local_video` 变为 `1`，主播放器现在会自动使用 `<video>` 标签播放 `video_path` 中的本地文件。

## 7. 批量视频源管理界面 (新功能)

为了方便用户管理大量视频，我们将创建一个新的管理页面或在现有页面增加批量管理功能。

### 7.1 页面设计
- **位置**: 可以是侧边栏的一个新菜单项，例如“视频管理”，或在“抓取管理”页面增加新标签页。
- **布局**: 一个支持分页的表格或列表。
- **核心列**:
    - `文章标题`: 显示新闻标题，并可点击跳转到阅读页。
    - `下载状态`: 显示视频的下载状态（待处理/下载中/已完成/失败）。
    - `当前播放源`: 显示当前是“远程内嵌”还是“本地视频”。
    - `操作`:
        - 一个 **“切换播放源”** 的开关 (Toggle Switch)。用户可以随时切换。
        - 一个“预览”按钮，可以在弹窗中播放本地视频进行检查。
        - 一个“重新下载”按钮，用于失败的任务。

### 7.2 批量操作
- 在表格的表头提供一个“全选”复选框。
- 在表格的顶部提供操作按钮，例如：
    - “**批量切换为本地视频**”
    - “**批量切换为远程视频**”
- 这些按钮将调用 `batchUpdateVideoSourceAction` 来一次性更新多篇文章的 `use_local_video` 字段。

### 7.3 后端支持
- 需要一个新的 `dbManager` 函数来获取所有包含视频的文章列表，并支持分页和筛选。
- 需要实现 `batchUpdateVideoSourceAction` 和 `setUseLocalVideoAction` Server Action。

通过此方案，我们可以将视频处理的复杂性从主流程中完全移除，同时为用户提供无与伦比的灵活性和控制力，完美地解决了当前面临的所有痛点。
