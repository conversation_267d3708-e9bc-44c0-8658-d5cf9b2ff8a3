'use client';
import React from 'react';

interface HeatmapDay {
  date: string;
  intensity: number;
  count: number;
  dayOfWeek: number;
}

interface ActivityHeatmapProps {
  data: (HeatmapDay | null)[][];
  loading?: boolean;
}

const ActivityHeatmap: React.FC<ActivityHeatmapProps> = ({ data, loading = false }) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">学习活动热力图</h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="grid grid-cols-53 gap-1">
            {Array.from({ length: 371 }, (_, i) => (
              <div key={i} className="w-3 h-3 bg-gray-200 rounded-sm"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">学习活动热力图</h3>
        <p className="text-gray-500 text-center py-8">暂无学习活动数据</p>
      </div>
    );
  }

  // 生成月份标签
  const generateMonthLabels = () => {
    const months = [];
    const today = new Date();
    for (let i = 11; i >= 0; i--) {
      const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
      months.push(date.toLocaleDateString('zh-CN', { month: 'short' }));
    }
    return months;
  };

  const monthLabels = generateMonthLabels();

  // 计算总活动数
  const totalActivities = data.flat().reduce((sum, day) => {
    return sum + (day?.count || 0);
  }, 0);

  // 计算最大活动数
  const maxActivities = data.flat().reduce((max, day) => {
    return Math.max(max, day?.count || 0);
  }, 0);

  // 计算活跃天数
  const activeDays = data.flat().filter(day => day && day.count > 0).length;

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">学习活动热力图</h3>
          <p className="text-sm text-gray-600 mt-1">过去一年的学习活跃度</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">
            <span className="font-medium text-gray-900">{activeDays}</span> 个活跃天数
          </div>
          <div className="text-sm text-gray-500">
            <span className="font-medium text-gray-900">{totalActivities}</span> 次学习活动
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        {/* 月份标签 */}
        <div className="flex mb-2 text-xs text-gray-500">
          <div className="w-8"></div> {/* 星期标签的空间 */}
          {monthLabels.map((month, index) => (
            <div key={index} className="flex-1 text-center min-w-[40px]">
              {month}
            </div>
          ))}
        </div>
        
        <div className="flex">
          {/* 星期标签 */}
          <div className="flex flex-col text-xs text-gray-500 mr-2">
            <div className="h-3 mb-1"></div> {/* 对齐第一行 */}
            <div className="h-3 mb-1">周一</div>
            <div className="h-3 mb-1"></div>
            <div className="h-3 mb-1">周三</div>
            <div className="h-3 mb-1"></div>
            <div className="h-3 mb-1">周五</div>
            <div className="h-3 mb-1"></div>
          </div>
          
          {/* 热力图网格 */}
          <div className="flex gap-1">
            {data.map((week, weekIndex) => (
              <div key={weekIndex} className="flex flex-col gap-1">
                {week.map((day, dayIndex) => (
                  <div
                    key={dayIndex}
                    className={`w-3 h-3 rounded-sm transition-all duration-200 hover:ring-2 hover:ring-indigo-300 hover:ring-opacity-50 cursor-pointer ${
                      !day ? 'bg-transparent' :
                      day.intensity === 0 ? 'bg-gray-100 hover:bg-gray-200' :
                      day.intensity === 1 ? 'bg-green-100 hover:bg-green-200' :
                      day.intensity === 2 ? 'bg-green-200 hover:bg-green-300' :
                      day.intensity === 3 ? 'bg-green-400 hover:bg-green-500' :
                      'bg-green-600 hover:bg-green-700'
                    }`}
                    title={day ? `${day.date}: ${day.count} 项学习活动` : ''}
                  />
                ))}
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-4 text-xs text-gray-500">
          <span>一年前</span>
          <div className="flex items-center space-x-2">
            <span>少</span>
            <div className="flex items-center space-x-1">
              <div
                className="w-3 h-3 bg-gray-100 rounded-sm cursor-help transition-all duration-200 hover:ring-2 hover:ring-gray-300 hover:ring-opacity-50"
                title="无学习活动 (0个)"
              ></div>
              <div
                className="w-3 h-3 bg-green-100 rounded-sm cursor-help transition-all duration-200 hover:ring-2 hover:ring-green-300 hover:ring-opacity-50"
                title="轻度活跃 (1-10个学习活动)"
              ></div>
              <div
                className="w-3 h-3 bg-green-200 rounded-sm cursor-help transition-all duration-200 hover:ring-2 hover:ring-green-300 hover:ring-opacity-50"
                title="中度活跃 (11-20个学习活动)"
              ></div>
              <div
                className="w-3 h-3 bg-green-400 rounded-sm cursor-help transition-all duration-200 hover:ring-2 hover:ring-green-500 hover:ring-opacity-50"
                title="高度活跃 (21-30个学习活动)"
              ></div>
              <div
                className="w-3 h-3 bg-green-600 rounded-sm cursor-help transition-all duration-200 hover:ring-2 hover:ring-green-700 hover:ring-opacity-50"
                title="极度活跃 (31+个学习活动)"
              ></div>
            </div>
            <span>多</span>
          </div>
          <span>今天</span>
        </div>
      </div>

      {/* 统计摘要 */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-green-600">{activeDays}</div>
            <div className="text-xs text-gray-500">活跃天数</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-blue-600">{totalActivities}</div>
            <div className="text-xs text-gray-500">总活动数</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-purple-600">{maxActivities}</div>
            <div className="text-xs text-gray-500">单日最高</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-indigo-600">
              {activeDays > 0 ? Math.round((totalActivities / activeDays) * 10) / 10 : 0}
            </div>
            <div className="text-xs text-gray-500">日均活动</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityHeatmap;
