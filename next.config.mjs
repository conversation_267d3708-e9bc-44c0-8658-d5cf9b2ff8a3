/** @type {import('next').NextConfig} */
const nextConfig = {
    serverExternalPackages: ['sqlite3', 'xmldom', 'xpath'],
    eslint: {
        // 禁用构建时的 ESLint 检查，避免过时配置选项的错误
        ignoreDuringBuilds: true,
    },
    async headers() {
        return [
            {
                source: '/media/videos/:path*',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable',
                    },
                ],
            },
            {
                source: '/media/videos/:path*.m3u8',
                headers: [
                    {
                        key: 'Content-Type',
                        value: 'application/vnd.apple.mpegurl',
                    },
                    {
                        key: 'Access-Control-Allow-Origin',
                        value: '*',
                    },
                ],
            },
            {
                source: '/media/videos/:path*.ts',
                headers: [
                    {
                        key: 'Content-Type',
                        value: 'video/mp2t',
                    },
                    {
                        key: 'Access-Control-Allow-Origin',
                        value: '*',
                    },
                ],
            },
        ];
    },
};

export default nextConfig;
