import { dbManager } from './database';

interface ApiKey {
  id: number;
  name: string;
  api_key: string;
  provider: string;
  priority: number;
  is_active: boolean;
  last_used_at: Date | null;
  last_error: string | null;
  quota_reset_interval_minutes: number;
  min_usage_interval_seconds: number;
  created_at: Date;
}

class APIKeyManager {
  private static instance: APIKeyManager;
  private keyRotationIndex: number = 0;
  private lastKeyRefresh: number = 0;
  private keyRefreshInterval: number = 30000; // 30秒刷新一次key列表
  private cachedKeys: ApiKey[] = [];

  private constructor() {}

  static getInstance(): APIKeyManager {
    if (!APIKeyManager.instance) {
      APIKeyManager.instance = new APIKeyManager();
    }
    return APIKeyManager.instance;
  }

  /**
   * 获取下一个可用的API Key
   * 按照优先级和使用频率进行轮询
   */
  async getNextAvailableKey(provider: string = 'google_gemini'): Promise<ApiKey | null> {
    await this.refreshKeysIfNeeded();

    const activeKeys = this.cachedKeys.filter(key =>
      key.is_active &&
      key.provider === provider &&
      !this.isKeyRecentlyFailed(key) &&
      !this.isKeyTooRecentlyUsed(key)
    );

    if (activeKeys.length === 0) {
      console.warn(`No active API keys available for provider: ${provider}`);
      return null;
    }

    // 按优先级分组
    const keysByPriority = this.groupKeysByPriority(activeKeys);

    // 从最高优先级组中选择最少使用的key
    const highestPriorityKeys = keysByPriority[0];
    const selectedKey = this.selectLeastUsedKey(highestPriorityKeys);

    return selectedKey;
  }

  /**
   * 获取所有可用的API Keys（用于并行处理）
   */
  async getAllAvailableKeys(provider: string = 'google_gemini'): Promise<ApiKey[]> {
    await this.refreshKeysIfNeeded();

    return this.cachedKeys.filter(key =>
      key.is_active &&
      key.provider === provider &&
      !this.isKeyRecentlyFailed(key) &&
      !this.isKeyTooRecentlyUsed(key)
    ).sort((a, b) => {
      // 先按优先级排序，再按使用时间排序
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      const aTime = a.last_used_at?.getTime() || 0;
      const bTime = b.last_used_at?.getTime() || 0;
      return aTime - bTime;
    });
  }

  /**
   * 标记API Key使用
   */
  async markKeyUsed(keyId: number, error?: string): Promise<void> {
    try {
      await dbManager.updateApiKeyUsage(keyId, error);
      
      // 更新缓存中的使用时间
      const cachedKey = this.cachedKeys.find(k => k.id === keyId);
      if (cachedKey) {
        cachedKey.last_used_at = new Date();
        cachedKey.last_error = error || null;
      }
    } catch (err) {
      console.error('Failed to update API key usage:', err);
    }
  }

  /**
   * 检查key是否最近失败过
   */
  private isKeyRecentlyFailed(key: ApiKey): boolean {
    if (!key.last_error) return false;

    // 如果是配额错误，使用key自定义的重置间隔
    const isQuotaError = key.last_error.includes('429') ||
                        key.last_error.includes('quota') ||
                        key.last_error.includes('resource has been exhausted');

    if (isQuotaError && key.last_used_at) {
      // 使用key配置的配额重置间隔
      const resetIntervalMs = key.quota_reset_interval_minutes * 60 * 1000;
      const resetTime = new Date(Date.now() - resetIntervalMs);
      return key.last_used_at > resetTime;
    }

    return false;
  }

  /**
   * 检查key是否使用间隔太短
   */
  private isKeyTooRecentlyUsed(key: ApiKey): boolean {
    if (!key.last_used_at) return false;

    // 使用key配置的最小使用间隔
    const minIntervalMs = key.min_usage_interval_seconds * 1000;
    const minTime = new Date(Date.now() - minIntervalMs);
    return key.last_used_at > minTime;
  }

  /**
   * 按优先级分组keys
   */
  private groupKeysByPriority(keys: ApiKey[]): ApiKey[][] {
    const groups: { [priority: number]: ApiKey[] } = {};
    
    keys.forEach(key => {
      if (!groups[key.priority]) {
        groups[key.priority] = [];
      }
      groups[key.priority].push(key);
    });

    // 按优先级排序返回
    return Object.keys(groups)
      .map(p => parseInt(p))
      .sort((a, b) => a - b)
      .map(priority => groups[priority]);
  }

  /**
   * 从同优先级的keys中选择最少使用的
   */
  private selectLeastUsedKey(keys: ApiKey[]): ApiKey {
    return keys.reduce((least, current) => {
      const leastTime = least.last_used_at?.getTime() || 0;
      const currentTime = current.last_used_at?.getTime() || 0;
      return currentTime < leastTime ? current : least;
    });
  }

  /**
   * 刷新缓存的keys（如果需要）
   */
  private async refreshKeysIfNeeded(): Promise<void> {
    const now = Date.now();
    if (now - this.lastKeyRefresh > this.keyRefreshInterval) {
      try {
        this.cachedKeys = await dbManager.getActiveApiKeys() as ApiKey[];
        this.lastKeyRefresh = now;
      } catch (error) {
        console.error('Failed to refresh API keys:', error);
        // 如果刷新失败，继续使用缓存的keys
      }
    }
  }

  /**
   * 强制刷新keys缓存
   */
  async forceRefreshKeys(): Promise<void> {
    try {
      this.cachedKeys = await dbManager.getActiveApiKeys() as ApiKey[];
      this.lastKeyRefresh = Date.now();
    } catch (error) {
      console.error('Failed to force refresh API keys:', error);
      throw error;
    }
  }

  /**
   * 获取API Key统计信息
   */
  async getKeyStats(): Promise<{ total: number; active: number; failed: number }> {
    await this.refreshKeysIfNeeded();
    
    const total = this.cachedKeys.length;
    const active = this.cachedKeys.filter(k => k.is_active && !this.isKeyRecentlyFailed(k)).length;
    const failed = this.cachedKeys.filter(k => this.isKeyRecentlyFailed(k)).length;
    
    return { total, active, failed };
  }
}

// 导出单例实例
export const apiKeyManager = APIKeyManager.getInstance();
