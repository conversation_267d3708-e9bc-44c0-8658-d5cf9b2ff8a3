# AI提示词测试页面使用指南

## 🎯 功能概述

AI提示词测试页面是一个专门用于测试和验证AI分析功能稳定性的管理工具，帮助解决AI输出不稳定的问题（如例句有时有注音，有时没有等）。

## 🚀 快速开始

### 1. 访问页面
- **URL**: `http://localhost:3000/admin/prompt-test`
- **权限**: 需要管理员权限
- **导航**: 侧边栏 → 提示词测试 (TestTube图标)

### 2. 基本操作流程
1. 选择测试类型（文章分析 / 语法分析）
2. 输入测试数据或加载示例数据
3. 执行单次测试或批量测试
4. 查看结果分析和问题报告

## 📋 详细功能说明

### 测试类型

#### 🗞️ 文章分析测试
- **标题**: 必填，输入日语文章标题
- **副标题**: 可选，输入日语副标题
- **内容**: 可选，输入日语文章内容（支持HTML格式）

**示例数据**:
```
标题: 日本の経済成長が加速
副标题: 新技術の導入により生産性が向上
内容: <h3>技術革新の影響</h3><p>日本の製造業では、AI技術の導入により生産性が大幅に向上している。特に自動車産業において、その効果が顕著に現れている。</p><p>専門家によると、この傾向は今後も続くと予想される。</p>
```

#### 📝 语法分析测试
- **文本**: 必填，输入要分析语法的日语文本

**示例数据**:
```
新しい技術について説明します。この方法を使うことができれば、効率が向上するでしょう。
```

### 测试执行方式

#### 🎯 单次测试
- 点击"单次测试"按钮
- 执行一次AI分析
- 查看即时结果和处理时间

#### 📊 批量测试
- 设置测试次数（2-10次）
- 点击"批量测试"按钮
- 连续执行多次测试验证稳定性
- 自动添加1秒延迟避免API限制

### 结果分析

#### 📈 统计指标
- **总测试次数**: 执行的测试总数
- **成功率**: 成功测试的百分比
- **平均耗时**: 所有测试的平均处理时间
- **发现问题**: 检测到的一致性和格式问题数量

#### 🔍 一致性检查
- **词汇数量变化**: 检查不同测试中词汇数量的差异（阈值：>3个）
- **语法点数量变化**: 检查语法点数量的变化（阈值：>2个）
- **输出稳定性**: 评估多次测试结果的一致性

#### ✅ 格式验证
- **振り仮名完整性**: 检查是否为所有汉字添加了振り仮名
- **例句数量**: 验证词汇和语法的例句是否为3个
- **必填字段**: 检查关键字段是否存在

## 🛠️ 使用场景

### 1. 提示词优化验证
```
步骤：
1. 使用相同输入进行优化前测试
2. 修改提示词内容
3. 使用相同输入进行优化后测试
4. 对比分析结果的改进情况
```

### 2. 稳定性评估
```
步骤：
1. 选择标准测试数据
2. 执行批量测试（5-10次）
3. 查看一致性分析报告
4. 根据问题调整提示词
```

### 3. 格式规范检查
```
检查项目：
- 振り仮名是否完整
- 例句数量是否正确（3个）
- 输出格式是否符合要求
```

## 📊 结果解读

### 成功率指标
- **>95%**: 系统稳定，提示词效果良好 ✅
- **85-95%**: 可接受范围，可考虑优化 ⚠️
- **<85%**: 需要立即检查和修复 ❌

### 常见问题类型
1. **一致性问题**: 输出内容在不同测试间变化较大
2. **格式问题**: 输出格式不符合预期要求
3. **性能问题**: 处理时间过长或不稳定

## 🔧 故障排除

### 测试失败
- 检查网络连接
- 验证API密钥配置
- 确认服务器运行状态

### 结果不一致
- 检查提示词的明确性
- 验证输入数据的完整性
- 考虑调整提示词内容

### 性能问题
- 检查API配额使用情况
- 考虑优化输入长度
- 评估模型选择

## 💡 最佳实践

### 测试策略
1. **建立基准**: 使用标准示例数据建立基准测试
2. **定期检查**: 定期运行测试监控系统状态
3. **版本对比**: 提示词修改前后进行对比测试
4. **批量验证**: 重要功能使用批量测试验证

### 数据管理
- 使用一致的测试数据
- 保存重要的测试结果
- 记录问题和解决方案

### 持续改进
- 根据测试结果优化提示词
- 监控长期趋势变化
- 收集用户反馈

## 🔗 相关资源

- **API文档**: `/api/ai/test/analyze-article` 和 `/api/ai/test/analyze-grammar`
- **提示词管理**: 数据库中的 `ai_prompts` 表
- **测试脚本**: `test-prompt-api.js`

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查服务器日志
3. 验证API密钥和配置
4. 参考故障排除指南

通过合理使用这个测试工具，可以有效提升AI分析功能的质量和稳定性！
