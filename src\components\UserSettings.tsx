'use client';
import React, { useState, useEffect } from 'react';
import { Save, RefreshCw, User, Settings, Loader2 } from 'lucide-react';
import { getUserSettingsAction, setUserSettingAction } from '@/app/actions';

interface UserSettingsProps {
  userId: string;
}

interface UserSettingsData {
  news_page_size: string;
  vocabulary_page_size: string;
  grammar_page_size: string;
  study_cards_per_session: string;
  study_vocabulary_per_session: string;
  study_grammar_per_session: string;
  auto_play_audio: string;
  show_furigana: string;
  theme_preference: string;
}

const DEFAULT_SETTINGS: UserSettingsData = {
  news_page_size: '10',
  vocabulary_page_size: '20',
  grammar_page_size: '15',
  study_cards_per_session: '10',
  study_vocabulary_per_session: '8',
  study_grammar_per_session: '5',
  auto_play_audio: 'true',
  show_furigana: 'true',
  theme_preference: 'light'
};

const UserSettings: React.FC<UserSettingsProps> = ({ userId }) => {
  const [settings, setSettings] = useState<UserSettingsData>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadSettings();
  }, [userId]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const result = await getUserSettingsAction(userId);
      if (result.success) {
        // 合并默认设置和用户设置
        const userSettings = { ...DEFAULT_SETTINGS, ...result.data };
        setSettings(userSettings);
      }
    } catch (error) {
      console.error('Failed to load user settings:', error);
      showMessage('error', '加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key: keyof UserSettingsData, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSetting = async (key: keyof UserSettingsData, value: string) => {
    try {
      setSaving(true);
      const result = await setUserSettingAction(userId, key, value);
      if (result.success) {
        showMessage('success', '设置已保存');
      } else {
        showMessage('error', result.error || '保存失败');
      }
    } catch (error) {
      showMessage('error', '保存设置失败');
    } finally {
      setSaving(false);
    }
  };

  const saveAllSettings = async () => {
    try {
      setSaving(true);
      const promises = Object.entries(settings).map(([key, value]) =>
        setUserSettingAction(userId, key, value)
      );
      
      const results = await Promise.all(promises);
      const hasError = results.some(result => !result.success);
      
      if (hasError) {
        showMessage('error', '部分设置保存失败');
      } else {
        showMessage('success', '所有设置已保存');
      }
    } catch (error) {
      showMessage('error', '保存设置失败');
    } finally {
      setSaving(false);
    }
  };

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 3000);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-indigo-600 mr-2" />
          <span className="text-gray-600">加载设置中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center">
              <User className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">个人设置</h3>
              <p className="text-sm text-gray-600">自定义您的学习偏好和界面设置</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadSettings}
              disabled={loading}
              className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
            <button
              onClick={saveAllSettings}
              disabled={saving}
              className="px-4 py-2 text-sm bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>保存所有</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {message.text}
          </div>
        )}

        <div className="space-y-6">
          {/* 分页设置 */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">分页设置</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  新闻列表每页显示数量
                </label>
                <p className="text-xs text-gray-500 mb-2">设置新闻一览页面每页显示的文章数量（范围 5-50）</p>
                <input
                  type="number"
                  value={settings.news_page_size}
                  onChange={(e) => handleSettingChange('news_page_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  min="5"
                  max="50"
                  placeholder="10"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  生词本每页显示数量
                </label>
                <p className="text-xs text-gray-500 mb-2">设置生词本页面每页显示的词汇数量（范围 10-100）</p>
                <input
                  type="number"
                  value={settings.vocabulary_page_size}
                  onChange={(e) => handleSettingChange('vocabulary_page_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  min="10"
                  max="100"
                  placeholder="20"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语法本每页显示数量
                </label>
                <p className="text-xs text-gray-500 mb-2">设置语法本页面每页显示的语法点数量（范围 10-100）</p>
                <input
                  type="number"
                  value={settings.grammar_page_size}
                  onChange={(e) => handleSettingChange('grammar_page_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  min="10"
                  max="100"
                  placeholder="15"
                />
              </div>
            </div>
          </div>

          {/* 学习设置 */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">学习设置</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  每次学习词汇数量
                </label>
                <select
                  value={settings.study_vocabulary_per_session}
                  onChange={(e) => handleSettingChange('study_vocabulary_per_session', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="3">3 个</option>
                  <option value="5">5 个</option>
                  <option value="8">8 个</option>
                  <option value="10">10 个</option>
                  <option value="15">15 个</option>
                  <option value="20">20 个</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  每次学习语法数量
                </label>
                <select
                  value={settings.study_grammar_per_session}
                  onChange={(e) => handleSettingChange('study_grammar_per_session', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="2">2 个</option>
                  <option value="3">3 个</option>
                  <option value="5">5 个</option>
                  <option value="8">8 个</option>
                  <option value="10">10 个</option>
                  <option value="12">12 个</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  混合模式总数量 <span className="text-xs text-gray-500">(兼容)</span>
                </label>
                <select
                  value={settings.study_cards_per_session}
                  onChange={(e) => handleSettingChange('study_cards_per_session', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="5">5 张</option>
                  <option value="10">10 张</option>
                  <option value="15">15 张</option>
                  <option value="20">20 张</option>
                  <option value="30">30 张</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  主题偏好
                </label>
                <select
                  value={settings.theme_preference}
                  onChange={(e) => handleSettingChange('theme_preference', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="light">浅色主题</option>
                  <option value="dark">深色主题</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>
            </div>
          </div>

          {/* 显示设置 */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">显示设置</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">自动播放音频</label>
                  <p className="text-xs text-gray-500">在词汇详情页面自动播放发音</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.auto_play_audio === 'true'}
                    onChange={(e) => handleSettingChange('auto_play_audio', e.target.checked ? 'true' : 'false')}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">显示假名注音</label>
                  <p className="text-xs text-gray-500">在汉字上方显示假名读音</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.show_furigana === 'true'}
                    onChange={(e) => handleSettingChange('show_furigana', e.target.checked ? 'true' : 'false')}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSettings;
