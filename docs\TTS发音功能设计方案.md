
# TTS发音功能设计方案

## 1. 需求背景

目前新闻阅读器中的“朗读”按钮是一个占位符，无法实际发音。为了提升用户的日语听力练习体验，需要为其实现真正的文本转语音（TTS）功能。

同时，考虑到未来可能会引入更高质量的AI生成音频，本次设计需要具备良好的扩展性，能够平滑地支持多种音频源。

## 2. 核心设计思想

- **双源切换 (Dual Source Switching)**：系统必须能够处理两种不同的音频源，并允许用户在未来进行选择。
  - **浏览器TTS (Browser TTS)**: 使用浏览器内置的`window.speechSynthesis` API进行实时、客户端的语音合成。
    - **优点**: 无需API成本，即时响应，可离线工作。
    - **缺点**: 音质和音色因浏览器和操作系统而异，可能听起来比较机械。
  - **AI生成音频 (AI-Generated Audio)**: 播放由服务器端AI模型预先生成的高质量音频文件（如MP3/WAV）。
    - **优点**: 音色自然、统一，发音质量高。
    - **缺点**: 需要API调用成本，生成有延迟，需要额外的存储空间。

- **用户可控 (User Control)**：未来可以在设置页面中让用户选择首选的音频源。

- **优雅降级 (Graceful Degradation)**：当首选的音频源（如AI音频）不可用时，系统应能自动无缝地切换到备用方案（浏览器TTS）。

- **逻辑解耦 (Logic Decoupling)**：将TTS的实现逻辑封装在一个独立的自定义Hook中，使UI组件与具体的发音技术分离。

## 3. 数据库设计变更 (未来扩展)

为了支持未来的AI生成音频功能，现有的`articles`表已经包含了所需字段，无需立即修改，但需明确其用途：

- **`audio_url`**: 用于存储指向AI生成的音频文件的远程URL。
- **`audio_path`**: 用于存储音频文件下载到本地后的路径。
- **`system_settings` 表 (未来新增)**: 可以增加一条记录如 `(key: 'tts_preferred_source', value: 'browser')`，用于存储用户的偏好设置。

## 4. 前端实现策略 (分阶段实施)

### 第一阶段：实现浏览器TTS (当前任务)

1.  **创建 `useTTS` 自定义Hook (`src/hooks/useTTS.ts`)**:
    -   **职责**: 封装所有与浏览器`speechSynthesis`相关的逻辑。
    -   **状态管理**:
        -   `isSupported`: 布尔值，判断当前浏览器是否支持TTS。
        -   `isPlaying`: 布尔值，表示当前是否正在播放。
    -   **暴露函数**:
        -   `speak(text, lang)`: 接收文本和语言代码，开始朗读。
        -   `cancel()`: 立即停止并清除所有朗读任务。
    -   **事件处理**: 内部处理`onstart`, `onend`, `onerror`等事件，以正确更新`isPlaying`状态。
    -   **自动清理**: 利用`useEffect`的清理函数，在组件卸载时自动调用`cancel()`，防止声音泄露。
    -   **语音选择**: 尝试查找并使用设备上可用的日语（`ja-JP`）语音包，以提高发音准确性。

2.  **集成到 `ArticleReader.tsx` 组件**:
    -   在组件中调用`useTTS()` Hook，获取`isPlaying`, `speak`, `cancel`等状态和方法。
    -   修改“朗读”按钮的`onClick`事件：
        -   如果`isPlaying`为`true`，则调用`cancel()`停止播放。
        -   如果`isPlaying`为`false`，则调用`speak()`开始播放。播放的文本应智能地拼接文章的标题、概要和正文。
    -   根据`isPlaying`状态，动态地将按钮图标在“播放” (`<Play>`) 和“暂停/停止” (`<Pause>`) 之间切换。
    -   在组件卸载或文章ID变化时，调用`cancel()`函数，确保切换文章时旧的朗读任务会停止。

### 第二阶段：支持AI生成音频 (未来计划)

1.  **扩展 `useTTS` Hook**:
    -   `speak`函数将被重构。它会接收一个包含多种源的对象，例如 `{ text: '...', audioUrl: '/path/to/audio.mp3' }`。
    -   函数逻辑会变为：
        -   首先检查用户的偏好设置以及`audioUrl`是否存在且有效。
        -   如果条件满足，则创建一个HTML `<audio>`元素进行播放，并管理其播放状态。
        -   如果条件不满足，则优雅地降级，调用现有的浏览器`speechSynthesis`来朗读`text`内容。

2.  **后端支持**:
    -   需要创建一个Genkit Flow，接收文章ID或文本，调用TTS模型生成音频，并将其保存到`public/media/audios`目录，最后将路径更新到数据库的`audio_path`字段。

3.  **UI调整**:
    -   可能需要在文章阅读器或抓取管理页面添加一个“生成高质量音频”的按钮来触发后端的AI音频生成任务。

## 5. 结论

通过这种分阶段、可扩展的设计，我们可以在当前版本快速、低成本地实现核心的朗读功能，同时为未来无缝升级到更高质量的AI音频方案奠定了坚实的基础。将核心逻辑封装在`useTTS` Hook中是本次设计的关键，它保证了代码的整洁、可维护性和高内聚性。
