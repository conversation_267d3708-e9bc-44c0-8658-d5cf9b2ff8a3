import { NextResponse } from 'next/server';
import { dbManager } from '@/lib/server/database';

export async function GET() {
  try {
    const dbStats = await dbManager.getDatabaseStatsWithMedia();
    
    return NextResponse.json({
      success: true,
      data: dbStats
    });
  } catch (error: any) {
    console.error('获取数据库统计失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
